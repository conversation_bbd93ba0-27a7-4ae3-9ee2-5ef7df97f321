import React, { ReactNode } from "react";
import { ColorsType } from "../../themes";
import { Modal, ModalWidthType } from "../Modal";
import { Heading } from "../Heading";
import { Button } from "../Button";
import { Stack } from "../Stack";
import { Spinner } from "../Spinner";
import { Overlay } from "../Overlay";

export interface ModalConfirmProps {
  children?: React.ReactNode;
  show?: boolean;
  onClose?: () => void;
  modalHeader: ReactNode;
  modalBody: ReactNode;
  cancelText: ReactNode;
  confirmText: ReactNode;
  loading: boolean;
  modalWidth?: ModalWidthType;
  onConfirm?: () => void;
  onCancel?: () => void;
  color?: ColorsType;
  confirmDisabled?: boolean;
  closeAfterConfirm?: boolean;
}

const ModalConfirm: React.FC<ModalConfirmProps> = ({
  children,
  show = false,
  onClose,
  modalHeader,
  modalBody,
  cancelText,
  confirmText,
  loading = false,
  modalWidth = "small",
  onConfirm,
  onCancel,
  color = "danger",
  confirmDisabled = false,
  closeAfterConfirm = true,
}) => {
  const [openModal, setOpenModal] = React.useState(false);
  const isMountedRef = React.useRef(false);

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    if (onClose) {
      onClose();
    }
    setOpenModal(false);
  };

  React.useEffect(() => {
    setOpenModal(show);
  }, [show]);

  React.useEffect(() => {
    if (loading) {
      isMountedRef.current = true;
    } else if (isMountedRef.current && closeAfterConfirm) {
      isMountedRef.current = false;
      setOpenModal(false);
      if (onClose) {
        onClose();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading]);

  let OpenButton: React.ReactElement | null = null;
  if (children) {
    OpenButton = React.cloneElement(children as React.ReactElement, {
      onClick: () => {
        setOpenModal(true);
      },
    });
  }

  return (
    <>
      {OpenButton}

      <Modal
        show={openModal}
        onClose={handleCancel}
        modalWidth={modalWidth}
        closeOnEscape={false}
      >
        {loading && (
          <Overlay>
            <Spinner />
          </Overlay>
        )}
        <Modal.Header>
          <Heading>{modalHeader}</Heading>
        </Modal.Header>
        <Modal.Body>{modalBody}</Modal.Body>
        <Modal.Footer>
          <Stack direction="row" justify="between">
            <Button
              dataTest="button-cancel"
              compact
              plain
              onClick={handleCancel}
              color={color}
            >
              {cancelText}
            </Button>
            <Button
              dataTest="button-confirm"
              color="danger"
              onClick={onConfirm}
              disabled={confirmDisabled}
            >
              {confirmText}
            </Button>
          </Stack>
        </Modal.Footer>
      </Modal>
    </>
  );
};

ModalConfirm.displayName = "ModalConfirm";

export { ModalConfirm };
