import React, { useState } from "react";
import { Stack } from "../Stack";
import { InputNumber } from "./InputNumber";
import { Field, Form, FormSubmit } from "../Form";

import { InputField } from "../InputField";

export default {
  title: "Components/Form/InputNumber ",
  component: InputNumber,
};

export const Playground = () => {
  const [value, setValue] = useState(0);

  return (
    <Stack direction="column">
      <InputField
        type="number"
        value={value}
        onChange={(value) => setValue(+value)}
      />

      {
        // eslint-disable-next-line no-console
        <Form<{ number: number }> onSubmit={(data) => console.log(data)}>
          {({ formProps }) => (
            <form {...formProps}>
              <Field name="number" defaultValue={10}>
                {({ fieldProps }) => (
                  <InputField
                    width="200px"
                    type="number"
                    noFraction
                    {...fieldProps}
                  />
                )}
              </Field>
              <FormSubmit>Submit</FormSubmit>
            </form>
          )}
        </Form>
      }
    </Stack>
  );
};
