import React from "react";
import { base } from "../themes/original";
import { DataTable } from "../components/DataTable";
import { Box } from "../components/Box";

function Display({ boxShadow }) {
  return <Box boxShadow={boxShadow}>Quilt is design system for Wuilt</Box>;
}

export const BoxShadows = () => {
  return (
    <Box margin="0 auto" maxWidth="1000px">
      <DataTable
        header={{
          cells: [
            { content: "Box Shadow" },
            { content: "Value" },
            { content: "Display" },
          ],
        }}
        rows={Object.keys(base.boxShadow).map((f) => ({
          cells: [
            { content: f },
            { content: base.boxShadow[f] },
            { content: <Display boxShadow={f} /> },
          ],
        }))}
      />
    </Box>
  );
};

BoxShadows.title = "Intro/Guide";

BoxShadows.story = {
  name: "Box Shadows",
};

export default BoxShadows;
