import React, { ReactElement, useState } from "react";
import styled from "styled-components";
import { Form, Field } from "../Form";
import { InputField } from "../InputField";
import { TextArea } from "../TextArea";
import { Button } from "../Button";
import { Modal, ModalWidthType } from "../Modal";
import { Heading } from "../Heading";

import { ChevronRightIcon } from "../icons";
import { Stack } from "../Stack";
import { Label } from "../Label";

const InputTypeObj = {
  input: InputField,
  textArea: TextArea,
};

enum ModeTypeEnum {
  Edit,
  Add,
}

type ModeType = keyof typeof ModeTypeEnum;

export type ElementsType = {
  tagName: any;
  fieldName: string;
  fieldLabel: string;
  baseLangValue: string;
  transLangValue?: string;
}[];

export interface ModalTranslateProps {
  children?: React.ReactNode;
  modalHeader: string;
  Elements: ElementsType;
  TranslateFrom: string;
  TranslateTo: string;
  mode?: ModeType;
  saveText?: string;
  cancelText?: string;
  modalWidth?: ModalWidthType;
  onSave: (data: any) => void;
}

const ModalTranslate: React.FC<ModalTranslateProps> = ({
  children,
  modalHeader,
  Elements,
  TranslateFrom,
  TranslateTo,
  mode = "Add",
  saveText,
  cancelText,
  modalWidth = "large",
  onSave,
}) => {
  const [openModal, setOpenModal] = useState(false);

  // @ts-ignore
  Elements = [
    ...Elements.map((element) => ({
      ...element,
      tagName: InputTypeObj[element.tagName],
    })),
  ];

  const ModalBody = (
    <div>
      <Stack
        direction="row"
        justify="between"
        align="center"
        spacing="none"
        spaceAfter="large"
      >
        <StyledLangName>{TranslateFrom}</StyledLangName>
        <Stack basis="20%" align="center" justify="center">
          <ChevronRightIcon reverseOnRtl />
        </Stack>
        <StyledLangName>{TranslateTo}</StyledLangName>
      </Stack>

      {Elements.map((Element, index) => {
        const FieldInputElement = Element.tagName;
        return (
          <Stack
            key={index}
            direction="row"
            justify="between"
            align="center"
            spacing="none"
          >
            <div style={{ flexBasis: "40%", marginBottom: "20px" }}>
              <Label>{Element.fieldLabel}</Label>

              {/* @ts-ignore */}
              <FieldInputElement value={Element.baseLangValue} disabled />
            </div>

            <Stack basis="20%" align="center" justify="center">
              <ChevronRightIcon reverseOnRtl />
            </Stack>

            <div style={{ flexBasis: "40%" }}>
              <Field
                name={Element.fieldName}
                label={`${Element.fieldLabel} ( ${TranslateTo} )`}
                defaultValue={mode === "Edit" ? Element.transLangValue : ""}
              >
                {({ fieldProps }) => <FieldInputElement {...fieldProps} />}
              </Field>
            </div>
          </Stack>
        );
      })}
    </div>
  );

  const OpenButton = React.cloneElement(children as ReactElement, {
    onClick: () => {
      setOpenModal(true);
    },
  });

  return (
    <>
      {OpenButton}

      <Modal
        show={openModal}
        onClose={() => setOpenModal(false)}
        modalWidth={modalWidth}
      >
        <Form
          onSubmit={(data) => {
            onSave(data);
          }}
        >
          {({ formProps }) => (
            <form {...formProps}>
              <Modal.Header>
                <Heading>{modalHeader}</Heading>
              </Modal.Header>
              <Modal.Body>{ModalBody}</Modal.Body>
              <Modal.Footer>
                <Stack direction="row" justify="between">
                  <Button color="danger" onClick={() => setOpenModal(false)}>
                    {cancelText}
                  </Button>
                  <Button submit> {saveText} </Button>
                </Stack>
              </Modal.Footer>
            </form>
          )}
        </Form>
      </Modal>
    </>
  );
};

export { ModalTranslate };

/**
 *
 *
 * Styles
 *
 *
 */

const StyledLangName = styled.div`
  background-color: ${({ theme }) => theme.palette.cloud.dark};
  color: ${({ theme }) => theme.palette.ink.normal};
  font-size: ${({ theme }) => theme.base.fontSize.md};
  text-align: center;
  padding: ${({ theme }) => theme.base.space.xs};
  flex-basis: 40%;
  font-weight: ${({ theme }) => theme.base.fontWeight.semiBold};
`;
