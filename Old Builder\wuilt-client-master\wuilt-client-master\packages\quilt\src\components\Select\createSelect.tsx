import React, { useRef } from "react";
import { mergeStyles, components } from "react-select";
import { useTheme } from "styled-components";
import { SelectProps } from "./types";
import baseStyles from "./styles";
import { GeneralInputWrapper } from "../InputField/InputField";
import { Global } from "../../common/types";
import ReactDOM from "react-dom";

type SelectComponentProps<Option> = Global &
  React.PropsWithChildren<SelectProps<Option>>;

const PortalMenu = ({ children, ...props }) => {
  console.log(props, props.getStyles());
  const dropdownRoot = window.top?.document.body || window.document.body;
  const Menu = components.Menu as any;
  if (!dropdownRoot) {
    return null;
  }

  return ReactDOM.createPortal(
    <Menu {...props}>{children}</Menu>,
    dropdownRoot
  );
};

export function createSelect(WrappedComponent: React.ComponentType<any>) {
  const Select = <Option,>(props: SelectComponentProps<Option | null>) => {
    const {
      styles,
      validationState,
      spacing,
      isMulti,
      bgColor,
      isError,
      dataTest,
      components: componentsProp,
      ...restProps
    } = props;
    const ref = useRef<HTMLDivElement>(null);
    const theme = useTheme();
    const isCompact = spacing === "compact";
    return (
      <GeneralInputWrapper
        data-test={dataTest}
        isError={isError}
        ref={ref}
        style={{ padding: "0" }}
      >
        <WrappedComponent
          isMulti={isMulti}
          menuPlacement="auto"
          components={{
            ...componentsProp,
            Option: addDataTest(components.Option),
          }}
          styles={mergeStyles(
            baseStyles(validationState, isCompact, theme, bgColor),
            styles as any
          )}
          menuPosition="fixed"
          menuPortalTarget={
            // (typeof window !== "undefined" && window.top?.document.body) || // TODO: revert iframe portals
            typeof document !== "undefined" && document?.body
          }
          {...restProps}
        />
      </GeneralInputWrapper>
    );
  };
  return Select;
}

const addDataTest = (Component) => (props) => {
  const dataTest =
    props?.data?.dataTest || `select-option-${props?.data?.value}`;
  return (
    <Component
      {...props}
      innerProps={Object.assign(props.innerProps, {
        "data-test": dataTest,
      })}
    />
  );
};
