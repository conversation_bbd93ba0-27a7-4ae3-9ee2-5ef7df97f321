import {
  createContext,
  FC,
  ReactElement,
  useContext,
  useEffect,
  useMemo,
} from "react";
import BrowserInteractionTime from "browser-interaction-time";
import { createAmplitudeProvider } from "./amplitude";
import { Attributes } from "./types";
import { createDatadogRumProvider } from "./datadog-rum";
import { createHubspotProvider } from "./hubspot";
import { createSegmentProvider } from "./segments";
import {
  createPostHogProvider,
  PostHogProvider,
  posthogClient,
} from "./posthog";
import { createSentryProvider } from "./sentry";

interface TrackingContextType {
  setContext: (key: string, attributes?: Attributes) => void;
  pushError: (error: any, context?: Attributes) => void;
  pushEvent: (name: string, attributes?: Attributes) => void;
  view: (name: string, properties?: Attributes) => void;
}

const noopTracking = {
  setContext: () => undefined,
  pushError: () => undefined,
  pushEvent: () => undefined,
  view: () => undefined,
};
export let trackingMethods: TrackingContextType = noopTracking;

export const TrackingContext =
  createContext<TrackingContextType>(trackingMethods);

interface TrackingProviderProps {
  user?: {
    id: string;
    email: string;
    name: string;
    attributes?: Attributes;
  };
  disable?: boolean;
  config: {
    amplitude?: {
      apiKey: string;
    };
    segment?: {
      writeKey: string;
    };
    hubspot?: {
      portalId: string;
    };
    datadogRum?: {
      applicationId: string;
      clientToken: string;
      site: string;
      service: string;
      env: string;
      version: string;
    };
    posthog?: {
      token: string;
      apiHost: string;
    };
    sentry?: {
      dsn: string;
    };
  };
  children: ReactElement;
}

export const TrackingProvider: FC<TrackingProviderProps> = ({
  config: { amplitude, segment, hubspot, datadogRum, posthog, sentry },
  user,
  children,
  disable = false,
}) => {
  const isEnabled = !disable;
  const value = useMemo(() => {
    const providers: {
      [key: string]: TrackingContextType;
    } = {};
    if (!isEnabled) {
      return noopTracking;
    }
    if (amplitude?.apiKey) {
      providers.amplitude = createAmplitudeProvider({
        user,
        config: amplitude,
      });
    }
    if (segment?.writeKey) {
      providers.segment = createSegmentProvider({
        user,
        config: segment,
      });
    }
    if (hubspot?.portalId) {
      providers.hubspot = createHubspotProvider({
        user,
        config: hubspot,
      });
    }
    if (datadogRum?.applicationId) {
      providers.datadogRum = createDatadogRumProvider({
        user,
        config: datadogRum,
      });
    }
    if (posthog?.token) {
      providers.datadogRum = createPostHogProvider({
        user,
        config: posthog,
      });
    }
    if (sentry?.dsn) {
      providers.sentry = createSentryProvider({
        user,
        dsn: sentry?.dsn,
      });
    }
    const tracking = {
      setContext: (name: string, context?: Attributes) => {
        Object.values(providers).forEach((provider) => {
          provider.setContext(name, context);
        });
      },
      pushError: (error, context?: Attributes) => {
        Object.values(providers).forEach((provider) => {
          provider.pushError(error, context);
        });
      },
      pushEvent: (name: string, attributes?: Attributes) => {
        Object.values(providers).forEach((provider) => {
          provider.pushEvent(name, attributes);
        });
      },
      view: (name: string, attributes?: Attributes) => {
        Object.values(providers).forEach((provider) => {
          provider.view(name, attributes);
        });
      },
    };
    trackingMethods = tracking;
    return tracking;
  }, [
    isEnabled,
    amplitude,
    segment,
    hubspot,
    datadogRum,
    posthog,
    sentry,
    user,
  ]);

  const innerJsx = posthog?.token ? (
    <PostHogProvider client={posthogClient}>{children}</PostHogProvider>
  ) : (
    children
  );

  return (
    <TrackingContext.Provider value={value}>
      {innerJsx}
    </TrackingContext.Provider>
  );
};

export const useTracking = () => {
  const context = useContext(TrackingContext);
  return context;
};

interface AnalyticsViewProps {
  name: string;
  attributes?: Attributes;
  children: ReactElement;
}

export const AnalyticsView: FC<AnalyticsViewProps> = ({
  name,
  attributes,
  children,
}) => {
  const context = useContext(TrackingContext);

  useEffect(() => {
    context.view(name, attributes);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useInteractionTime((milliseconds) => {
    context.pushEvent("View - End", {
      ...attributes,
      name,
      activeTimeInSeconds: Math.floor(milliseconds / 1000),
    });
  });

  return children;
};

export const useInteractionTime = (
  callback: (millisecondsElapsed: number) => void
) => {
  useEffect(() => {
    const browserInteractionTime = new BrowserInteractionTime({
      idleTimeoutMs: 3000,
    });

    browserInteractionTime.startTimer();

    return () => {
      browserInteractionTime.stopTimer();
      callback(browserInteractionTime.getTimeInMilliseconds());
    };
  }, [callback]);
};
