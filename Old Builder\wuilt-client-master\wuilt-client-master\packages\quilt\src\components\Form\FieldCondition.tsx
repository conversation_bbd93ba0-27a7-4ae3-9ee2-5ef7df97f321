import React, { useContext } from "react";
import { getIn } from "final-form";
import { Field } from "./Field";
import { FormStateContext } from "./Form";

export interface FieldConditionProps {
  children?: React.ReactNode;
  when: string;
  equal: any;
  defaultValue: any;
}

const FieldCondition: React.FC<FieldConditionProps> = ({
  children,
  when,
  equal,
  defaultValue,
}) => {
  const { initialValues } = useContext(FormStateContext);
  const initialValue = getIn(initialValues, when);

  if (Object.keys(initialValues).length === 0) {
    return null;
  }

  return (
    <Field
      name={when}
      margin="0"
      defaultValue={defaultValue ?? initialValue}
      hideErrorMessage
      isError={false}
    >
      {({ fieldProps: { value } }) => (value === equal ? children : null)}
    </Field>
  );
};

FieldCondition.displayName = "FieldCondition";
export { FieldCondition };
