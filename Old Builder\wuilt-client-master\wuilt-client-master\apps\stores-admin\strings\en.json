{"++S6xx": "Click on <b>Create</b> to create a new API Key", "+/lYSm": "Phone number only", "+0zv6g": "Image", "+1WwYY": "Where can I get the {secret<PERSON>eyFieldLabel}?", "+2dhVA": "Enter the OTP generated from Fatoora portal", "+3bzMa": "How to add a simple product to your store?", "+3zeQW": "Delete Section", "+54WpT": "Here’s what’s happening with your store in <b>the last 7 days.</b>", "+7BzYr": "Multiple Choice", "+7uACJ": "Orders by date", "+8ZyPl": "Yes, Required", "+8zynv": "emad", "+9Pj52": "Pickup location address is required", "+AtaGy": "Product general settings", "+Czd0x": "Arrived destination", "+DL/5j": "In transit ready", "+EZQ3i": "Enabling guest review will allow any logged in user to submit a review on your products, not necessarily have purchased the product", "+FnBeB": "<PERSON><PERSON><PERSON>", "+G52cO": "<PERSON><PERSON><PERSON>", "+Grcse": "<b>1</b> Online Payment integrations", "+HMSZw": "Enter amount", "+Jbqk+": "Edit Products row", "+LB3sK": "Qesm Al Qosimah", "+O3Jhw": "Please edit the quantity of this product, you can only add up to <b> {availableQuantity} per order </b>", "+PR8VI": "No. of products to display", "+TUaIX": "Option deleted!", "+TVvN8": "Select at least one collection.", "+U6ozc": "Type", "+ULc6a": "Sending email canceled", "+VpITS": "Orders archived successfully", "+W13O7": "Delivery min cycle (North Coast)", "+a0fTT": "Arab Banking Corporation - Egypt S.A.E (ABC)", "+b2tpb": "Card name is required", "+bRxXe": "Please select an element", "+eVaYg": "Failed payout", "+hsN28": "Shipping Status", "+iNxUQ": "Search abandoned checkouts", "+iZ00L": "New URL is required", "+jSzcP": "{count} Cities", "+jhHPF": "{baseCostWeight}Kg", "+l9UyV": "Payment Settings", "+opGhO": "Payout status", "+p1C40": "• Don’t forget to set them to Active once you finish editing them", "+qv/av": "RATING", "+raXew": "Staff Accounts", "+sV2Nf": "J&T", "+v5e55": "Show Items", "+vVZ/G": "Connect", "+xTpT1": "Attributes", "+yzUgz": "Street, neighborhood, area", "+zGn1g": "Faisal Islamic Bank of Egypt (FAIB)", "+zZekV": "No Pages", "/+H2am": "First & Last name", "/+LHBr": "Estimated taxes", "/+ZJGZ": "Payout settings updated successfully", "//TdcF": "Disabling product reviews will remove the the reviews and the ability to review products on your store", "/0/dgI": "Confirmed return", "/02Sc2": "Product description is too large", "/06iwc": "Large", "/07zy4": "ElHalfayah Spring", "/0TOL5": "Amount", "/0yBN9": "Chat & Phone Support", "/1vMUa": "Set end date", "/3UMxU": "Sort Products", "/4tOwT": "<PERSON><PERSON>", "/6IZbE": "Customers will see this at checkout.", "/7EsPT": "Write your address here", "/8dO08": "Stock update failed", "/9WTfl": "Discount percentage", "/ArBgP": "Receiver Name", "/BgyNa": "Account number", "/CkN2H": "Cancel withdraw request", "/CwVl6": "Fire Extinguisher", "/EJ2K7": "Please enter a valid postal code", "/GATJs": "LOOK & FEEL", "/Hp/q0": "Track shipment", "/JAKv0": "Edit Collections row", "/KD9EM": "<PERSON><PERSON><PERSON><PERSON>", "/POa1m": "Review published successfully", "/QgnNh": "E-mail & Phone number", "/R6mb8": "Enable guest reviews", "/S1vsR": "Mobile Wallet", "/VnDMl": "Other", "/W0Cz/": "How to use store analytics?", "/WMCDd": "Something went wrong!", "/YBXaB": "It gives you a more professional look, can be used for online ads, and makes it easier for your customers to find you on Google.", "/YcbKD": "Arrived origin terminal", "/a8wHi": "This Field must be 5 digits", "/aBLH2": "Get started", "/aKx1G": "Buy X get Y", "/dzcE1": "External Link", "/h6vtI": "Create at least one category", "/idAUH": "Add Language", "/iniP3": "<PERSON><PERSON><PERSON>'naa'y", "/nuHkL": "Add a video input field by clicking the <b>add video</b> button.", "/owofd": "Add a note to your customers at the payment step in checkout", "/sWDkM": "Self-Defense Tools (Spray, Sticks,etc.)", "/t9a6u": "Custom amount", "/tcFiu": "<b>Action needed:</b> Orders that need delivery info edit from your end to be successfully delivered.", "/tmab5": "Max quantity exceeded", "/vRQhD": "New Capital", "/vyAxn": "Arab International Bank (ARIB)", "/wLKWU": "How to add and manage collections?", "/x6d15": "Failed to add item", "/xdCqj": "Payment Related", "/xsr4z": "ex. Color", "/yBziy": "Google Tag Manager ID is required", "/z24z5": "How to add a simple product?", "001snJ": "Edit Embed row", "03EQBE": "Pickup location name", "04uUWC": "ex. https://www.youtube.com/c/YOURSTORE", "06261/": "There are no automatic discounts added yet.", "06u3PQ": "ex. Sony, Samsung, Apple", "08Df8c": "Choose one store you’d like to upgrade to one of Wuilt Premium plans", "08NsuO": "<span>{name}</span> reactivate the order", "09JQKf": "Alex Bank Egypt (BOA)", "09yepu": "Collection URL", "0AWVJ9": "Add your payouts details.", "0Azlrb": "Manage", "0CKPSF": "ex. Our Featured Text", "0CwSTU": "100% of the order value", "0E47bk": "• {count} regions", "0F8HPi": "Customize store homepage", "0H0PZL": "Remaining amount", "0HT+Ib": "Archived", "0Hhe6f": "On hold", "0JMYBR": "Zone {index}", "0SJtU7": "Failed settlement", "0SYSz1": "Buy domain", "0TiaQL": "Products imported successfully", "0U3frR": "Withdraw amount must be at least 1", "0Urzaq": "EGP 7.21 (including VAT)", "0WE7Qw": "<PERSON><PERSON><PERSON>", "0WJNP/": "Return", "0Y3U5B": "<PERSON>", "0ZDdC4": "Material", "0aUn9q": "Shipping airway bill", "0b4NZL": "Enter custom state name", "0cqx5Z": "Not-Required", "0elHND": "Custom Cities", "0fQwJ/": "Enables direct tracking of server-to-server and offline events, ensuring comprehensive and accurate data collection.", "0h/lPM": "Upgrade", "0hF3WZ": "Return processing", "0hZMPw": "Tulip Hotel Suez road", "0iEb0f": "Video row", "0j6UB/": "Create a product", "0jVDeS": "Mantrac Equipment’s Filters", "0je0j1": "Cancelled successfully", "0k+OHd": "Agricultural Bank of Egypt (PDAC)", "0kCnKb": "Import Products", "0kDtdH": "Update item failed", "0kqaDB": "Staff Permissions", "0laLl6": "<PERSON><PERSON><PERSON>", "0m5Dz+": "Current theme", "0nYOo/": "Reconnect Now", "0niASN": "End Time", "0oL1zz": "Copied!", "0oTzsO": "Order setted as unfulfilled", "0omO22": "Review unpublished successfully", "0qoghq": "Are you sure you want to discard the changes?", "0u3s/t": "API Key Generator", "0vHrc4": "National Bank of Greece (NBG)", "0vL5u1": "Create an account", "0wWkhF": "Conversion rate is required", "0z0Sz3": "Min. per cart", "10nEe/": "All Fees are in USD and are exclusive of all taxes, levies, or duties imposed by taxing authorities, and you are responsible for payment thereof. For more information, refer to our", "10xL7/": "Total order discount", "11M4TG": "subdomain name is invalid", "11UKIB": "Product options values already exist", "15CNak": "Postal code is required", "16zObY": "Withdraw request cancelled", "172N3w": "Please wait...", "19VPIm": "Verify if you set it up correctly", "1BLo9o": "Add a primary button", "1D+G0r": "Missing header(s)", "1DoJSs": "Sitemap XML", "1EYCdR": "Tags", "1EcgBo": "SHIPPING INFO", "1FATAi": "Add another location for pickup.", "1H1Wva": "Add a note to your Email confirmation.", "1HbOPm": "ElAzima", "1Ia8qN": "Processing", "1KxtyA": "Your name is required", "1MjaF2": "Enables accurate tracking of user actions and conversions, improving ad targeting and performance insights", "1MqqGT": "Charge wallet", "1NBqKf": "Update bank info", "1OS4Ec": "City Level On Checkout", "1RMld+": "Taxpayer ID", "1SphRN": "Phone no.", "1VSsl7": "20 characters max", "1YWUxW": "Abis 07", "1Z8Ue4": "Last Order", "1Zx4GF": "Learn how to generate Fatoora Portal OTP", "1b+Ps1": "Account owner name", "1b1oS+": "el nagrayeen", "1c0N68": "Update store details, applies to new shipments only; contact support for past orders.", "1c7nUE": "Responsive layout Advanced customization options", "1cXqOj": "Success transaction", "1e+GCU": "Prohibited item", "1eZYt0": "You don't have sufficient balance to issue the shipping bill.", "1gGExl": "Old URL is required", "1hSGga": "Changing the order total will remove any manual applied discounts and replace the previous total.", "1iEPTM": "General", "1j/AQi": "This is your unique link to the XML file that should be imported into the {integrationName} account.", "1jgPTz": "<b>Unlimited</b> staff accounts", "1koBsW": "You haven’t added any banners", "1pO2Zo": "There are no coupon codes added yet.", "1pXahC": "<span>{name}</span> requested an airway bill", "1peLhr": "Orders reactivated successfully", "1rlBUx": "Unexpected error", "1sqSHb": "Ammunition Life & Sound.", "1t8Bbh": "Start date must be less than the end date", "1tQMdn": "large", "1v663u": "Secondary phone number", "1x/SNe": "Other Languages", "2/2yg+": "Add", "20EeV7": "Delivery min cycle (Alex, Suez Canal (Port Said, Ismailia, Suez))", "21SFhp": "Need attention", "21hYfd": "Packaging materials", "23wsFZ": "Sinai, New Valley, North Sinai, South Sinai and New Valley", "24TOSj": "Nasser Social Bank (NSB)", "24YhUc": "{items} Items", "24tAMi": "API Key Generated Successfully!", "26PhC/": "<PERSON><PERSON><PERSON>", "27BAVr": "Currency Change Confirmation", "28C0Aj": "Discount", "29B5ol": "PRODUCT PAGE", "29pssM": "Are you sure you want to leave this page?", "2A8czU": "Reset shipping rates to their original price? This action cannot be undone.", "2BtiQy": "Damaged", "2CiKVQ": "<PERSON><PERSON><PERSON>", "2GURQY": "Appearance", "2Gv9xR": "Allow customers to create reviews with star ratings of your products", "2IFy+5": "Awaiting your action", "2MOv6I": "Bahariya Oasis", "2NXRke": "<span>COD Transfer confirmed –</span> {translatedCurrency} {amount}transferred", "2O2sfp": "Finish", "2P2o/G": "Reach out to us!", "2Pk/1F": "IBAN number", "2SHvv6": "Experience the perfect blend of animation and modernity with our new store theme.", "2SUnzx": "<b>{count}</b> Unsuccessful", "2U+1EG": "<PERSON><PERSON><PERSON><PERSON>", "2U2FP9": "Today at", "2UI237": "<span>Fulfillment canceled – </span>The fulfillment was canceled", "2UcZ+c": "Fees cannot be higher than amount", "2WaMod": "You’re importing <b>{productsCount, plural, =0 {0 product} one {1 product} two {# products} few {# products} many {# products} other {# products}}</b> / <b>{variantsCount, plural, =0 {0 variant} one {1 variant} two {# variants} few {# variants} many {# variants} other {# variants}}</b> with a total of <b>{skuCount, plural, =0 {0 SKU} one {1 SKU} two {# SKUs} few {# SKUs} many {# SKUs} other {# SKUs}}</b> and <b>{imagesCount, plural, =0 {0 image} one {1 image} two {# images} few {# images} many {# images} other {# images}}</b>", "2X5VMq": "Enter the pickup location address", "2Xk3mG": "Reactivate order", "2YHKEN": "{count, plural, =0 {Zero Orders} one {One Order} two {Two Orders} few {# Orders} many {# Orders} other {# Orders}}", "2Yr2MB": "Final amount", "2aqv4G": "Updating the order will immediately affect the air waybill.", "2dc/1C": "Manual withdrawal", "2dsUA3": "Shipment in progress", "2ecZ2X": "Order setted as partially fulfilled", "2fqMCy": "<PERSON><PERSON><PERSON><PERSON>", "2hkR1B": "Fulfilled ({count})", "2jSEqT": "Secure payments", "2l2y5p": "Create Order", "2lNplQ": "Arrived destination terminal", "2lb4bZ": "Add video", "2n90Qm": "Airway bill requested", "2qRNcu": "Used for Buttons, Labels, Prices & Variants", "2rdLv9": "Tiktok", "2sLL6/": "Selected products", "2tLjNM": "Pickup location not registered at provider", "2tYwdR": "This controls which Klaviyo list will we add new customers to", "2ttp+W": "Using the Conversions API with a pixel improves targeting, optimization, and achieves an average 13% CPA improvement.", "2u/2SQ": "Select a value for this option", "2ubJiv": "Update currency", "2vj5he": "Oops, An Error occurred. this {integrationName} store is already attached with another store", "2x9S+8": "Alsaloom", "2yJzWP": "Inconsistent variation(s)", "2zAY1d": "FAQs", "2zEG6l": "Invalid value(s)", "307fnc": "Order sent to {provider}", "30RRsv": "Custom product", "30zBF1": "Send after", "31mT0J": "Order/shipment tracking number", "32Pu6k": "<b>{count}</b> Action needed", "33wtbb": "Add your first option", "34G0mF": "On going", "39PtLD": "Instagram", "39xhko": "(Length * Width * Height)cm/3000", "3AyEvo": "Payment updated! Order marked as partially paid. (EGP {paidAmount})", "3BneS3": "Include archived orders", "3D/kw9": "You haven't added a payout method yet.", "3EnruA": "Postal code", "3Gr7nP": "Hello, {name}", "3KB8YU": "Add Collection", "3LFZUM": "Alharagyah", "3M0IPm": "<PERSON>", "3N0aP9": "Add Shipping \"{amount} {currency}\" Discount to total Shipping value", "3NVyN7": "Please enter your first name", "3R7DLn": "Start gathering and showing customer reviews for your store products", "3Rt+4O": "You dont have any products yet", "3Rx6Qo": "Advanced", "3S33kj": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3SB7Bq": "Order is canceled", "3TtExR": "Protects against lost and damaged shipments.", "3TvbWu": "<PERSON><PERSON><PERSON>", "3UE33V": "Add new Debit/Credit Card", "3UdyTP": "Liquids Items", "3VI9mt": "Save Changes", "3a5wL8": "Active", "3aXUzh": "This product has multiple options, like different sizes or colors", "3ajH0r": "Order setted as fulfilled", "3b0NOF": "Address added!", "3c38L7": "Thanks for your patience", "3dwSlf": "Remove item", "3etgsG": "Kabreet", "3f4+GP": "Red Sea", "3gLXJD": "All reviews submitted on your store, whether Verified purchase or Guest reviews, are not published automatically. Reviews are sent to the <b>Reviews</b> tab for you to choose which to <b>Publish</b>", "3gYA53": "Extra COD Collection Fees", "3hYpLY": "Email notifications", "3hmlx/": "Update Staff Permissions", "3jUFGW": "Are you sure you want to delete the {page} page?", "3lFbmc": "Enter store name", "3m4iix": "Product not available", "3nhRQ1": "Area not supported", "3pESiw": "Why is it important?", "3pe/85": "Industrial Development Bank (IDB)", "3sT8wz": "Delivery min cycle (Central Egypt (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Sohag))", "3vX064": "Maximum compensation per lost or damaged shipment", "3w5Nop": "Total shipments", "3wSbMZ": "Chairs", "3x7wJx": "Are you sure you want to remove {countryName} from this {zoneName} zone?", "3x8xvF": "Read Sea", "3y9R4A": "Elmarg <PERSON> <PERSON><PERSON><PERSON><PERSON>", "3yk8fB": "Wallet", "3z1dnB": "To reconnect, activate the new Conversion API tool to track ads accurately, lower costs, and overcome ad limitations!", "3zOk9H": "Explain the reason for this refund...", "4+c5lA": "Legal page", "4+xFcK": "Edit automatic discount", "41d/Fg": "Enter the pickup location name", "44Z8WS": "Select a flyer", "44aBGX": "About the company", "450Fty": "None", "46mMfJ": "Add Staff", "47FYwb": "Cancel", "49IjhJ": "Single, users can pick only one product.", "49sNcx": "Sale Price", "49z1jB": "Shopping cart functionality", "4AAxxZ": "Alex, Suez Canal (Port Said, Ismailia, Suez)", "4AiT9W": "Address updated!", "4HJO6D": "Product reviews has been enabled on your store", "4Nva5e": "Pharouns Rally Tabania Bivoac", "4R1rGW": "Translations saved!", "4RHt8W": "Please select a collection", "4Rz2mY": "CUSTOMER", "4T0oX/": "Add your products with photos and prices. Build your catalog by adding products.", "4Vfjv7": "Desktop Image", "4dDeoi": "#{serial}", "4dZi3Y": "API Key", "4dqhLQ": "Ghamaza Elsaghira/Elkobra", "4eiM7S": "Everything in <b>Plus +</b>", "4fDKNQ": "Add your Google Tag Manager container ID to update your site tags and code", "4gOq7u": "{fixedOff}{currency} Off total price", "4o5N5G": "Are you sure you want to delete the {value} option value?", "4oB0Fm": "Edit Collection", "4pHzvx": "<span>Order viewed</span> – This order was viewed.", "4pgM2I": "Non-servable areas", "4qZygX": "Invited Stores", "4qmB4Q": "Page visibility Status", "4tMyq1": "Discount Name", "4tiAU/": "Delivery min cycle (Al Wadi Al Gaded, North Sinai, Abu Simbel, Marsa <PERSON>, Salloum, Halayeb & Shlateen)", "4zMFic": "Pending pickup", "51vt6V": "<PERSON><PERSON>", "54wpwb": "🎉 <span>Congratulations!</span>", "56ILXv": "Connect the best shipping companies to your store, order packaging materials from your store, and ship your products effortlessly.", "57E7W/": "List the materials you'll use to package the order.", "57MvNW": "Page Information", "58UFar": "Are you sure you want to cancel the return airway bill?", "59JCaj": "SSL SECURE PAYMENT", "5A5tMT": "Fixed amount", "5AD4R4": "Biggest Offer of the Year!", "5CqMBV": "Page deleted!", "5DTZAJ": "Print Airway bill", "5Dyt/2": "Edit your store's free sub-domain on Wuilt to match your brand", "5F8uSC": "Original order", "5FjyGT": "Please enter your country", "5Fs4aA": "Add Category", "5HeOFu": "Click to edit", "5IP7AP": "<PERSON><PERSON><PERSON>", "5ItEH1": "Banners row updated!", "5JcXdV": "Create Account", "5Jcaq4": "NEW", "5JdOPS": "Airway bill request transaction", "5LCUG5": "Allow your customers to pay in multiple methods with just a few clicks", "5M54sW": "Delga", "5MDGuM": "Enter your email", "5MFNWE": "<b>Past Due</b> - Contact Us", "5MTDfT": "There are no categories added. Add your first category", "5NMyv/": "Info bar", "5ODqV9": "Custom Shipping Zones", "5QBrGP": "Orders cancelled successfully", "5QYdPU": "Start Time", "5QiLdG": "Customer gets {returnAmountWithShipping}.", "5RyK2O": "Short description (150 chars)", "5SDp1b": "Extra Shipment Charge", "5U2wOH": "Add variant", "5U8ytZ": "Add Automatic Discount", "5VJ7dT": "VAT number", "5Wwopw": "Pricing", "5X3XJF": "Request needed", "5XawEn": "ITEMS", "5YhudP": "Pending settlement", "5bGX59": "Social Media", "5eA+9c": "You have made some changes which will be lost if you discard the changes.", "5fTjGI": "New variants option added!", "5fXyYj": "Online Payment Integration", "5flv+N": "Manual shipping", "5gqbOV": "attribute must be unique", "5iAoBq": "Account number is required", "5iUj/w": "<span>{name}</span> canceled the order", "5ij3RF": "Material request transaction", "5jFeDE": "<PERSON><PERSON><PERSON>", "5lCY7S": "Add Product", "5lZK3W": "Selected Products ({count})", "5msJy3": "Automatic discount added!", "5ow6dg": "WhatsApp", "5q0GWM": "Service Quality Complaint", "5q3qC0": "Download", "5qaD7s": "Rejected", "5qtWXE": "Customize Checkout", "5s2ruq": "Abis 08", "5sg7KC": "Password", "5wC1Uy": "Online Payments", "5yZZ3C": "Availability Status", "5zXQbE": "Order is archived", "5ziJhx": "UnFulfilled ({count})", "6+4oFM": "Invalid quantity", "6+NbDk": "Book a demo", "6/UCQ6": "Select a city", "60UDUN": "Your payment method has been successfully deleted", "61Xs6O": "Customer name", "62q3s4": "Delivery min cycle (Canal)", "632IT+": "<PERSON><PERSON>", "64NAin": "Display", "69XEZi": "Transaction pending", "6AQwkN": "{count, plural, =0 {} one {<b>1</b> Attribute} two {<b>#</b> Attributes} few {<b>#</b> Attributes} many {<b>#</b> Attributes} other {<b>#</b> Attributes}}", "6BYNdT": "Return request sent successfully", "6BmJBo": "Code title is required", "6CUUxx": "<PERSON><PERSON><PERSON> - Establ Antar - Arab Rashid", "6FJvBQ": "Refund amount is required", "6FtF5d": "Connect a domain you already own", "6GgWks": "Please enter the app name", "6GggzB": "Bank name is required", "6IA1Cq": "Alkarah", "6JL8M8": "Edit Page", "6LKnLf": "Region Level On Checkout", "6LrbbP": "Search or Choose page", "6P3dIB": "ElH<PERSON>", "6QbnnT": "Edit pickup location details.", "6SI3PV": "medium", "6SKhT/": "Aramex doesn't support refunds while returning shipments!", "6Tps09": "Inactive", "6aHfA7": "Pinterest", "6ao3+Q": "(Annually)", "6blfux": "Rows ‘<b>{rowNumbers}</b>’ in ‘<b>{productNameOrHandle}</b>’", "6cBDhe": "Integrations", "6dIxDP": "Yesterday", "6dkSvF": "Single Choice", "6ee1ZX": "Upload Favicon", "6fFaFn": "Prohibited items", "6fkz6Q": "Cash on delivery is disabled", "6fpLnn": "Option", "6frscI": "Facebook Pixel", "6g0XL0": "Total orders", "6j4NIq": "All Features", "6jE+aY": "Add Video row", "6kXKTp": "{name} permissions revoked successfully", "6md355": "Store Currency", "6nEr8K": "ElTamamah", "6oOCCL": "Upload file", "6peuvs": "Undelivered attempt", "6qiRsH": "The logo container size will be {size}px*{size}px", "6u8KLJ": "Elkasr", "6uMqL1": "Unpaid", "6uffkn": "{count, plural, =0 {} one {1 level} two {# levels} few {# levels} many {# levels} other {# levels}}", "6uqGgH": "Product option already exist", "6wAlLx": "Shipment canceled", "6xsI9b": "Recent Reviews", "7+Domh": "Notes", "70ikix": "{maxLength} characters max", "724CrE": "Select...", "7397xA": "All products", "76HVpX": "A reminder email will be sent to the customer approximately {emailSentDayDifference, plural, =0 {today} other {tomorrow}} at {emailSentTimeInHoursAndMinutes}", "76Twyb": "<b>Free Trial</b> - {count} days left", "76XEEJ": "Transferred by Wuilt member", "76pb2o": "Google Analytics & Facebook Pixel", "79IWia": "Header", "7DIW6+": "Learn More", "7Ge4t3": "Delivery min cycle (Upper Egypt, Red Sea, Marsa Matroh)", "7GpM0h": "We’re currently updating our shop, and we will be back really soon", "7JQtTa": "{numberOfItems, plural, =0 {0 items} one {1 item} two {# items} few {# items} many {# items} other {# items}}", "7JvvQi": "Incorrect Bank Details.", "7LReO8": "Legal Pages", "7LjPjd": "<PERSON><PERSON>", "7NFfmz": "Products", "7Now1D": "Delete the option's values from the linked products and try again.", "7OquwP": "Vodafone Store Starter", "7P1QZj": "Request by {count}x or more", "7PMLfK": "Order Total", "7SJF2y": "withdraw", "7Uhk06": "Discount percentage must be less than 100", "7V6vuD": "Action needed", "7X9Qy0": "Return shipment bill", "7XkDpv": "The product data will be applied to all variants.", "7Xz00f": "Super Admin", "7Zq+MU": "Store tax will be added to order checkout", "7a2qUk": "Invalid cart max", "7b5dyA": "Additional weight", "7cfLJs": "Manage your store’s legal pages", "7ee+8G": "Please edit the quantity of this product, the minimum quantity <b>{availableQuantity} per order</b>", "7g+l0L": "Created on {createdAt}", "7gRfiR": "Near UpperFayoum, Bani Suif, Menya, Assuit and Sohag", "7hehel": "Your withdrawal request is pending processing. Money will be transferred next Thursday.", "7kaHEt": "Claim Domain", "7kg3dK": "Zone not supported", "7l044F": "Select packaging material", "7luA64": "Migrate/Import your products", "7m/Bvw": "Button background", "7mw9yr": "Online payment is enabled, visit settings page to activate them", "7rQTlS": "Set the refund amount", "7rT8dy": "Cannot refund an unsettled payment", "7rueTA": "Product reviews changed successfully", "7s4V6B": "Changed from", "7uRrPW": "ex. Our Featured Collections", "7ujAM7": "Are you sure you want to cancel this order fulfillment status?", "7vfd9o": "Now you can ship with the biggest companies in Egypt, easily and with just a click, all from one dashboard!.", "7w6zRb": "Wizard not completed", "7yH8jR": "El Salloum", "8/3Bt1": "Actual weight", "8/Af0a": "Free Shipping discount added", "8/r7Ho": "Collection url must not be empty", "81AOPK": "Embed row", "878mRN": "Settings updated", "87tixo": "<PERSON><PERSON><PERSON>", "89T/ze": "Invalid", "8AT504": "All carts", "8AxPUr": "<PERSON><PERSON>", "8IWVBX": "Your reply has been posted", "8PlOYs": "Shipping information", "8PnTBr": "301 Redirect", "8R2UJn": "A basic store theme is a simple and user-friendly design template for an online store.", "8RszcM": "Start editing robots.txt", "8T65rW": "ORDER TRACKING", "8URj5w": "Product listings", "8UzT6d": "CSV", "8V20ON": "Footer End Part", "8VsGr7": "Reviews updated successfully", "8VwDIP": "Online Payment", "8WKgF6": "Revenue", "8Wuy8C": "The actual weight of the shipment in KG", "8XWENH": "EXPORT LANGUAGE", "8XaHNu": "Abis Elsabeaa'a", "8b4GdX": "Export orders", "8bgi5q": "Pickup time exceeded", "8fI5PB": "Cancel order", "8ffPeP": "Buy {customerBuyCount} and get {customerGetCount} with {percentage}% discount on selected products", "8hEtcB": "Reactivate auto send", "8jHhlN": "Request airway bills", "8k/ydZ": "Down for maintenance", "8nTJAi": "<PERSON><PERSON>", "8notOt": "Press enter to add", "8oVFtC": "Minimum quantity of items", "8qJOWu": "Refund amount shall be less than order amount", "8qmkig": "<b>Expired</b> - Upgrade Now", "8r0TAZ": "<span>{name}</span> canceled the airway bill", "8t/ufm": "Fulfill items", "8uEwrb": "Add attribute values to the row by clicking the <b>Add attribute</b> button below", "8vV3Z9": "Add automatic discount", "8vbb+R": "El <PERSON>", "8vbtx1": "Email is not valid", "8vgHov": "Enter refund amount", "8w/vXG": "City sub-division", "8w2T4h": "Payment method deleted successfully", "8wVRDL": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>", "8wlovj": "Are you sure you want to delete the {variant} variant?", "8xXqFO": "Average order value", "8ywLSf": "Twitter", "8zfHVo": "Maintenance mode", "9+8ARf": "You’re importing products to your Wuilt Store", "9+Ddtu": "Next", "9+KZhw": "Add a note to your Email confirmation", "9/BBN/": "SVG Icon", "9/ngI2": "Only SVG tag is acceptable", "93tPcl": "<PERSON>", "949h+H": "See information about the company shipping zones and rates.", "94mNuy": "This option allows buyers to inspect the product inside the package before accepting or paying for it.", "95stPq": "Completed", "96LQMy": "Email confirmation notes must be between 10 and 350 characters", "97gepv": "Deleting Product", "97s2+w": "Created on", "98Cp7D": "Airway bill", "98xFp9": "Egypt Post", "99OtWT": "Pending...", "9BaRDZ": "Manage all your orders and shipments effortlessly from one easy-to-use dashboard.", "9BgMfi": "Elgabl Ela5dar fel Eltal Elkebeer", "9CCPJU": "Almargayah", "9DVzTk": "{count, plural, =0 {No items} one {One item} two {# items} few {# items} many {# items} other {# items}}", "9EtQM0": "To connect your account:", "9FLIXa": "It may take up to 24 hours for your domain's new DNS records to propagate and your site to appear worldwide", "9JFntE": "QUANTITY", "9JdDNd": "Suez Canal Bank (SCB)", "9L1hAY": "Make the announcement bar dismissible", "9Ni82C": "You can now find the imported products under store <b>Products Tab</b> and start editing them", "9Obw6C": "Filter", "9PkB1V": "Edit Text row", "9QCMWh": "Account IBAN number is required", "9SR+qB": "Unauthorized action", "9T8sQg": "Withdraw amount is required", "9XUYQt": "Import", "9YazHG": "Company", "9ZSvl3": "Online payment", "9a9+ww": "Title", "9cJssD": "Optimal description length is ~ 155 to 300 characters", "9dscMN": "Secret key must not contain special characters", "9fo/xL": "Your subscription was ended", "9j3hXO": "To", "9j5KzL": "Enter category name", "9k5qQl": "Payment methods", "9kzAW9": "Search by attribute name", "9lQTrh": "We will review your top-up request and add the funds to your wallet within one hour.", "9maTCT": "Be sure to review the terms and conditions thoroughly.", "9oJl55": "Make row take full screen width", "9qB9mf": "Paste video link here", "9sBrUx": "To start using Wuilt shipments, add money to your wallet to ship your first order.", "9sCCBz": "Page updated!", "9uOFF3": "Overview", "9vg5Hb": "{count} Products selected", "9vqPaF": "Root", "9w1BHU": "Browse Products", "9w8vC7": "Cash Outlet", "9wAw2w": "Saint <PERSON>", "9xD8sO": "We’re importing {productsCount} products to your Wuilt store", "9zALAl": "Product Reviews", "A+V57x": "EGP", "A/NxFC": "Rejected transaction", "A06dWw": "Always show", "A1Py1x": "On-hover animation", "A1gHJU": "<PERSON>", "A3+Uo7": "Easily withdraw funds or top up your balance, all in a simple and user-friendly dashboard.", "A3JLm3": "ex. https://www.linkedin.com/in/YOURSTORE", "A3kYun": "No coupons", "A5WDzK": "Arab African International Bank (AAIB)", "A6ivgz": "Subdomain", "A7ey1V": "New orders to ship", "A8qPuH": "ShopMart", "AC+b3Q": "Calculation error", "ACtNdE": "Unlimited products", "ACvUZz": "e.g., https://[storeDomain]/en/about", "ADwomG": "Button text color", "AEnoiS": "Comment on an order or mention staff using Timeline.", "AFAU4f": "<PERSON><PERSON><PERSON>", "AHTNQn": "Shipping Companies", "AJvAiV": "Insufficient quantity", "AKnuZc": "Code should not contain spaces", "AL3nyV": "ElAk<PERSON><PERSON>", "ALPpar": "{percentage}% discount on shipping for {minimumPurchase} on the total invoice", "AN4GQ/": "Shipping status", "AOAZ5L": "2 working days", "AOLNgx": "No payment method", "AP1QY6": "Choose an available domain name to house your website", "AP1z35": "Heavy bulky (Package type)", "AVl5ku": "Search customers by name, e-mail, phone no.", "AY4uuA": "Note: Product image will get enlarged on click or tap", "AZeJCx": "Alakwaz - <PERSON> - <PERSON><PERSON><PERSON>", "AarHCY": "Store Name", "AcxDgz": "Activate", "AjBEeD": "Page name", "Amt/8b": "Minimum", "AoYpEs": "You can offer your customers a fixed value, percentage, or shipping discount on products, collections, or variants in your store.", "AqXzM2": "Order #{orderNumber}", "Ar3oN9": "Payment settings", "AutANa": "Medium Flyer", "AvQNkB": "<PERSON><PERSON> Quantity", "AvX099": "Withdrawal transaction", "Avsnjl": "Themes", "AwMARL": "Business Website", "AwOfOx": "Start selling today", "AwzkSM": "Tax", "B/we9O": "160 characters max.", "B5GI7G": "Add funds to your wallet", "BAAiPI": "Shipping Quick actions", "BACCkG": "Length", "BB57xW": "<PERSON><PERSON><PERSON>", "BCzmdv": "Maximum", "BD/Bw8": "Join Wuilt Merchants Community on Telegram", "BDPXWH": "Transferred successfully", "BERvr6": "Wallet settings", "BEqvr/": "Contact Info", "BF1Gga": "Update customer contact information", "BFcZ/0": "Add Page", "BIFTGS": "Pending transaction", "BIJk93": "Canceled (uncovered area)", "BJ2x02": "Shipping percentage", "BJk1LT": "Dedicated account manager", "BKNdtj": "Pickup date", "BLG0i/": "Shipment canceled successfully", "BMJVyr": "Delivery min cycle (Far Upper, Matrouh, Qena, Luxor, Aswan, Red Sea, Matrouh)", "BN1Bgh": "<PERSON><PERSON><PERSON>", "BNeOxz": "Domain removed successfully", "BNtpR2": "Tax Percentage", "BOaTAG": "Mark as Partially paid", "BPnT3T": "Small", "BR4z3R": "your domain {domain} is available", "BR5hv3": "Made with core features you can easily customize—no coding needed.", "BRM8W3": "Order total changed from EGP {oldTotal} to EGP {newTotal}, Reason: {reason}", "BRbkB6": "Top up amount must be at least 100", "BSXj/0": "Success COD", "BT/uvC": "Expiry date is required", "BTEVGr": "• {count, plural, =0 {No Items} one {One Item} two {# Items} few {# Items} many {# Items} other {# Items}}", "BTzCXj": "Add/Remove Custom Regions", "BV9xIo": "Today ({date})", "BVTni9": "Payment method", "BVWzJ5": "Edit details", "BVy5Z7": "SSL protection", "BWpuKl": "Update", "BX2tRL": "A sharp, minimalist theme that puts your products front and center", "BXs8Uz": "Enter top up amount", "BY343C": "Change", "BY5c+5": "Change status", "BYDBkR": "On-scroll animation", "BYkFhD": "Products row deleted!", "BaIwdV": "Street", "BamltS": "Upgrade your account to add more languages", "Bbrn+Y": "INACTIVE", "Bc/3Vq": "Small flyer", "BcNTsT": "Total Items Sold", "Bdm/2b": "Cancel fulfillment", "Beauty": "Beauty", "BfoAEw": "ElHager", "BfpxFI": "You haven’t selected any collections", "BgYXO1": "CVC is required", "Bh3RjO": "Address confirmed", "BhOuqz": "Change company", "BhV9oS": "District Not Found.", "Bi9O7Y": "Automatic discount updated!", "Bj6Kz9": "{amount} {currency} was refunded.", "BkkJn7": "Vodafone Store Standard", "Bl6896": "Refund amount", "BlCBvH": "Thank you for shopping with us!", "BlGveL": "Button text", "Bmv8kN": "Add/Remove Cities", "BokmK9": "Cancel send", "BpJ/i9": "Forever.", "BqZ+oN": "Pickup location name cannot contain special characters except '-'", "BsLaPQ": "Material prices", "Bt+6fb": "Customers must add the quantity of items specified below to their cart.", "Bu0UXk": "Shipping city level disabled successfully", "BvbEPB": "City/Area", "BvhICM": "Picking up", "Bvrplf": "{countryName} has been removed from {zoneName} zone", "Bw2zSz": "Minimum per cart must be less than maximum per cart", "Bxemyu": "<b>{count}</b> Request needed", "Bxwp6j": "Returned to business", "C+1pxl": "Please contact us to make changes", "C+2nBu": "ElNegaila", "C+M2nh": "Show/Hide homepage about section", "C1EBvW": "User is not authorized, Please validate the credentials used for the integration and try again", "C4hGj5": "Binoculars.", "C4l33N": "Abis <PERSON>", "C5UsUs": "Oops, an error occurred. please try again", "C5ZWD4": "Apartment, suite, unit etc.", "C5jEja": "Delta, Remote Areas of Cairo (Al Obour, 10th of Ramadan, Badr, Al Shorouk, Al Sadat, Shoubra El Khema)", "C5x3Ns": "Active Dates", "C5xzTC": "Premium", "C6YbML": "(+ Extra fees)", "C7f19k": "<PERSON>", "C9WGEu": "of", "CA3td/": "Failed to print airway bills", "CBl+Kv": "Api key is required", "CBtR7x": "Medium Box", "CCwvNQ": "Mismatched provider", "CEjr10": "Payment not successful", "CFHAtO": "Row ‘<b>{rowNumber}</b>’", "CH+RNv": "Search for collection", "CHCZ1q": "Product Variants", "CHZDL0": "Activate shipping companies to your store.", "CHnQ7E": "Abis 3", "CLwM7I": "You are about to deactivate Wuilt shipments!", "CMansq": "Background color", "CNqZNa": "Changes Saved!", "COH44m": "Easy Orders", "CP1fWD": "Has an account", "CQXyX9": "Select City", "CR/T81": "Order status changed to", "CStAtr": "Copy it and paste here", "CW2IcV": "<b>5</b> staff accounts", "CXRlIo": "Print", "CZ6iiK": "You don't have permissions to edit this page", "Cf6XNM": "Sell In Arabic & English", "CfHsqQ": "Rates are based on the smallest package", "ChNuY1": "Searching driver", "Chn+7q": "Add to store customers", "Ci4LAe": "Cash Collection", "Cjnhz6": "Monitor when customers add items to their cart but don't check out, and consider sending a follow-up email with a direct link to their cart.", "CjsakQ": "Add/Remove Custom States", "Clothing": "Clothing", "Cm5PDg": "Ecommerce Wuilt Template", "CmG7hL": "InstaPay top up", "CmZT0z": "Success settlement", "CnC/xP": "Regular Price", "CqYUsH": "Discount items type not provided", "CsdTXo": "Paste URL link here", "Ct9WDr": "URL redirect updated", "CtApVp": "Customers will get a discount automatically in their cart.", "CtJWAJ": "<span>{name}</span> archived the order", "CtZMCP": "Secret", "CvBxE8": "Please specify a type", "CwagEW": "Go to Websites", "CxfKLC": "Pages", "Cxrerm": "Pickup location not supported", "D1Cxso": "Select Country", "D2hnVm": "Are you sure you want to delete the {product} product", "D3Lu7Z": "Leave blank to disable", "D3idYv": "Settings", "D64kXa": "Si<PERSON>", "D64uLA": "Up to <b>100</b> products", "D7ps94": "How to activate and manage Wuilt Shipments?", "DBJL2o": "<PERSON><PERSON><PERSON><PERSON>", "DEOnrt": "Add your first product description", "DH8pt2": "Preferred transfer days", "DJurBS": "Spying Tools (Hidden Cams & Mics, GPS, etc.)", "DKrbEe": "Min quantity not met", "DM1tYT": "Migrate from <span>Shopify</span>", "DMqnF4": "Delivered", "DNA5GS": "Ahli United Bank (AUB)", "DNvvZU": "{productsCount} Products Imported Successfully!", "DOV+r4": "Buy Now button", "DOs9xK": "<PERSON>", "DP5VOH": "Shipping Address", "DPnILG": "Set up your payout settings first.", "DRLNZw": "Edit rates", "DRMMDs": "Attribute Name", "DRrTZk": "If you want to have unlimited products, upgrade to <b>Plus</b> or <b>Advanced</b> plan", "DSgfKr": "{name} manually edited the order", "DTAkJu": "The total amount after discount must be greater than 0", "DV4aYS": "Store SEO Settings", "DXD57F": "In transit received in hub", "DZKhdy": "Product title is required", "DaXDkG": "El doaa", "DaaEaD": "REVIEW", "Daz4V+": "Internet connection lost. Changes you make may not be saved", "DcwDWO": "El Zahraa", "Dd5Xsg": "Stay on this page", "De7YUw": "<PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "DeZqQy": "Use code:", "Dg5Fxs": "Please Enter the number of the products", "DhMCzk": "Your store is being created", "DiQuTx": "Ship with {provider}", "DkEaKu": "Videos row updated!", "Dkg4uF": "Size: 800px x 1200px - aspect ratio: 2/3", "DlDXRO": "Add pickup location", "DmMKww": "This product has limited quantity", "DmOMXT": "ElZenia", "DniiVj": "Missing additional info", "DqNEaJ": "A reminder email was sent to the customer {emailSentAt}", "DqrbMv": "el hanafy", "DrW3Wz": "Deleting Variant", "DtRJSZ": "Mail has been sent successfully to {email}", "Dtd1cF": "Products row", "Du9e4e": "Update Custom Regions", "Dxd1uB": "Shop", "E+h9q/": "<PERSON><PERSON>", "E0Hv8Q": "Global tax {tax}%.", "E0WLt5": "XML File", "E0gpTf": "Search users by name, e-mail, phone no.", "E0vP/w": "Abu <PERSON>", "E2WD3R": "Customer pays the return rate.", "E2c8ia": "This Field must be 4 digits", "E2rh1p": "Buttons", "E3ivuA": "Delivery min cycle (Alexandria, Behira)", "E4+9rA": "Already processed", "E4Ed2e": "Shipping amount", "E5R2Rg": "Option Values", "E80WrK": "Information", "E8VDeH": "No. of Orders", "E9GnLP": "Aldama", "E9v4wi": "Change to", "E9xdmd": "While hovering", "EA5Az9": "Or drop files to upload", "ECExWv": "Start your dropshipping business on Wuilt with {integrationName}", "EEdOfh": "Select a country", "EGHu0Q": "ElAgharbanaat", "EGTO3Y": "Discount percentage is required.", "EGmzBe": "6 Hours", "EIa2T2": "Alkrabla", "ELjB/J": "<PERSON><PERSON><PERSON>", "EP75hK": "Delivery min cycle (Delta)", "EPLtRa": "There was a problem importing your CSV", "EQG0EE": "Cancel return airway bill", "EQPozz": "Antiques.", "ERAbI+": "There are no product attributes added yet!", "ES7qIr": "Qatar National Bank Alahli (QNB)", "ESbmHz": "<PERSON>", "EU1IWw": "You’re adding custom cities under {stateName}", "EU36wp": "Contact info.", "EUOKEt": "The integration deleted!", "EWngmU": "The checkout timeout is invalid.", "EWw/tK": "Apply", "EXYuFH": "Housing And Development Bank (HDB)", "EXb8Fu": "Validate", "EZozk2": "This option is linked to {count, plural, =0 {} one {<b>1 product</b>} two {<b># products</b>} few {<b># products</b>} many {<b># products</b>} other {<b># products</b>}}", "EaORP9": "Invalid city", "EacBjJ": "Add Discount", "EcglP9": "Key", "EciuMD": "Unlimited Products", "EdRdk1": "<PERSON><PERSON><PERSON>", "EdoWRV": "You have stable connection now. Editing is available again", "EfWyVl": "<PERSON><PERSON>", "EgOSRZ": "No Location", "EgkxWe": "Tell the world what makes your store special", "EhefYN": "Upper Egypt, Red Sea , Marsa Matroh", "EjX68N": "How to bulk import products?", "Ejbeo9": "Adult Products", "EkF8qr": "Plan started on {subscriptionStart} - Renews at {subscriptionEnd} and ({daysLeft, plural, =0 {} one {1 remaining day} two {# remaining days} few {# remaining days} many {# remaining days} other {# remaining days}})", "El3BHm": "A step is missing to complete the transaction.", "Electronics": "Electronics", "Em8j7d": "160 characters max", "EmgyG3": "Custom checkout updated", "EmpHyB": "Facebook", "En91ub": "A 1% fee is applied after the order exceeds EGP 2,000", "EpTTzZ": "Review Rating", "Eq2y/J": "Amount off products/collections", "EqxJUD": "Easily ship your products with different options.", "EsIQtF": "Product URL", "Et1YZn": "Invalid checkout notes", "Eu38ks": "Minimum purchase amount is required", "EuG7yL": "ex. <PERSON>", "EvOBKN": "Need help? We’re here for you", "Ex0/23": "Migrate from <span>Easy-orders</span>", "Ezb07z": "Are you sure you want to delete the {collection} collection?", "F+L/Y3": "Your withdrawal request is pending processing. Money will be transferred next Tuesday.", "F/Or18": "Please enter a valid domain address", "F/QEac": "Customer email is required", "F0STIq": "Klaviyo Installed successfully", "F2Zv/P": "Please enter your organization", "F2v3fv": "Add option value", "F34Qh7": "Weight calculations", "F3LuQG": "Dedicated Account Manager", "F5sf8U": "CCTV, DVR, NVR,.etc.", "F7r9Dh": "Not picked yet ready", "F9OiZt": "Redirect URL deleted successfully", "F9WHi2": "Limited Permissions", "FAVHrs": "Agricultural Soil.", "FCsFx6": "{amount} {currency} is pending payment on Cash on Delivery (COD)", "FETHiz": "ex. https://www.tiktok.com/YOURSTORE", "FGhjfk": "Promo code usage limit reached", "FKewH3": "<PERSON><PERSON><PERSON><PERSON>", "FL6lKX": "Lost", "FMKUos": "Need help with your Wuilt store? Request a demo with a Wuilt expert, they will onboard and assist you with everything you need.", "FO+0EY": "Fixed amount is required", "FPcbu0": "Please enter a valid phone number", "FQUn00": "{name} permissions changes saved", "FQum7f": "ElTawfiqeyah", "FQx3Ge": "Cannot update the store sub-domain address", "FSy+xu": "Advanced shipping settings enabled successfully", "FU9UPo": "Deposit transaction", "FX9RUR": "Delivery min cycle (Sinai, New Valley, North Sinai, South Sinai and New Valley)", "FXEXr5": "Payment updated successfully!", "FZI5xl": "First name is required", "FZYT2S": "New Currency is required", "FaKj4k": "Coupon Code", "FaZltW": "Pickup map location", "FdDtWh": "MANUAL CHANGES", "Fe6eHI": "Give staff access to your store by sending them an invitation & configuring their permissions", "Feo0iS": "Select or create an option and its value", "FgiXKd": "Add/Remove Custom Cities", "Fkkh1j": "“Allow customers to open packages” option is now disabled", "Flx2cd": "Delivery location", "Fn6Onv": "Chat App Integration", "FoEamN": "Delete email", "FoHhPF": "ElHaswi Spring", "FqNbTE": "Sekam", "Frf/VA": "Are you sure you want to delete this address?", "Fs1yWv": "Custom Footer", "FuI6qb": "Your last name", "Furniture": "Furniture", "Fv6I5e": "View report", "FwjNHN": "Preview of the first product in CSV", "FxQp91": "Export your transactions to a CSV or Excel file.", "FxxeZL": "Firearms and Sound Effects too.", "FzyuNE": "SAVE {discountPercentage}% with yearly plans", "G+ViE7": "Make sure to read the terms and conditions", "G+ZOvj": "Your store’s integration with {integration} has been disconnected to be replaced with a better tool!", "G/yZLu": "Remove", "G0FS2H": "Font style", "G27DvO": "(18px*18px)", "G5OzWs": "Show on hover", "G7eYd6": "CRN ID", "G7lgGo": "city, zoneId, or districtId is required.", "G7oTYe": "Uncovered drop off or pickup address.", "GAa2Uy": "Old URL", "GBx/U4": "Central Egypt (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ya, <PERSON><PERSON><PERSON>, Sohag)", "GDtNgJ": "Info bar text", "GEEpGk": "Add pages to your store, and customize how they look", "GG7dbf": "Added “FREE SHIPPING” Discount", "GHYwh6": "Product attributes must be unique", "GHfSPD": "<PERSON><PERSON>", "GHuLvb": "ElKhatatbah", "GIKv5Z": "<PERSON><PERSON>", "GJYfSE": "<PERSON><PERSON>", "GK8pLL": "Delete review reply", "GMY0Zw": "Pickup address is required", "GNFnxS": "Selected in another zone", "GO3nj3": "You’ll be able to manage product reviews, once your customers add them", "GS9lwl": "Or drop file to upload", "GZJpDf": "Analytics", "GbhQNd": "In transit received by myler", "GdGXNO": "Products are deleted!", "GfBSPQ": "Order ID", "GfPyX/": "<PERSON><PERSON><PERSON><PERSON>", "Gh+LjA": "There are no products yet", "GiFCZV": "To create an account with {integrationName}", "GiHTvu": "Please wait for product media upload to complete", "Gmngoe": "Collections row added!", "GnLWnS": "Sports Equipment", "GpuoSE": "SKU must be at least 3 characters", "Groceries": "Groceries", "Gs86nL": "Refunded", "GsBRWL": "Languages", "GshkfP": "<PERSON><PERSON>", "GtQUit": "Download a <link>sample CSV template</link> to see an example of the format required.", "GuF6Y0": "<span>Partially fulfilled</span> – {items, plural, =0 {} one {1 item} two {# items} few {# items} many {# items} other {{items} items}} fulfilled", "GuMH+I": "Track your shipments easily", "GufXy5": "Value", "Gy9EC3": "Qetaa ElTarik <PERSON>", "GzaPTE": "Meta Pixel is now connected", "H+BVaq": "Total Spending", "H/j2wo": "Claim domain", "H4Hpf/": "Grow faster with us! 🚀", "H4PL1G": "<PERSON><PERSON>", "H77bVG": "A favicon is a small icon next to your site title. It help visitors recognise your brand and stand out in the tabs.", "H7iy9z": "Select at least one product for each category", "H89b8u": "Google Analytics is updated", "H9Dwvp": "Select Collection", "H9En0K": "1 Online payment integration", "HAlOn1": "Name", "HBG/0I": "Black Desert", "HE1o0L": "Enable Cart Limits", "HEtFaA": "Tags removed successfully", "HGHo5k": "Success rate", "HGgnTJ": "Deer Elnawa<PERSON>", "HHKrqM": "Enter your desired domain name", "HHXLSq": "Coupons", "HIzN7V": "Add payout method", "HJEJo0": "Classic", "HK8PaE": "Add collections row", "HKkXVb": "<b>Delivered Orders:</b> Orders successfully delivered to customers.", "HOgmsp": "Edit Rate", "HP3V8w": "Invalid input", "HPZJM8": "Sunday 5:00 PM", "HRb+f8": "Elmansory<PERSON>", "HTYbII": "Selected orders", "HVdS1p": "The number you enter here will be multiplied by the resource price.. {br}ex. 50 * 1.25 = 62.5", "HYNI7d": "Search countries by name", "HYdAi3": "You haven’t selected any attribute values", "HalL+U": "Unlisted", "Handcrafts": "Handcrafts", "Hb3FkU": "No Shipping Zones added yet!", "HcULo4": "And also updating <b>{productsCount, plural, =0 {0 product} one {1 product} two {# products} few {# products} many {# products} other {# products}}</b> / <b>{variantsCount, plural, =0 {0 variant} one {1 variant} two {# variants} few {# variants} many {# variants} other {# variants}}</b>", "HeACAf": "Enter product quantity", "Hftk/G": "Each +1 Kg", "Hg3P1+": "<PERSON><PERSON>", "HhCIlW": "Homepage Collections", "HhfMHc": "Segn Belbes Elgeded", "HhjmvS": "Invalid phone number", "HiehQI": "Please enter a valid robots.txt file", "Hj8ga6": "Collections row deleted!", "HmRhMH": "Secret key must not contain spaces", "HmfeV1": "Allows sending server-to-server and offline interaction data as events directly to Google Analytics via HTTP requests, enabling enhanced measurement for web and app streams.", "HmrQBG": "The transaction couldn't be completed.", "Hov/Fb": "Imported Cigarettes", "HqRNN8": "Support", "Hr826A": "Invalid authorization token or API key.", "HtSDyc": "Help Center", "Htago3": "Meta description", "Hu39wP": "Only jpg/jpeg, webp and png files are allowed!", "Hu5Su8": "Review deleted successfully", "Humfno": "End date", "HvFxVs": "Don’t show this again", "Hy0mkk": "The transaction could not be found. Please check and try again.", "Hy14Hm": "No rates. Customers in this zone won't be able to complete checkout.", "HyMpO2": "Percentage", "Hyv/iB": "Damaged Package", "I0B9Z3": "Delivery min cycle (Near UpperFayoum, Bani Suif, Menya, Assuit and Sohag)", "I1P6jZ": "Meta title", "I1iEGe": "Payout method", "I3AjCF": "Here’s where you can manage delete, upgrade, or adjust settings for any of your online stores", "I5OJY8": "Small flyer/box", "I68FCe": "No hidden fees, no catches—just a lifetime of possibilities for your business.", "I6BexX": "Your {integrationName} products should appear here", "I7hn09": "Maximum compensation per damaged shipment", "I7jJn7": "Body font family", "I8+kvd": "<span>Fulfillment updated – </span>The fulfillment was updated", "I9V6UN": "Font preview", "IAzm6n": "{mappedCitiesCounter} cities are already pre-mapped with Bosta’s cities", "IDOba6": "Abis ElMostagadah", "IEBd77": "Checkout ID", "IFo1oo": "Year", "IHN8Bk": "Are you sure you want to delete this Redirect URL?", "IIAdak": "<b>{count}</b> Ongoing", "IKBQ8m": "Payment not settled yet", "ILAV/O": "Pick up and delivery information", "IMNn0W": "Add rows and start customizing your page look and feel", "INlWvJ": "OR", "IOn04Q": "Reminder status", "IPTyRk": "EXPORT", "IPnW+e": "Upper Egypt, Touristique (Qena, Luxor, Aswan, Red Sea, South Sinai, North Coast, Matrouh)", "IQF/fo": "Enter a value for the product quantity", "IRWhI3": "How to edit your store look and feel?", "IRcP0Y": "Create shipping zone", "ITdmlJ": "Contact information", "IUFqJK": "ex. <PERSON>, <PERSON>, Blue", "IaH7ZH": "Here you’ll find top selling products in your store", "IcNqWt": "{variantsCount, plural, one {One variant} two {Two variants} few {# variants} many {# variants} other {# variants}}", "IcUakl": "Time zone", "Id0gUT": "Go to product", "IdLXhT": "Oops, an error occurred. please try again.", "IdRwxu": "LEGAL PAGES", "IeUH3/": "Refund", "Ig0PiB": "Min per order: {minQuantity}", "IgQzAc": "Total Spent", "IhLvoQ": "No. of videos to show per row", "IhOP0t": "Top up wallet", "Ihp1/S": "Invalid discount", "IhxKmf": "Add a reply to this review", "Ij7NEX": "Are you sure you want to delete <b>{couponCode}</b> coupon?", "IkH3lh": "Delete Page", "Ikjx4J": "Ex. 1 USD = 30.83 EGP", "IkyBmr": "Select shipping destination first!", "Im0jVw": "Automatically send abandoned checkout emails", "ImXaug": "token", "IqFUJl": "Rate name", "IrMJdC": "Payout settings", "IsA+GX": "Order Confirmation", "IsnJU9": "You can offer your customers a fixed value, percentage, or shipping discount on the total invoice.", "IvjoDS": "Connected", "IwZXpD": "Are you sure you want to delete the {value} attribute", "IymI2S": "Manage your team members and their account permissions here.", "IzlPwE": "{count, plural, =0 {} one {<b>1</b> Collection} two {<b>#</b> Collections} few {<b>#</b> Collections} many {<b>#</b> Collections} other {<b>#</b> Collections}}", "J+dIsA": "Subscriptions", "J01fhX": "This exclusive offer is just for <span>Egyptians</span> to support local businesses and growth!", "J0vUhO": "Track", "J2+mmH": "No. of attributes to show per row", "J3GrTc": "City Not Found.", "J4+EY1": "Logo size changed!", "J5Iw3b": "Alhamra Doom", "J6Eyds": "Purchase", "J6cIWw": "Remove your store domain", "J756Pa": "Export abandoned checkouts", "J7EQvi": "The products are imported successfully", "J8/oH0": "<span>{admin}</span> updated the payment: <span>paid amount</span>: {paidAmount} {currency} <span>remaining amount</span>: {remainingAmount} {currency}", "J8oI0Z": "Enter EasyOrders Api key", "J93sJN": "Enter custom code", "J9WI1d": "<span>50%OFF</span> on all websites and store plans!", "J9eMH0": "ElKanayas", "J9px1G": "Compare values to previous dates", "JAw+V5": "Order notes updated", "JBWS0c": "Link", "JBn+y1": "Multiple-Choice, users can pick multiple products.", "JC68LI": "Sort Options", "JCIgkj": "Username", "JDv4TN": "{variationsCount} variants", "JFASou": "Light bulky (Package type)", "JH7kob": "El'Ezza Spring", "JHmqcD": "Recent transactions", "JISVYW": "Orders in progress", "JJNc3c": "Previous", "JLAVxb": "{count} variants", "JOj+UA": "Request Airway Bill", "JPvrXS": "Picked", "JTyusB": "ElHu<PERSON><PERSON>Sharqi<PERSON>", "JURbwp": "Bank information", "JUsIUJ": "See how your customers checkout", "JVlTkH": "ex. Our Featured Videos", "JXdbo8": "Done", "JZUGa6": "Account owner name is required", "JZWZSA": "copied", "JaqYLK": "Pay at Fawry", "Jb8ueI": "There are no pages added yet!", "JbnSw3": "<PERSON><PERSON>", "JeVoUk": "Missing product weight", "Jewelry": "Jewelry", "JjYkCO": "Homepage Products", "JkLHGw": "Store", "Jl0VP8": "Invalid expiry date", "JlX9Hj": "Business name", "JplO2W": "Renew your store", "Jq3FDz": "Content", "JqiqNj": "Something went wrong", "Jqq6Q6": "Orders restored successfully", "Jr9hau": "Current Month", "JuYhOK": "<PERSON><PERSON>", "JwIIgK": "Cancel material order?", "Jx4yPA": "Shipment already ongoing", "K/2yNm": "Clear contact info failed", "K/4Dof": "<PERSON><PERSON><PERSON><PERSON>", "K0uOiY": "Here’s what’s happening with your store in <b>the last 7 days.</b> <b>({fromThreeWeeks} - {fromTwoWeeks}) </b>, compared to <b>{fromAWeek}- {CurrentWeek}</b>", "K3r6DQ": "Delete", "K48iIo": "Get a domain name for your store", "K4hDsW": "Add Payment Method Icons", "K56Dim": "Organization", "K72OwA": "<PERSON><PERSON>", "K7AkdL": "Show", "K7rGCU": "Fayoum", "KDdLx1": "Abandoned Checkouts Settings", "KEMPNa": "Order total is required", "KFfLUG": "Tax percentage must be less than 100%", "KGXYqj": "Add Attribute", "KH5r65": "Delete payment method", "KHKq5x": "Ship {selectedOrdersCount, plural, =0 {} one {1 order} two {2 orders} few {# orders} other {# orders}} with {provider}", "KHZlmi": "Discount Type", "KHuVha": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "KHvcuI": "Alwakdab - Alramady", "KImut5": "CHECKOUT", "KLA1nD": "Reviews are not published automatically", "KLh26j": "Congratulations, your store has been created", "KMX1ym": "No, Not Required", "KNiAD7": "Customer full name", "KOcBRL": "Google Analytics", "KP63fg": "<PERSON><PERSON>", "KUBgP9": "Checkout details", "KUnNmV": "Set as Pre-selcted by default", "KVGuI3": "Customer Not Available", "KVzGD9": "Rate Price", "KW7OnZ": "Tax Settings", "KYMCYg": "Shipping rate deleted", "KbH3zq": "Go to Products", "KdSKbs": "Reason cannot be empty", "KfwpJB": "<span>{name}</span> unArchived the order", "KgKNZt": "Apply discount to order", "KiUbmB": "Store currency: The store currency will be updated to the new selected currency. All transactions, including purchases, refunds, and financial reports, will be processed in the new currency going forward.", "KjTqup": "It's a 6-letter code used to track customers actions in your store on Klaviyo platform", "KkVksS": "el masakeen", "KkyaEB": "After Porto Sokhna", "Kl0EL2": "Confirm rejected reason", "Kld2Yq": "Refund amount must be less than or equal to order total", "KlvzVf": "Not Available", "Km4h+B": "Search tags", "Kpbcl6": "<PERSON><PERSON><PERSON>", "KqFb2u": "Request withdraw", "Ks9I9F": "Hi, I would like to book a demo with a <PERSON><PERSON> Expert.", "KtZV9p": "Connection", "KvAfKU": "Instapay transaction", "KvfbIY": "Unlimited Bandwidth", "KwskJY": "COMPANY", "KzUb3J": "New Valley", "L++/M+": "Fulfillment Status", "L+XRF3": "Discount buy x get y data not provided", "L+o3b/": "Last 24h", "L1UKFY": "Subdomain is reserved", "L2GwCQ": "No attributes available", "L2zHhh": "Rescheduled", "L4Vst7": "<span>Order updated</span> – This order was updated.", "L6DkUy": "Help video", "L7uspn": "Shipment not found", "L8p7OU": "Export customers", "L8seEc": "Subtotal", "L99Ji4": "Select shipping cost", "LAhH5V": "Top up amount is required", "LDUIiX": "No variants selected", "LEdEua": "The cost of selected areas has been updated successfully", "LFMvQu": "Failed to download attachment", "LFidc+": "and connect with to e-payment providers!", "LGS6Lt": "Waiting for route", "LIW9jr": "Guest reviews", "LIed/p": "Professional multi-lingual website for your business", "LIvjV5": "Fixed Amount", "LJPN97": "<PERSON><PERSON><PERSON>", "LKuWlb": "Invalid shipping rate", "LLtKhp": "Subject", "LN2GJk": "Order total", "LOany2": "Edit shipping rates", "LQPOVs": "ACTIVE", "LST6kH": "10 hours (recommended)", "LSWVjA": "<PERSON>", "LShFCM": "SHIP TO", "LTvSs0": "<PERSON><PERSON><PERSON>", "LUEP3D": "Measurement ID", "LUUg+c": "Banners row", "LV+wFp": "Decide the final refund amount", "LWD3mC": "Find out about the prices and sizes of different packaging materials.", "LaRuLE": "Reactivate", "LaaZmA": "You're just a few steps away from shipping with top shipping companies.", "LabHH3": "Khoman Spring", "LgQ73F": "{selectedProductsNumber} Selected", "Lgz7CD": "Appliances & Furniture", "LiJqR0": "Text row", "Lif92v": "Any items from", "LkTxJq": "Not picked yet", "LkicTF": "New URL", "Llxbwx": "Android or IOS", "Lmlys/": "State is required", "LnXn+X": "Hawd 14", "Lpd/zd": "Wuilt Shipments", "LpvoQd": "You can't apply discount when both manual discount and shipping discount are applied", "LrVpo7": "Add api key short description placeholder.", "LrqDMl": "Secure online payment using Visa or MasterCard", "LsXm/2": "Enmaa Livestock", "LsykA7": "You’ve got a free store for life! 🚀", "LtO4b8": "Easy-to-edit", "Lv0zJu": "Details", "LvDdMP": "Option added!", "LvqNMn": "Material orders", "LyQkRS": "Category max quantity exceeded", "LzhZQh": "ex <EMAIL>", "LzlgxH": "Returned to stock", "M/TDpl": "Banners", "M/shor": "TAMKEEN", "M/zZVx": "Add filter", "M0F6C/": "Waiting address confirmation", "M1Tiek": "ElHaiz", "M4gAc9": "Add value", "M5+Yy+": "Store industry", "M95phA": "Enter withdraw amount", "M9T725": "Phone and chat", "MAi2tA": "New order notifications", "MAp/Tl": "Enter zone name", "MAsnIg": "Sell In different languages", "MCYd6V": "No product reviews added yet!", "MCpLG4": "Pickup location address", "MCsLan": "Edit Order Total", "MDPHxV": "Add Customer", "MF1iRv": "<PERSON><PERSON>", "MI5Xwl": "Invalid district", "MIwyNY": "<PERSON><PERSON> (MISR)", "MIyOVC": "How to Generate Your API Key", "MJ2jZQ": "Total", "MKw6o0": "Enter reason...", "MLMHS4": "Filtered abandoned checkouts", "MMOk0m": "Add Option", "MMhV12": "qadra", "MOEKdb": "Showcase your featured products and latest news in the homepage.", "MQVSGJ": "No collections available", "MRppfU": "Store logo", "MSz6AQ": "Max per order: {maxQuantity}", "MUzewk": "South Sinai", "MVrgMM": "Upgrade your account to add more products", "MXERMx": "Mansheyat DeBono", "MZ25LD": "Transaction screenshot", "MZ7zCx": "Elgalala", "MZDHYm": "Alkaa'mat", "MZcf38": "<PERSON><PERSON><PERSON><PERSON>", "MZeSKu": "Pre-selected by <PERSON><PERSON><PERSON>", "Mc8nNE": "Transaction failed", "McPwlh": "Options sorted!", "MdntHP": "Current page", "MeOJtL": "Edit Zone Name", "MfVxTD": "Tag added successfully", "MgyQrJ": "Fatoora OTP guide", "MhqPn+": "COD is required for Cash Collection Orders.", "Mj0IUv": "Coupon updated!", "MjT0je": "Zone Not Found.", "MkeUSh": "No top up needed", "MlzEfB": "Inventory", "MmCQZw": "Add to store", "MnDdaW": "Airway bill canceled successfully", "Mnl4OO": "Post reply", "MoRTa7": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MoRfGh": "Make your first payout and track its status here.", "Mop0K6": "Select at least one option and its value", "Mq1NwT": "Product card animation", "MsuiP0": "This exclusive offer is just for Egyptians to support local businesses and growth!", "MubQVf": "“{city}” is not defined in Bosta", "N1+QQz": "Google Tag Manager", "N21hXH": "Please enter content", "N2IrpM": "Confirm", "N2c3yD": "{amount} {currency} available for refund", "N5aoqu": "Al Wadi Al Gaded, Saini governates", "N6B3he": "Add some products to create an order.", "N6v25t": "Customer gets the full amount ({returnAmount}).", "N8kdnr": "No. of collections to show per row", "N8o1dQ": "Add Embed row", "N9bXjM": "Ship order #", "NAHD/Q": "Picked up from business", "NARs5+": "Store Languages", "NASoMm": "There are no rows added!", "NAi8P3": "Bulk Upload in Progress", "NCD1mC": "First name must contain only letters", "NCSnZU": "Large Flyer", "NDV5Mq": "Options", "NEDJhZ": "If you don't see your products here, <b>contact us</b> to help you import them", "NGor0v": "No. of banners to show per row", "NJd8K1": "<PERSON><PERSON>", "NJs06/": "Pending Payment", "NKPfmm": "{count} selected", "NLhHJ8": "Edit this address", "NMB2vd": "(Offline)", "NMgUsQ": "<PERSON><PERSON><PERSON> - Arab Elwalda - Arab Ghonim", "NMyxji": "Container ID", "NNp7Kh": "• You can identify the imported products with {integrationName} badge", "NOUGNr": "Payment refunded successfully!", "NP5jYb": "Enter option name", "NPJbjM": "Mailchimp Installed successfully", "NQ8YV+": "There's a problem loading your data at the moment. Don't worry though! We'll quickly solve it!", "NR49+f": "Collection added!", "NRZd2j": "<PERSON>", "NSASgZ": "Payment method updated", "NWypOO": "Subdomain already taken", "NXNDn3": "Shipping rates: The change in currency will affect the shipping rates. Shipping costs will be recalculated based on the new currency", "NZPkoH": "<PERSON><PERSON><PERSON>", "NbJG7z": "Enter custom city name", "NcCRvs": "0.75% of the AWB amount", "NcKzml": "Edit Order #{orderSerial}", "NeTm0G": "Maximum number of buy x get y discounts reached", "NeXetG": "Add Permission", "NeoNcq": "Add images", "NfwDx2": "Order details", "NgvNKw": "Are you sure you want to delete the {value} option?", "NiV2Rn": "Western Spring", "Nk9TRZ": "Add at least one video link", "NljcOi": "The default cost is {cost}", "Nm7RfA": "This user is already added to the store", "NmK6zy": "Payment", "NmRXld": "Add a new A record, then copy and paste the data from the table below", "Npfwil": "<PERSON><PERSON> saved!", "Nq3WsS": "Apply fixed or percentage amount discounts on your custom build", "NrepBZ": "Discount settings", "NtCCj5": "Shipping city level enabled successfully", "NtH8NK": "Galfeena", "Ntjkqd": "or", "Nyq8y6": "Transfer the amount needed to top up the wallet using Instapay.", "O3INET": "This option value is linked to {count, plural, =0 {} one {<b>1 product</b>} two {<b># products</b>} few {<b># products</b>} many {<b># products</b>} other {<b># products</b>}}", "O6FA0r": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "O7eS7Y": "Duplicate variations(s)", "O95R3Z": "Phone", "O9iqJc": "Book a demo with a <PERSON><PERSON> Expert!", "OB+q8O": "{count} States", "OBSPVE": "Please map this state/city to one of the available cities", "ODdLiX": "No minimum requirements", "OFN81W": "PAY & GET PLAN", "OIZbQc": "Clean aesthetic", "OK5+Fh": "<PERSON><PERSON><PERSON>", "OKuYsp": "Failed to update subdomain", "OL+oyc": "Grow your business even bigger!", "OLhBOo": "Coupon activated successfully", "OLo90U": "Application name", "OMT+M5": "<PERSON><PERSON><PERSON>", "ON9I5D": "Applied Discounts on Shipping", "OQLZV+": "Wuilt shipments", "OR6NkT": "Delta, Canal, Dakahlia, El Kalioubia, Gharbia, Kafr Alshe<PERSON>, Monufia, Sharqia, Damietta, Isamilia, Port Said, and Suez", "ORjjOy": "Coupon added!", "OU0Qnz": "Discount value is required", "OVKeHc": "Provider not available holiday", "OW50WK": "Minimum purchase amount should be greater than discount amount", "OWr2Kc": "Vat number must be 15 digits starting with 3 and ending with 3", "OXsG0V": "Your information is protected by 256-bit SSL encryption.", "OZZ9KJ": "Select location", "OaYU4D": "NEED HELP?", "ObjIkz": "Customers will see this in cart and at checkout.", "ObroR1": "Select all fields", "Obzgqa": "Review reply deleted successfully", "Of81ge": "ID must not contain special characters", "OfQ2IH": "Meta Pixel and Conversion API", "OfkyXk": "Search or Choose collection", "OgFBh1": "<PERSON><PERSON>", "Ogg5vw": "0: 1 EGP{br}2: 1.25 EGP{br}3: 1.250 EGP", "OhIqLr": "Edited", "OhaOa1": "Elnahda <PERSON> <PERSON><PERSON><PERSON>", "OiLtwd": "Are you sure you want to deactivate the shipping company?", "OmYRre": "You are at grace period if you didn't renew your store will be closed today", "OnVWB0": "Num products", "OoKk6/": "Deducted by Wuilt member", "OojpFD": "InstaPay", "OphrTn": "External link", "OsU2Fs": "Attribute", "OtGFZd": "Update your payout information.", "OtMych": "Delete review", "Other food & drink": "Other food & drink", "Otj30z": "We're glad to have you onboard. Here are some quick tips to get you up and running.", "OtwWA+": "Shipment scanned", "Ow2id3": "Unfortunately, couldn't import products", "P0SXsP": "Shipping trails", "P0h/Ov": "Choose page", "P1q5rV": "Invalid Sheet, please check the help center for sheet guide", "P2cFt7": "Select pickup location state", "P7G8fw": "Google Analytics is dis-connected", "P7PLVj": "Date", "P7cGft": "ElLatat", "P7mQHk": "Min. quantity", "P8V2k0": "Shipping pending", "P9PntS": "<b>{count}</b> Delivered", "PA0Jyv": "Go to wallet page", "PCJyCa": "We'll apply additions and removals to the base prices without altering the original base.", "PFXGaR": "Shipping Zones", "PFtMy9": "Canceled", "PHAliI": "Shipping rate", "PHBEzb": "In transit between Hubs", "PIkDe2": "Create a new customer", "PJPyK9": "Upgrade your account to add more staff accounts", "PKGlgQ": "Item not found", "PKXefQ": "You've reached the maximum number of pickup locations.", "PLBvnH": "BUTTON DESIGN", "PLg/m4": "Conflict", "PP/kyG": "Choose collection", "PQxPYM": "Select attribute", "PRlD0A": "Shipping", "PSEKdC": "Invalid card number", "PVXd+T": "Please enter a name", "PVp59b": "Airway bill number", "PYDSFT": "Deleting Redirect URL", "PZaYLF": "El amera", "Painting": "Painting", "PajvDX": "No Permissions", "PbXjsm": "of the Year!", "Pc+tM3": "Generate", "Pc0nCi": "Confirm Navigation", "Pd7Qiq": "Should be greater than one", "PdjSGH": "Option {number, number}", "PdlYhS": "The account ID is used to access the Optimonk API and to integrate with other third-party tools.", "Pf3F4m": "Invalid Option Values", "PgJGHT": "Available", "PhUwrN": "All abandoned checkouts", "Photography": "Photography", "PiT+7u": "Customer name is required", "Pku6ub": "Belbes Desert Road", "PnFo6o": "The order wasn't picked up by the shipping company, you'll receive a refund for the airway bill fees.", "PnjxRE": "Create Store", "PoH2zh": "A new navigation menu will open, click <b>Public API</b>", "PoOp0a": "Upload & Continue", "Ppx673": "Reports", "Psd5Mf": "Select material", "Psv554": "Recovery status", "PwvUJ0": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PyMR+v": "Logo removed!", "Q/kKGu": "SEO Description", "Q1ItHz": "Far Upper, Matrouh, Qena, Luxor, Aswan, Red Sea, Matrouh", "Q2AobD": "Mobile wallet", "Q2EQMh": "Update Selections of {area}", "Q2xuXs": "Returned to shipper", "Q4m1CG": "Edit Product", "Q5CLtp": "Plans", "Q5wCHb": "Edit your free subdomain", "Q6wcZ5": "First Name", "Q7/Qu+": "Flyer", "Q7DU2C": "Set a maximum or minimum quantity for this product that your customer may add to their cart", "Q8PtW2": "Central Bank Of Egypt (CBE)", "Q8Qw5B": "Description", "QAP5dV": "Your Current Plan", "QAbdH+": "After Karta", "QBTXsh": "Limit this product quantity when added to cart", "QC0pBz": "This will remove the reviews & the ability to review products on your store.", "QGVE7G": "Discount amount off data not provided", "QGpO3e": "Sale price must be less than the regular price", "QHL0X0": "Invalid E-mail address", "QHWAxL": "Better shipping experience", "QJhSsa": "GO LIVE!", "QK1aP1": "Shipping company", "QMH6Zx": "Notes to seller", "QODlzl": "Deposit by <PERSON><PERSON>", "QOIyKN": "For payments made via POS", "QOLh4y": "Bank name", "QOpaXi": "<PERSON><PERSON><PERSON>", "QP+Oej": "ex. https://www.pinterest.com/YOURSTORE", "QPilL1": "Map location", "QSdi36": "<PERSON><PERSON> error", "QTnwLL": "Pending payout", "QW+Q5N": "Clear all", "QWsUu6": "Add products row", "QXi5/0": "<PERSON> sha<PERSON><PERSON>", "QYArp+": "<PERSON><PERSON><PERSON>", "QZiHVZ": "Sheikh <PERSON><PERSON>", "QaJEBb": "{dimension}cm", "QakM5s": "Discount collections or products not provided", "Qc5fQJ": "The list of non-serviceable areas.", "QcbK8W": "These are the ways your customers will be able to pay you during Checkout", "QiAB6I": "Volumetric weight calculations", "QqWjVL": "Citi Bank N.A. Egypt (CITI)", "Qqr1nw": "ex. Our Featured Products", "Qr2hFX": "Abis 06", "QvKYpK": "Learn more about robots.txt", "Qw5Uha": "No products", "R+8LxI": "Edit Coupon", "R+J5ox": "Review", "R/6nsx": "Subscription", "R1iSHb": "Order created!", "R2B3i7": "Add your bank account information.", "R3uY7b": "Collection deleted!", "R5c5Gl": "Dimensions (cm)", "R62vZl": "Sync your domain to your website", "R7rgWd": "Visible to you only", "R9j8DY": "Select value for each variant option", "RDFBFA": "ex. your-domain-name.com", "RDXEnM": "This is the fee for transactions through Wuilt Pay.", "RDm1mR": "You’re adding custom regions under {cityName}", "REm9Re": "Ratings & Reviews", "RGAHIK": "Everything in Standard plus", "RGf6v/": "Double-check the generated template and fill in any missing information", "RJ8sFT": "<b>Full Width - </b>", "RKCkvk": "Oops, An Error occurred. double check the credentials and try again", "RKlqx9": "Contact customer support for negative refund amount", "RL6GBF": "Disable product reviews", "RLeTfd": "Toshka", "RLhZlS": "Only jpg/jpeg, png, webp, and gif files are allowed!", "RLi7Ru": "Stars color", "RLvSUp": "Homepage About", "RMQSGx": "Meta Pixel is dis-connected", "RMRoC/": "<PERSON><PERSON><PERSON>", "ROm8pn": "No reviews added yet", "RP7pBd": "50%OFF", "RQnr9M": "Are you sure you want to cancel this shipment?", "RRZptJ": "Are you sure you want to delete this review?", "RRahVB": "LANGUAGES", "RSOCn7": "Send an email to customers who didn’t finish checking out.", "RTOlSm": "Store", "RVoJvL": "Stretch row to full-width", "RWG6mb": "Image size must be at least 32x32 px. Supported formats: png, jpeg. 1 MB max.", "RXRuO5": "Promocode", "RYWzks": "Option Name", "RYbf8K": "Add/Remove States", "RYiGdc": "Contact Information", "RZOWZ0": "Amount paid", "RZVCP/": "Your order has been sent to {companyName}", "RahCRH": "Expired", "Rawe5C": "Something went wrong while sending order #{orderSerial} to {provider} <span> '{' {error} '}' </span>", "Rb/hb9": "LinkedIn", "Restaurants": "Restaurants", "Rfvi9/": "Last 30 days", "Rh6vDo": "Logo size", "Rhqsrd": "Zat El Karam", "Rk4VGw": "Connect existing account", "RkVjyU": "No shipping providers integrated yet!", "RkfO9v": "e.g., https://[storeDomain]/en/about-us", "Rm2SRw": "Clear cart failed", "RnQpWH": "Collections row", "Rnlfj8": "Mandishah", "RnyTeH": "Bosta settings are updated successfully", "RogNJ/": "Add Products row", "RpjBhB": "Request materials", "Rqubj2": "MIDBANK (MIDB)", "RrCui3": "Summary", "RsGizV": "Emirates National Bank of Dubai (ENBD)", "Rv5zdk": "Package opening fees", "Rw7iI7": "Disable", "RwQ/au": "<PERSON><PERSON><PERSON> added!", "Rx5JMs": "Different phone numbers required", "S+5UdO": "Money will be transferred within 3 working days", "S/0i5n": "<PERSON><PERSON><PERSON>", "S0apD2": "<PERSON><PERSON><PERSON>", "S1gpGs": "Last step", "S3cNDn": "Navigation Menu", "S57QRB": "Filter by", "S5t0mW": "Please select a pickup location", "S6PYUv": "Make your material order and track its status here.", "S8yNE7": "Eldoaa", "SAk4jl": "Custom Build", "SENRqu": "Help", "SFSrMz": "<PERSON><PERSON><PERSON>", "SFuk1v": "Permissions", "SHhuWm": "Insufficient Wallet Balance.", "SKFWE+": "Cancel Subscription", "SKePOP": "Add the CNAME record, then copy and paste the data from the table below", "SM+EuW": "OTP", "SMPq9M": "Welcome to Wuilt shipments", "SOL7Ze": "ElSalloum", "SPGUe0": "Order Timeline/Comments", "SPrEvk": "RN", "SSDVwp": "(Monthly)", "SSP7gv": "Invalid package size", "SVwJTM": "Export", "SW641/": "Discount title is required", "SWmn9H": "Delete coupon code", "SX126d": "Export data", "SZX73D": "For order #{serialOrder}", "SaH5Zj": "Attribute deleted!", "ScK4IK": "Partially paid", "Seanpx": "Required", "Services": "Services", "Seu2ON": "To:", "SfRSlv": "Products rows", "Sjo1P4": "Custom", "Sl5Qr2": "Variant deleted!", "SlWuz7": "Google Analytics and Measurement Protocol API", "Sn7xaZ": "Invalid value for ‘<b>{columnName}</b>’", "So7aY2": "Charge customer for shipping cost difference (EGP {diff})", "SogXzS": "Oops, An Error occurred. Please recheck the ID", "SpPKR1": "This product is always available", "Sports": "Sports", "Sqmb2a": "Transaction status", "SsoTeD": "ex. https://www.facebook.com/YOURSTORE", "SvqQp2": "Search collections", "SyR1qP": "Modern", "Sz+Qhv": "Related Transactions", "T/WTkK": "Withdraw amount must be less than wallet balance", "T2DR4n": "Tomorrow ({date})", "T3BRyF": "Debriefed Successfully", "T3nqNp": "Customer E-Mail address", "T4gmxU": "Refund Policy", "T62a6z": "<PERSON><PERSON><PERSON>", "T6HcsZ": "Deleting Attribute", "T72ceA": "Export transactions", "T7Ry38": "Message", "TBOJMi": "Enable users to pick their city on checkout, Gives you more control on showing, hiding and pricing the cities you serve", "TBjjou": "Open package fees", "TBovrx": "Month", "TCZ75v": "My Store", "TE4fIS": "City", "TH1fFo": "Telegram", "THThZY": "Edit Category", "THeCiw": "Currency mismatch", "TJo5E6": "Preview", "TKmub+": "This field is required", "TMOuJj": "Here is what you can do", "TQYJpB": "Some values are missing", "TR+DC3": "Product deleted!", "TRgBFd": "More Options", "TSKHzz": "<b>Expected COD:</b> The total expected Cash On Delivery to be collected from the in progress orders.", "TSr8tw": "Approved payout", "TTWEc9": "Reason is required", "TThIOM": "Not found", "TWZdCI": "Edit variant", "TYN18P": "Print Airway bills", "TYVNC7": "Select Bank", "TcEMDh": "<PERSON><PERSON><PERSON>", "TdTXXf": "Learn more", "TeqVgV": "Invalid customer name", "TgGVeW": "Action required", "ThAqpX": "Folder menu items has no link, It’s used for menu organization", "Tj69jm": "Order Cancellation Related", "Tk0ZpR": "You’ve made some changes. Would you like to save them before leaving?", "TkVgmk": "Fully Responsive Design (works seamlessly on all major web browsers, tablets and phones)", "TkYZBT": "Customers", "Tkcsvf": "Attribute added!", "TkwTKx": "Connect my account", "TlQPoh": "Cannot change shipment status", "TndG0b": "{value}% Discount", "TnfocA": "Vodafone Store Advanced", "To+Zwp": "Deactivating shipping company", "ToUG9H": "{companyName} activated successfully", "Toys": "Toys", "TtTPxt": "Your cart is empty", "TuIOpR": "Shipping zone updated!", "TuNqpM": "File size shall be less than 10MB!", "TudBjU": "Pickup location", "TwHQMT": "Remove tags from orders", "TxQNvN": "Cash on delivery is enabled", "TyL0KG": "Text alignment", "U+dGE5": "Colors", "U1qOwQ": "Yesterday at", "U34YI+": "Branch assigned", "U3RGdF": "Confirm Full Payment", "U4q6BZ": "Narcotics and Registered Drugs.", "U54gg9": "{selectedNumber} Selected", "U55OrL": "CVC is invalid", "U74BNH": "Cannot cancel in progress order", "U7O7rO": "My Subscription", "U8QBHO": "Powered by", "U9CUN+": "<span>75%OFF</span> on plus and advanced store plans", "UDYFCR": "Favicon removed!", "UFQQSc": "Pickup address", "UHu1T/": "Phone number is required", "UIuFyd": "Pending COD", "UNOOD7": "or drag and drop", "UOoIZx": "WooCommerce", "UPJQPD": "Required category missing", "UPpT2q": "Remaining balance after automatic transfer", "UQ9B76": "Add your first product", "UQHsqP": "Add your first collection", "UQS5ws": "Generate an API key to authorize external API", "UR1bin": "Store details", "URmisu": "Al Baraka Bank Egypt B.S.C. (ABRK)", "US+yOA": "Cart is reserved", "US2oB/": "Zones", "UTVrpt": "Enter Shopify store link", "UaduDG": "Robots.txt file created", "UdVzmc": "Out for delivery", "Ue3cf2": "Open link in a new tab", "UfqJ7U": "Minimum purchase requirements", "Ufvpce": "Add banner", "UhfPP7": "Edit Banners row", "UjEjZV": "Complete your first order and receive payment via cash on delivery.", "Um+17a": "All orders are successfully sent to {provider}", "Un1mxZ": "Add tag", "UnCw4y": "Enable “Allow customers to open packages” option in checkout", "UnbMVC": "Focuses on essential features", "UoA7/Q": "{name} has access to this store now.", "UpYu+z": "Order #{orderSerial} sent successfully to {shippingProvider}", "UpfMgy": "EXPORT AS", "Uq9V/4": "Secret value", "UqKMjl": "Postal Code", "UsihVP": "Not Sent", "UvIDEi": "<PERSON><PERSON><PERSON>", "UwT1mQ": "Create Discount", "Ux2zzz": "Edit Attribute Value", "UxJ9Nt": "Business name is required", "Uxd10D": "Maximum COD value", "UyoYSx": "Header is required", "Uz2uz6": "(Facebook, Tiktok, GMC)", "V+XO3u": "Failed to get shipping snapshot", "V+iIgm": "<PERSON><PERSON>", "V0RBFn": "Cut off time for Thursdays requests", "V22724": "Installed", "V23QW1": "Define your shipping rates", "V2ZatV": "Fast And Friendly Support", "V3oRVa": "Let your visitors get to know you. These details are used for businessing, invoicing, and more.", "V4dc2X": "Homepage Main Slider", "V5HESX": "Cancel withdraw", "V671eT": "Customize how your store looks", "V6pP8P": "Are you sure you want to delete this section?", "V8bGew": "Stock reservation failed", "VAsUyo": "Only visible to you", "VBRoiX": "Show customer avatar", "VE504l": "First name only", "VIb/ai": "Translation saved!", "VIlOH/": "Wadi <PERSON>", "VJ10Vq": "No, keep it", "VJIkJf": "Free Domain Name", "VKAFu7": "Not sent due to auto send email disabled from settings.", "VKb1MS": "Categories", "VKsytu": "Subdomain is required", "VLFi1T": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "VLK0WW": "Forbidden", "VLWlHJ": "Add custom state", "VMPLB4": "Applied manual {discountCurrency}{discountValue}{discountPercentage} to subtotal order Discount", "VPbey1": "Shipping problem?", "VSx48d": "Videos row deleted!", "VUZ9T+": "Transaction type", "VUjjHr": "Edit URL redirect", "VVTVas": "Name on card is required", "VXWFBi": "Arab Hesar <PERSON> / Bahary - Arab Elngoom", "VYd12T": "Recovered", "VZtmul": "<PERSON><PERSON><PERSON>", "VaJVoA": "{count, plural, =0 {} one {1 custom city needs} two {# custom cities need} few {# custom cities need} many {# custom cities need} other {# custom cities need}} to be mapped to Bosta", "VaT9Xe": "White bag flyer", "VajsWZ": "Are you sure you want to mark this order as fully paid?", "Vbm0+b": "Attribute values", "Vcg/Bg": "Is it required for the user to pick at least one product?", "Ve5UEh": "Robots.txt file updated", "Vf1ImJ": "GO LIVE", "Vf57Ie": "Partially fulfilled", "Vge+RX": "Footer", "Vh7xQ7": "Charge tax on this product", "VhUKkX": "Reviewer name", "VhZq7r": "User didn’t provide a personal email.", "Vhirq7": "{brand} ending {last4} -- Expires on {expMonth}/{expYear}", "Vi3fpw": "The cost of returning the package to the origin", "ViTqX7": "<b>Unlimited</b> products", "Virtual services": "Virtual services", "VjOWRo": "Saraboum", "VjZ6FK": "Unpublish", "Vjl0vA": "Add new category", "Vkzm9r": "ex. +20 xxx xxx xxxx", "VmFx/X": "This is a preview of a product in the CSV, make sure the data looks right. If it doesn’t look right, try to <link>Download Wuilt CSV template</link> and fill it with the right data", "VmaVhj": "<PERSON><PERSON>", "VnkQEx": "<PERSON><PERSON>", "Vo7DaB": "<PERSON><PERSON>", "Vp4mg4": "<PERSON><PERSON><PERSON>", "VpqhiZ": "Mark as default", "VtXFNz": "<PERSON><PERSON>", "VzLJXE": "Let customers open packages before accepting.", "VzQSOV": "<PERSON><PERSON><PERSON><PERSON>", "VzzYJk": "Create", "W+ILKJ": "You can edit your robots.txt file to tell search engines which pages of your site to crawl", "W/8cfm": "Dashboard quick tour", "W/G3/o": "Package material is required", "W/R2eB": "<span>{admin}</span> marked this order as paid", "W/qTnN": "Embed row deleted!", "W0KIfl": "Manual discount", "W0NpKz": "No tags available to remove", "W0hYbW": "Online payments", "W1r0Vu": "Store page", "W3+MEl": "New Currency", "W435jR": "City is required", "W6nwjo": "Draft", "W7VrxF": "You’ve reached the limit of 1 free store", "W8PJKA": "Note location", "W9kcoN": "Automatic discount", "WB4EXy": "(including 14% VAT)", "WB8A2Q": "Add Text row", "WC6prw": "Alexandria, Behira", "WCXadH": "<PERSON><PERSON><PERSON>", "WEMk2V": "Order payment is not settled yet", "WFfLAS": "Add your first packaging materials.", "WG3wjP": "Enter attribute name: ex. color", "WHgdsk": "Out of stock", "WIEJh0": "Preselected:", "WK62MN": "Fulfillment", "WKXVd0": "Customize your store look", "WLQUXg": "Main color", "WLicqk": "The total amount is invalid.", "WMqMj/": "Payout date", "WNJXiQ": "No shipping rates for this location, customers in this location will not be able to checkout.", "WPd54L": "Amount must be big than 0", "WQWB1Z": "Rates reset successfully", "WRfbhR": "<PERSON><PERSON><PERSON>", "WT5FlJ": "Leave a comment...", "WTocjo": "Card Number", "WTrOy3": "Cha<PERSON>", "WYy/Qw": "Select menu item type", "WZHz0h": "Please pick correct days", "WZVTOm": "Add a secondary button", "Wbeihq": "No products available", "WcchUa": "Before payment methods", "WeFTr2": "Paid amount", "WhLfzM": "Address Line 2", "WiKyhr": "Congratulations", "WjyfEH": "Enable/Disable Info bar", "WkrD/I": "Pickup locations", "WlAED4": "Secondary phone no.", "WlyxAh": "Unauthorized", "Wmof7J": "ElSehr w ElGamal", "WnQ/AZ": "Contact us", "WoqWRU": "<b>Grid</b>", "WpAR6a": "(In Arabic)", "WpEJtj": "Failed transaction", "WpHFfD": "Quantity is required and ust be greater than or equal one.", "WpdLpL": "Expiry date is invalid", "WqGYy0": "Apple Pay", "WqHwB4": "Order Receiver email is invalid.", "WtP9dm": "Marina delta", "WvAy4z": "2 Years", "Wve+DN": "Customers will enter this discount code at checkout.", "WvshSL": "Learn more how to import CSV", "WwUyCC": "Default rates for each zone", "WxVmEQ": "{customer} placed this order", "WyRch3": "Enter your Shopify store URL in the format: https://shopify-domain.com", "WypnXi": "Synchronize your products catalog with {integrationName}", "Wzjtsa": "Add page", "Wzu7xt": "New Redirect", "X/XZDD": "help monitor when customers don't respond to delivery attempts. If there's no response after the 3 trials, the package will be returned to the sender.", "X0ha1a": "Save changes", "X0xd7f": "Please enter a page name", "X2WRGJ": "This collection is empty", "X2xRj0": "Attribute Values", "X3W4KJ": "{<PERSON>Name} will no longer get updates from your store products. Are you sure you want to remove synchronization?", "X5qjVp": "Accept Online Payment!", "X6S+XJ": "Order is already refunded", "X7jl6w": "Orders", "X8Xn2V": "VAT Info", "X9KpIU": "Subdomain updated successfully", "X9lgvQ": "{name} edited the customer note", "XBoahx": "Internal server error", "XCWPjt": "Please select who will pay package opening fees", "XEGXMr": "Delivery outcomes", "XELInS": "Ask any question and get the help you need.", "XF4avs": "Here you will find your store customers and track their orders.", "XFsLQi": "Enter rate name", "XGCMAH": "Al Areesh", "XGZ8M9": "Created on {createdAt} and trial expires in {trialRemainingDays, plural, =0 {} one {1 day} two {# days} few {# days} many {# days} other {# days}}", "XJFkNc": "Disable product reviews?", "XJz+D5": "Automatic Discount", "XKbysY": "Are you sure you want to request materials from {companyName}?", "XNZ0gL": "Partially Refunded", "XO3NhR": "Update contact info failed", "XPruqs": "Order", "XPvwH2": "Navigation menu is limited to one root and two sub-levels only", "XQkudg": "You dont have any collections yet", "XRHyCb": "Value renamed!", "XSBwKk": "ORDER LIST ({count})", "XTFwJA": "PRODUCTS", "XUGjdA": "Custom tax must be greater than 0", "XVTo4F": "Are you sure you want to delete your saved payment method?", "XW1UC4": "Mark as fulfilled", "XW6Msg": "Failed to get items snapshot", "XY/5wo": "TOTAL", "XZtQ8o": "Account Id", "XaIebz": "Manage Attributes", "XafjQh": "ex. https://www.instagram.com/YOURSTORE", "XajgYb": "Custom Build Discount", "XdUnk4": "{numberOfOrders, plural, =0 {No orders} one {# order} two {# orders} few {# orders} many {# orders} other {# orders}}", "XgYo/k": "Failed Delivery Attempt", "XjS0ci": "Cancelled by shipper", "XlIbq2": "A template customized specifically for Farah Omar store", "XngFYo": "Free shipping", "XoW23L": "Something went wrong, try again!", "XqQbkD": "Account deleted", "XtkGQd": "Please select at least a collection first", "XvZocq": "Shipment on hold to cancel", "Y/gMNw": "Generate API Key", "Y/u8Ie": "Select state first", "Y47aYU": "Top up", "Y4PEKz": "75%OFF", "Y5QXnP": "Arab Investment Bank (AIB)", "Y63Z/a": "Shipping delivered", "Y64YCT": "Background color is required", "Y6CYdM": "Add your products & Start selling online today", "Y7WXOT": "Transaction history", "YAgYL6": "Tuesday", "YAnsyA": "Exception", "YCaHZK": "This option allows you to dive deeper in your shipping settings, and have more customizations & control over your shipping zones.", "YDMrKK": "Users", "YFo3lj": "Your order has been sent to {provider}", "YFuhAG": "Branch name must be a 10 digit Tin number for your provided VAT number", "YHnTZw": "Remaining products", "YLbte2": "Integrate your store with the best shipping couriers", "YMMj8b": "Variant \"{values}\" already exists.", "YN46JB": "Commercial International Bank - Egypt S.A.E (CIB)", "YOBkHs": "Add Zone", "YPL7RL": "Email subject", "YPyks+": "Total Store Products", "YQakqM": "No cash on delivery transactions are available", "YTIsbM": "Fees for order #{orderSerial}", "YVQvd0": "A techie and modern store theme is a simple and user-friendly design template for an online store.", "YWm1o1": "Enter the custom tax", "YXVx4p": "30 characters max", "YYU0VW": "Upgrade to Premium", "YYc9o3": "Category {index} Title", "YZ1Msb": "Use the correct language URL", "YbNZKz": "Page created!", "Ybntgn": "Manual order creation", "Yc1FL6": "Enter the old URL you’re directing traffic from, then add the new URL that you want people to find.", "YghXlk": "Abandoned checkouts will be highlighted here.", "Ygvi+I": "{companyName} deactivated successfully", "YjFu7+": "Changed email from <b>{oldEmail}</b> to <b>{newEmail}</b>", "Ymv5w0": "Rejected payout", "YqDUGY": "Transaction completed", "YvRMOE": "Filling information", "YwQVo6": "Your domain may take up to 24 hours to propagate", "Yx6py4": "Payment Status", "YzFeTP": "Text row deleted!", "YzHmAk": "Browse {integrationName} Products", "YzqIt1": "Add to Store", "Z+95A6": "No options available", "Z+d8/1": "Expiration Date", "Z2EXel": "<PERSON> as <PERSON><PERSON>", "Z2lH2f": "Shipping rate updated!", "Z2o8Ml": "Top selling products", "Z2zIp5": "24 hours", "Z32GIe": "Request return shipment", "Z3GpJF": "Current Plan", "Z7HFMi": "Create Category First", "Z8ZUV1": "Return options", "Z91V8I": "Automatic discount name", "ZA+T2U": "Custom states under {countryName} updated successfully", "ZAYXqz": "Update Custom States", "ZAu0Ww": "<PERSON><PERSON><PERSON> saved!", "ZCIRdA": "Al Maarakah", "ZERrR5": "<PERSON><PERSON><PERSON>", "ZErQkM": "Farm Walid Abd<PERSON> Had<PERSON>", "ZH0CeT": "Group destinations to a region and manage its shipping and delivery options.", "ZMQy8q": "ElShameya", "ZNxA6s": "ID must start with G-", "ZO4rep": "Welcome and a quick tour", "ZPOKis": "SLAs, fees & compensation", "ZPvMWi": "Customer’s name field", "ZQu5uq": "A 1% fee is applied after the order exceeds EGP 3,000", "ZR8OA4": "Sell in all languages", "ZRgmKp": "Max. file size 10MB - Max. products 100", "ZVZIGo": "There are no customers yet.", "ZWNAEJ": "Manage your payment methods", "ZXTJwY": "Values", "ZZQtvw": "Practical solution for setting up an online store quickly", "Ze6L7H": "Are you sure you want to delete the {rate} rate", "Zg1UtS": "Bani Salama Village", "Zht7uy": "Choose a name for this category", "Zi0SGl": "Payment intent creation failed", "ZibE+c": "Terms and conditions", "Zix8Dd": "{percentage}% Off total price", "ZjcGjW": "“Allow customers to open packages” option is now enabled", "ZkHra8": "Delete the option value from the linked products and try again.", "ZlnAQT": "PRODUCT CARD", "Zm0lmJ": "<PERSON><PERSON>", "ZmBop7": "Add collections to the row by clicking the <b>Add collection</b> button below", "ZmaTMT": "Heavy Bulky", "Zmbw2u": "Are you sure you want to revoke {name} permissions", "ZoLGcZ": "ex. Our Featured Banners", "ZoNNfI": "Slide {number}: Button 1 text", "Zpj6Qb": "You have registered your token. Here is your secret token key! Make sure you copy this down, because if you lose it you will need to re-create the token.", "Zq0WB2": "Export Orders", "ZqeP7G": "Videos links", "Zsd9Ac": "Remove background", "Zt72kF": "Aramex doesn’t allow editing the Airway bill amount for ongoing orders.", "Zu3fj5": "The United Bank (UB)", "ZuBQCr": "The integration is installed successfully", "ZvbM2O": "Slide {number}", "ZwGl1S": "3-4 working days", "ZxZl0s": "Status refreshed successfully", "ZyETY/": "Importing Products...", "ZyK0a1": "Enter attribute name", "ZyYVlP": "Add your social media links", "Zynb+q": "Collection title is required", "ZzcKBd": "Order is paid online", "Zzk5aY": "Audi Bank (AUDI)", "a+MUqU": "EMAIL TO", "a+YPw/": "Search by {searchOption}", "a/4SMm": "Required field", "a0nizF": "ex. https://t.me/YOURSTORE", "a12jU8": "When a customer pays online, the payment will show here.", "a1fAXn": "STATUS", "a2XxMN": "<span>Marked as fulfilled – </span>All items have been fulfilled", "a5UA8B": "Extra COD collection fees", "a60dYr": "This user is not registered in Wuilt", "a8npXI": "Uninstall", "a98egg": "Customer updated!", "a9NQok": "This process will remove all {integrationName} products. Are you sure you want to remove them?", "a9spen": "Your customers should be able to checkout using", "aA8bDw": "Text", "aAMkSq": "Pick up", "aAsX76": "White bag Flyer", "aE8x+V": "Simple product", "aFyu8N": "Translations", "aHAp3A": "Already have a store?", "aIRWQB": "Back to home", "aIt/bF": "Page sections", "aJ3PA0": "Alkarnak", "aKosVg": "Scheduled reminder", "aL34mn": "Need Permission from Sharm Elsheikh Authorities", "aO+/x2": "<PERSON><PERSON>", "aPZkOF": "For which is higher, the actual or volumetric weight", "aPcvs8": "ex https://google.com", "aUYCGS": "Select at least one state", "aWieK7": "Delivered orders", "aWpBzj": "Show more", "aXh5Uj": "Branch name", "aZBX3P": "Add the destinations in this zone:", "aZaay4": "First Abu Dhabi Bank (FAB)", "aarGAL": "Keep up to date with all wallet transactions, add balance, withdraw and manage from here.", "acrOoz": "Continue", "ad7I3+": "Add Coupon", "afb2Tb": "Upgrade your store & Start selling today!", "agAEKD": "<span>{name}</span> changed the shipping company", "agOXPD": "Size", "agY9Hm": "Manage your pick up locations.", "ahXIwQ": "Edit contact information", "ahYUH3": "ElQata", "aheQdn": "Last Name", "ai4U+7": "Add Options", "aiPpzN": "Menu item type", "aj7lV2": "Attribute updated successfully", "aj8kcD": "Enter the pickup address", "ajU+HT": "Please enter a valid URL", "akYhUt": "After Kilo 107", "aknANV": "CIB | Commercial International Bank", "alBFIH": "<b>4-level</b> custom shipping zones", "anK7jD": "Product Type", "aoVttg": "Add e-mail", "aofVJR": "Suez", "aqEKH7": "Add row", "ar": "Arabic", "arPp4e": "Terms & Conditions", "arQ2Oo": "Delivery min cycle (Al Wadi Al Gaded, Saini governates)", "atLE7b": "This Field is required", "ataTev": "Unfulfilled", "auglGx": "Formats: .jpg, .png, .webp, and .gif", "axU6Ap": "<PERSON><PERSON><PERSON>", "azRYQJ": "Enter rate cost", "b+KKbu": "Changes saved!", "b/Wtd4": "Online payment transaction", "b/a37E": "Customers will get a discount if they enter a code at checkout.", "b/bIDJ": "Option value deleted!", "b1+rCl": "Enter card number", "b1zuN9": "Price", "b3s/ZD": "Small Box", "b4GGYZ": "Add Images", "b5Vn/Q": "Select font style", "b7YdZ/": "Tables", "b7olOU": "Not Authorized!", "b7wHG1": "Not selected", "b87Hsg": "Attribute {number, number}", "b8l7ZE": "Tag already exists", "bB+ACm": "Please enter your address line 2", "bBmtkL": "Product image zoom", "bHejJZ": "Tobacco Products", "bIBQAU": "Albangar", "bIeY2r": "Add Banners row", "bLn5W5": "Tawfeek Al Hakeem", "bLv3zP": "Pickup location not found", "bOrtHF": "Elsadat & Elkhatatba", "bPzVII": "please try again later", "bQGS7N": "This product is no longer available, <b>please remove it from your cart</b>", "bQkqCi": "{provider}", "bRtQ2Y": "Rejected by <PERSON><PERSON> member", "bSmPif": "The COD amount should be less than or equal 30000 EGP.", "bTkkGa": "Expired Stores", "bULE9C": "Online Store", "bW7B87": "New", "bYaz6A": "Buy from selected products / collections and get {discountValue} discount", "bYw3IN": "Deleting Address", "bZ/qLD": "Total sales", "baMhAF": "Determine the amount of either money or items your customer should buy to get an offer.", "bh2Vi9": "Packing Slip", "bhCuWC": "{quantity, plural, =0 {} one{1 Item} two{2 Items} few{# Items} many{# Items} other{# Items}}", "bhr/Bf": "Twice a week (Tuesday & Thursday)", "bi1Jzn": "Tell us where your business is based.", "biq8aT": "Cancel request", "bkLZ+q": "Add Attributes row", "bkcuXM": "Edit Slide", "bnKt93": "Import Product Translation", "boAvtT": "Shipping providers", "boVP9a": "Send new order notifications to", "bogZif": "Ezbet ElOla", "bq10Zi": "See all selected collections ({count})", "bqbyWS": "Status COD", "bsviKS": "Abu Swir Desert", "btMJn6": "No zoom", "bw4Ohj": "Each option must contains values", "bwU4ue": "Generate api key empty state placeholder", "bxswus": "Changed phone from <b>{oldPhone}</b> to <b>{newPhone}</b>", "byP6IC": "Selected", "c/Nj0P": "• {count} states", "c0Ic6m": "Credit Agricole Egypt S.A.E (CAE)", "c1pbvZ": "Enter custom region name", "c1zaMK": "Please enter your address", "c2rkAO": "Success! Order marked as fully paid.", "c4my4r": "SEO Title", "c516y1": "Not-Quantifiable", "c7/79+": "Billing Address", "c8NRYr": "Terminated", "c8nvms": "Sales", "cAsDnK": "Collection Products", "cAvx8K": "COD fees threshhold", "cDWp7t": "Need help importing products?", "cDwFID": "Invalid address", "cGkfPl": "Chat app integrations (<PERSON><PERSON><PERSON>, <PERSON>, others)", "cIlJHA": "<PERSON><PERSON><PERSON>", "cJv5W4": "Processing payout", "cKJMSt": "Automatic withdrawals won't process if your wallet balance is under EGP 100.", "cKXXDA": "Supported platforms", "cKjLYA": "There was a problem with order #{orderSerial}", "cM+uf3": "Edit existing tags and add new ones.", "cNiKNu": "My Stores", "cNj3kR": "Delete Variant", "cOpHew": "I confirm that I have copied this key and stored it safely.", "cRS526": "Slide {number}: Sub-heading", "cRo+pN": "Current Quarter", "cRvwin": "<PERSON><PERSON>", "cSJaln": "Upgrade and Save with a Premium Plan", "cU/aSG": "Email is required", "cUDRdV": "<PERSON><PERSON><PERSON>", "cX6Emb": "Export Products / Orders", "cXAlMR": "Scheduled", "cXxoKh": "ElWahat ElBaharia", "cY/99u": "Update your bank account information.", "cb2kY6": "Standard", "cb3r27": "Add or remove additional fields to your checkout to get more info from your customers", "ccKjqi": "Terms and condition", "ccXLVi": "Category", "cd3soI": "Customer added!", "cfeaip": "Partially Fulfilled ({count}/{totalCount})", "cfvMAF": "Port Said", "ciGlTY": "Meta Analytics", "cicU+X": "You are at grace period if you didn't renew your store will be closed in {remainingDays} days", "cieiJe": "Light Bulky", "ckOOUo": "Analytics & Remarketing", "cloWBl": "Remove {countryName}", "cn/Y3f": "Max pickup locations reached", "cpbq3q": "Show attribute values names", "cpy/FA": "There are no orders matching your filters", "cqAOoB": "Browse Products to Add", "cqk3OB": "Manage your payout settings", "csERj+": "Upgrade your account to add custom product", "ctKx7u": "Add/Remove Countries", "ctzR4h": "ex. <EMAIL>", "cuYOL+": "Please type in your existing domain", "cupNx+": "<PERSON><PERSON>", "cuuwR9": "Airway bills cost", "cvYKN+": "Instapay address", "cwBsV0": "Are you sure you want to delete this email?", "cwKerS": "Enter the amount to update payment status.", "cyR7Kh": "Back", "cyT7Oe": "Valu", "d+chsQ": "<b>In Progress:</b> Orders that have been picked up and are currently in the delivery process.", "d+qBTO": "Shipment already exists", "d/SCHW": "Customer note", "d0OyGU": "Please enter your address line 1", "d1OgED": "(optional)", "d1lG6R": "Edit Option", "d3LTWQ": "Join now", "d6EBBt": "Choose city", "d7NxxQ": "Last Quarter", "dEWqm1": "Your plan supports only 1 social integration Upgrade your plans to add more", "dG1+Gz": "No values added", "dJvuV+": "What is a domain?", "dLxdob": "Go to Customer", "dM+p3/": "From", "dM9/ys": "Pay by Credit Card", "dMvvqr": "Sold", "dPOVJT": "Al Wadi Al Gaded, North Sinai, Abu Simbel, Marsa <PERSON>, Salloum, Halayeb & Shlateen", "dSrbsn": "<PERSON><PERSON><PERSON>", "dTR5nN": "at", "dTTvAy": "Elazab", "dTW4m3": "Add a note", "dUuT/n": "Insurance fee", "dUxyza": "Reviews", "dVGrZ+": "Made by wuilt", "dVYa4v": "Duplicate SKUs", "dWSl0r": "Start your first transaction now and track its status here.", "dXVOqs": "Your plan doesn't include Google Tag Manager", "dYAmsc": "Plan Start Date", "daRLFn": "Currencies.", "db4WMW": "<PERSON><PERSON><PERSON>", "dd7NBe": "Total amount", "de": "Germany", "de5d1I": "Pickup due date invalid", "dfldGw": "Zone name", "dgXcwY": "Show star rating", "dlbSHt": "Show overall rating", "dmEurm": "<b>Unlimited</b> Online Payment integrations", "doUGPC": "Are you sure you want to update the rates prices?", "dpBfxT": "Delivery Delay", "dpfx1q": "Page updated successfully", "dqD39h": "Yearly", "dqcoBp": "Bosta App Settings", "drJdjj": "Send now", "drqP2L": "Delivery", "dtRQ8c": "Fractional values are not supported.", "dwxK0O": "Manage Online Stores", "dxkC2H": "{count, plural, =0 {} one {<b>1</b> Video} two {<b>#</b> Videos} few {<b>#</b> Videos} many {<b>#</b> Videos} other {<b>#</b> Videos}}", "dy8l6W": "Haram", "dyElqO": "Expiry", "e+x83a": "Sobhi Hussein Area in Badr", "e/U0Xt": "Conversion Rate", "e0kEtS": "Deactivate", "e1CWwo": "on all websites and store plans!", "e1T0e+": "{amount} {currency} is paid via online payment", "e2DBWR": "Tag deleted successfully", "e61Jf3": "Coming soon", "e6Ph5+": "Address", "e85oKA": "Google Analytics is now connected", "e8Eciw": "60 characters max", "eCRVyQ": "Cart recovery email canceled", "eCSLBP": "The order subtotal must be more than {minimum} to apply the coupon", "eDqTnS": "{integrationName} will no longer get updates from your online orders. Are you sure you want to remove integration?", "eF5yVi": "<PERSON> (Store owner)", "eFJAme": "Payment gateway not supported", "eHb83/": "<b>Unsuccessful Orders:</b> Orders that failed to be delivered, including returns.", "eJsh5s": "Google Tag Manager is dis-connected", "eKEL/g": "Pending", "eKv7yX": "Post", "eME53n": "Full Permissions", "eMco4Q": "Purchase a New Domain", "eRI5qW": "Sub-heading", "eTJOP5": "<PERSON><PERSON><PERSON>", "eTrzQC": "Shipment created", "eTsv6Z": "<PERSON><PERSON>", "eU2xVT": "Medium flyer/box", "eUvPlA": "{count, plural, =0 {Not Used} one {One Time} two {# Times} few {# Times} many {# Times} other {# Times}}", "eV+kxi": "View Reports", "eVXxQl": "Customers won’t see this", "eXa3fC": "14px-30px", "eYjo8Y": "Select font family", "ebfMvW": "Selected collections ({count})", "ecjkjo": "Assuit", "eckDTw": "Amount must be less than or equal to order total", "ef/rGP": "Customer location is required", "ejEGdx": "Home", "ekPvxn": "Chargable weight", "ekXood": "Unlimited", "ekzu1Q": "Category Type", "elQY/1": "Product status", "en": "English", "en+bd/": "Changed from <b>“{oldNote}”</b> to <b>“{newNote}”</b>", "ep6n1x": "Shipping region level disabled successfully", "eqoPAh": "Orders per page", "er7gIB": "Refund Payment", "euu9C3": "You won't be able to use some features that are only available in Egypt", "exZ4LO": "Display products from", "eyaAmf": "<PERSON><PERSON>", "f/2YYl": "Embed row updated!", "f/5qfF": "North Coast", "f/Z/Ow": "Your current plan supports 100 products only", "f/ufhi": "Pickup <PERSON>", "f0Dz83": "Package type", "f0xvtz": "Transaction amount", "f1f2u+": "Send to {provider}", "f2grRI": "Click on your location on the map.", "f3FAoF": "COD", "f4XE+Q": "Shipping trials", "f5Jwfw": "Edit Video row", "f6mWZm": "Oops, An Error occurred. Please recheck the access token", "fAk1L9": "Open {provider}", "fAsNma": "-", "fBg+7V": "Navigation", "fC3oaj": "Please enter a valid API key", "fD5hgG": "Customer pays {returnAmountWithShipping}.", "fDZLuF": "Card number is required", "fF376U": "Current", "fFGX4T": "Wallet charge failed", "fFxkhN": "Upload the transaction invoice to continue.", "fG6dRl": "Enter a promocode", "fIHFDI": "Add New Address", "fJiPMz": "Store name is required", "fNCCY7": "Basic", "fNDSBc": "Deleting Option", "fPjZq6": "You can edit the product prices in the variants card below", "fPnSbH": "Refund order", "fQgXaX": "Re-attempt delivery", "fQjwhV": "Basic Info", "fUs13Y": "Go to Fatoora Portal", "fV1FU+": "How to add and manage products?", "fVOSe4": "Show review date", "fX68Ra": "Reply to review", "fXD9Z/": "Browse Collections", "fY5zre": "Images deleted!", "fZKX/J": "Payout settings not verified", "fb9/Ow": "Product added!", "fdj+YC": "Please specify the minimum purchase amount", "febMqF": "El <PERSON>", "ff0aT0": "Email body", "fiC9xB": "Shipping Policy", "fjnihG": "Shipping discount value must be less than the shipping amount", "fkzH83": "Add attribute", "fl7IPM": "Coupon settings", "flSkCD": "Enter the maximum selections count", "flY/kL": "Price changed", "fm3DcF": "Select payment method", "fncQOs": "Define your shipping regions and how rates are calculated.", "fp3A/1": "Edit customer note", "fr": "French", "frGFyl": "Online payment is disabled", "fs4Dm/": "Return Request Related", "fsEzmr": "Edit Attributes row", "fsJQTh": "2 Staff accounts", "fuV9Zv": "Return to origin", "fv4NCr": "A 2% transaction fee + 14% VAT will be added", "fvXPL0": "ElKar<PERSON>un", "fvyO5Z": "Rejected by shipper", "fx1gKM": "Edit tag", "fxpYZ/": "Pick a plan to use when your free trial ends", "fyKxQd": "Category max selection exceeded", "g/WM7K": "Refund payment", "g/WR9S": "APPEARANCE", "g/jo36": "Save my information for a faster checkout", "g1+jjU": "Invalid secondary phone", "g1TrCF": "Attribute value image updated!", "g3BP0J": "Cash On Delivery (COD)", "g5pX+a": "About", "g6GVjH": "<PERSON><PERSON>", "g73c7c": "Clean, Minimalist design", "g7ZE8q": "2% + 4 EGP fees are applied.", "g9/szf": "Provider not available", "g9HKeP": "Manage the integrations on your store or add new ones.", "gAcvpF": "Name for the attribute (shown on the front-end).", "gBjzL4": "Optimonk Installed successfully", "gCZp3S": "Edit menu item", "gDmRyR": "Integrate {integrationName} with Your Store", "gE8wjz": "{integrationName} will no longer get updates from your online store. Are you sure you want to remove integration?", "gEAcAn": "Available in", "gG6C6W": "Custom regions under {cityName} updated successfully", "gHbe/K": "View store website", "gHjIcU": "<PERSON><PERSON><PERSON>", "gIITk0": "Reactivated successfully", "gJbfns": "E-mail is required", "gKKPxX": "Item maximum quantity is rquired", "gLGimh": "Deleting Installation", "gMwtEe": "Name on card", "gOc4On": "NOTES", "gPFV5C": "You have made some changes which will be lost if you leave this page.", "gPNRZr": "Invalid shipping details", "gQOBZG": "Top up your wallet", "gSEK50": "Fireworks.", "gSWME1": "Edit Credentials", "gTeQX2": "Sharqia", "gTr0qE": "Shipping Settings", "gTvssF": "Upgrade Store", "gU9tN0": "Click to show", "gUIhhI": "Search for products", "gWnmbW": "Page {currentPage} of {numberOfPages}", "gX5Z6B": "View your plan subscription information.", "gY2aPp": "Show number of reviews", "gYqY0d": "Customer buys", "gZTn5O": "Promotional Credits", "gZtXyn": "Large Box", "gaN+xB": "Attributes row deleted!", "gbABaJ": "Customer/E-mail", "gbDZer": "Delete Discount", "gcbFWX": "Search or Choose product", "gcuDcH": "<PERSON><PERSON>", "geC7KY": "Add your contact info so people can get in touch. This email will be used to notify you about activities on your site.", "genK9M": "Governorate/City", "gi21ny": "There are no orders", "gjBiyj": "Loading...", "gjwFsT": "Select date", "gk6SAm": "Shopify", "gkwxbs": "Display your featured products list in the homepage", "gl816G": "Update order", "glp0g5": "The Refund COD amount should be less than or equal 30000 EGP.", "gnK4sY": "Add Slide", "gqSjEy": "Pickup location name is required", "gqZ9PA": "Add attributes to this product, they are useful for searching and filtering products, also they will be displayed under “Additional Info.” in the product page", "grwZlV": "1 Year", "gs2IGT": "Add a note in payment page", "gsVcPm": "Add your first collection description", "gtWFrR": "Request all your {companyName} packaging materials based on your shipping needs.", "gw90Mf": "<PERSON><PERSON>", "gzSA8V": "PAST DUE", "gzcDlE": "Bank account", "h+OGOs": "Alomrah", "h/P1Qx": "No Reviews", "h/qjhb": "Products Sorted!", "h1xklL": "Domain verified successfully!", "h3wzcw": "Order amount", "h4pocV": "New Orders", "h5P++h": "Variants", "h6ATMR": "Add Manual \"{amount} {currency}\" Discount to total order value", "h9vCBR": "1 Hour", "hABk/Y": "Easily ship your products with Wuilt Shipments or on your own.", "hAyYW1": "Purchase Failure.", "hCCWuZ": "Previous period", "hCVRXo": "Refund fees", "hE2CZo": "ElSaaraneyah", "hFtZBL": "Inconsistent cart limits", "hHQCHx": "Weights over 30 kg per Piece", "hJZwTS": "Email address", "hKL3+S": "el maadya el q<PERSON>ya", "hMRP6J": "<PERSON><PERSON><PERSON> Address", "hNxn6L": "Use this to import translation of your products, collections & variants", "hORzNo": "Refunded transaction", "hOxIeP": "Availability", "hP7/Mn": "Customize your checkout fields", "hQdIYt": "No material orders", "hRhXhv": "Url is required", "hSI3Bs": "Product options must be unique", "hUWqr2": "Disable your store temporarily", "hVeZ0H": "El sayadla", "hYOE+U": "Invite", "hZOGLS": "Contact Us", "hZUiYv": "Select a customer", "hZuKQX": "No. of products to show per row", "ha65BW": "Shipping rates", "haiTdR": "Note: After canceling the shipment, make sure it has been deleted from the shipping provider dashboard", "hawDdJ": "Enter its maximum quantity", "hbSqJA": "Bank", "hcQynA": "No redirections added", "hd3SAy": "Phone and chat support", "hdEKbW": "Bicycles", "hdJy12": "Add custom city", "hf/tV5": "Alshalofa", "hfq+Jd": "This will be applied in product card and product page.", "hgzBJQ": "Abis Eltanya - Bab <PERSON>beed", "hi/icp": "Request return airway bill", "hi0lH4": "Add your shipment pickup location", "hiBcId": "Out of Stock", "hj9T4h": "Comment added", "hkENym": "Customer", "hkmZ2g": "Popular free themes", "hmZ3Bz": "Media", "hmnIY5": "Card", "hn9+SK": "Enter attribute value name", "hnDAWv": "{name} updated customer details", "hndrd4": "Your customer will pay this amount to courier upon delivery.", "hqfaK1": "Product Options", "hrDzK8": "Contact Vodafone.", "hrgo+E": "Archive", "hrqySY": "Store homepage", "hv+n31": "Deduction transaction", "hzHVYP": "Add tags", "hzSNj4": "Dashboard", "i/D+2N": "Who will pay package opening fees (EGP {amount})?", "i/WTxP": "Please choose a background color", "i2OlE9": "There is no risk—if <PERSON><PERSON> isn’t right for you, cancel before your trial ends and we won’t charge you.", "i3P6ZD": "Created at:", "i4kTeU": "Ezbet ElJurf", "i77djx": "Sinai", "i7IJCd": "<PERSON><PERSON><PERSON>", "i8Gkfz": "<PERSON><PERSON> not sent error", "iBIxIe": "Bahr Elbaqr", "iDcBt4": "on plus and advanced store plans", "iDxOQY": "Arab Bank PLC (ARAB)", "iES+1d": "Page URL/Slug", "iEg/g/": "Mobile View", "iFJfKb": "Are you sure you want to cancel the withdraw request?", "iGM3Fl": "Migrate from EasyOrders", "iGczYw": "Add new variant", "iHwHRZ": "Manual Discount", "iI8fEr": "Are you sure you want to cancel the material order?", "iJnmw0": "Custom States", "iL/4z9": "<PERSON><PERSON> Quantity", "iMkDy9": "Attributes let you define extra product data, such as size or color, and displayed in the product details page", "iNFUyB": "You’re editing the rate cost of {count} areas under “{rateName}”", "iPu0PX": "The payment failed.", "iRszAs": "<b>{count}</b> Pending pickup", "iStn7+": "<PERSON><PERSON><PERSON>", "iV4DqQ": "Deleting Rate", "iW0onJ": "Shorouk", "iYc3Ld": "Payments", "iZFIgR": "Go live description", "iaGWE4": "El Wadi El Gaded", "ic1Itt": "Unsuccessful orders", "idkGk/": "Pick a date", "ii+G4b": "Zone name is required", "iiRArd": "Cod amount exceeded", "iisqZH": "Update shipping zone", "ikmzHi": "ex. https://www.twitter.com/YOURSTORE", "ilBVSc": "Sort options", "ilmhUO": "Grab a coffee while we work our magic! ☕✨", "im9OV5": "SKU: {sku}", "iorQ6+": "Search pages", "ipApG1": "Wrong Delivery Address", "ipKshO": "Free Trials", "irFBKn": "Last 7 days", "irlU6C": "Total / To be paid by customer", "iux08V": "CONTACT INFO", "ivSl4l": "Enter name on card", "iwYn+1": "Deleting Automatic Discount", "ix27PQ": "Youtube", "ixiJlg": "Edit Order", "iydw3u": "Enter a valid phone number", "izFXq5": "<PERSON><PERSON><PERSON><PERSON>", "izVrvV": "Mobile Image", "j+9DQX": "Footer Start Part", "j/gENh": "Something went wrong, try later", "j696Wt": "Unsuccessful", "j6KiRW": "Manage your wallet settings", "j6oRf7": "Package size", "j73Ncy": "Bad request error", "j747au": "Giza", "j7M9od": "copy", "j7WSfa": "Unsaved changes", "j7k26f": "Image sorted!", "j8j5mK": "Category Image", "j9cOh/": "{brand} ending {last4}", "j9yWeF": "You can edit the product inventory in the variants card below", "jBHHhS": "No items in the abandoned checkout.", "jEz2pD": "Invalid checkout postcode", "jG+0CX": "Stock: {quantity}", "jGi3Y4": "Text color", "jIAbpO": "Automatic discount deleted!", "jKBllh": "Access & Permissions", "jKjpV0": "{count, plural, =0 {} one {Download attachment} two {Download # attachments} few {Download # attachments} other {Download # attachments}}", "jKqEBQ": "Select attributes to be added", "jLexZ3": "Approved by <PERSON><PERSON> member", "jN0bGo": "<PERSON><PERSON><PERSON>", "jOJrgb": "Product prices: Changing the currency will update the prices of all products in the store.", "jOZ5Ow": "This controls which mailchimp list will we add new customers to", "jPj0sd": "{reviewsCount, plural, =0 {0 review} one {1 review} two {# reviews} few {# reviews} many {# reviews} other {# reviews}}", "jSqwdW": "Coverage", "jT6/da": "Heading", "jU3fsF": "Amount is required", "jY+f2f": "Fulfilled", "jYSkjR": "Let your visitors get to know you.", "jZe+ZZ": "Use the URL redirect manager to Send site visitors and search engines from an old URL to a new URL", "jacl08": "Customer gets", "jbwIqs": "Add/Edit payment methods in your store.", "jdJhOL": "Phone number", "jeHgIR": "Slide {number}: Heading", "jf4hZs": "el anabra", "jfUy1h": "Width (cm)", "jhPmSa": "Full Access", "jhw6Z1": "Cairo", "jj841B": "<PERSON>", "jm/spn": "Reset", "joRakM": "Mandatory", "jomXBE": "Picked up", "jq72o8": "Try importing the CSV file again after fixing the errors.", "jsBzHH": "Add image", "jsPOkc": "Wadi <PERSON>", "juAYBw": "Sent to", "jv7oHw": "Change currency", "jvo0vs": "Save", "jx0Iac": "<PERSON><PERSON>", "jyDj6Q": "Attributes row updated!", "jycvf5": "Egypt Post (POST)", "jzNy6B": "Rows ‘<b>{rowNumbers}</b>’ with value ‘<b>{productNameOrHandle}</b>’", "k+Ob6z": "{count} Selected Rates", "k+jeZe": "Rates updated successfully", "k2yT4Z": "Ship with", "k4IyJs": "Banque Du Caire (BDC)", "k4Y4eu": "Active:", "k4brJy": "SKU", "k6HOPg": "Cost Price", "k6q5b7": "ElNegeilah", "k81S1y": "Logo", "k96i2p": "You've reached the limit of 1 free store", "kAd6nI": "Domain is not verified yet", "kBX6Za": "Invalid sale price", "kBYwYT": "X Large flyer/box", "kD7YCt": "Image uploaded!", "kDNmhM": "Delta", "kEnYwD": "Pickup address cannot contain special characters except '-'", "kFXHJl": "DESIGN", "kGQJcD": "Reorder", "kIW/Ua": "E-mail address", "kNMal8": "Zone Name", "kPRvA1": "Legal page deleted!", "kPsWRL": "Renewal Date", "kQ+nLP": "Toshka Ganoob <PERSON>", "kQ5GLu": "X Large Flyer", "kQBCOm": "Selected abandoned checkouts", "kQGnis": "Store Starter", "kS0iHl": "Please select at least one attribute value", "kW0X61": "CATEGORIES", "kYq7/K": "Enable City Level first to enable Region Level", "kaPKOB": "Insufficient balance", "kbLBuY": "Profit", "kd+ih8": "Are you sure you want to delete the pickup location?", "ker84L": "Replace cart items failed", "kfr95E": "Address Line 1", "kg2MlN": "(Default language)", "khKGGZ": "Category maximum selections is rquired", "khVfqL": "Review date", "ki9zMn": "Are you sure you want to remove the selected tags from all the chosen orders?", "kiQYHP": "Length (cm)", "kiy0gr": "{count} products", "kkHagW": "Favicon", "klawy+": "Link your own domain name", "klvJow": "Change Domain", "kpU5Au": "Export Products", "kpkJdT": "Confirm Payment", "ku+mDU": "State", "kuRtXR": "el tarfaya", "kvCiiF": "<PERSON><PERSON><PERSON> Offer", "kwcnPE": "Product Collection", "kxKV+f": "ElArq<PERSON>b", "kzaL3E": "10 working days", "l/bkKR": "Base price <span>EGP {baseCost}</span>", "l1xfKj": "Egyptian Arab Land Bank (EALB)", "l3b3Nb": "Liqueur", "l5WTPf": "Light bulky (Package size)", "l7M68y": "Zip/Postal Code", "l7MDVY": "The refund amount will be deducted from your wallet balance and cannot be undone - are you sure you want to proceed?", "l7oSTT": "Sharp Weapons (Sword, Dagger, etc.)", "lA2dqw": "Add a banner by clicking the <b>Add banner</b> button.", "lAELLj": "Nasr City", "lCILNz": "Buy Now", "lD3+8a": "Pay", "lE7Cix": "Customer address is required", "lEMUo8": "Regional Settings", "lEzG/K": "Customer full name is required", "lFV+8c": "Order total changed from \"{oldTotal} {currency}\" to \"{newTotal} {currency}\" , Reason: \"{reason}\"", "lFWtoL": "New URL should start with your store domain", "lG1DMH": "No customers", "lGEsV6": "Add options for your products", "lGUhTY": "Enter Fatoora portal OTP", "lGYx7E": "SEO settings", "lGdpjv": "Make your first payment to add your first payment method.", "lJEnpw": "Left", "lKv8ex": "<PERSON><PERSON><PERSON>", "lL36Bf": "Collections to show per row", "lNZuWl": "All orders", "lNv985": "Invalid promcode", "lOdoIb": "Plus", "lOzDTt": "Congratulations!", "lRF78c": "Your product data has been successfully converted, try to <link>Download your CSV file</link> containing all products migrated from <span>{commerceName}</span>", "lRagR6": "Blom Bank (BLOM)", "lTuFmE": "Go to the DNS Management for your domain.", "lU8zgp": "Applied manual {discountCurrency}{discountValue}{discountPercentage} to shipping Discount", "lWVgwX": "Option value", "lXovqo": "Partially Fulfilled", "la8vaF": "Banners row deleted!", "laMijF": "Footer Type", "lbIZxN": "{count, plural, =0 {} one {<b>1</b> Product} two {<b>#</b> Products} few {<b>#</b> Products} many {<b>#</b> Products} other {<b>#</b> Products}} from <b>{collectionName}</b>", "lcMPaK": "Products Status Is Changed to {status}", "lckB6U": "Collections to display", "lf+twp": "No orders", "lf2D/K": "Pick a time, and a Wuilt expert will contact you", "lf2nW0": "Arrived pickup", "lfJMSu": "Discount percentage is required", "lh3to7": "Search or Choose Customer", "liHutq": "Apartment, suite, Unit etc.", "livOMR": "TTL", "ljxw1z": "All customers", "lllk8K": "Add Manual \"{percentage}%\" Discount to total order value", "lnaWo/": "Region", "lrOVHY": "Visit the Help Center", "lss6VS": "Compare plans", "lt5vyL": "<PERSON><PERSON><PERSON>", "ltAD5R": "Product listing, detail, order confirmation, and checkout pages", "ltSmln": "Shipping zone deleted", "ltTmWc": "<PERSON><PERSON>", "ltZbDq": "Invalid record(s)", "ltb+54": "Assign another company to deliver the order.", "lwSbLf": "Wuilt Shipments Part 2", "lwSqme": "Generate legal pages", "lxZOTj": "Agree and continue", "m0F/d2": "COD Amount", "m0lA2s": "Update Permissions", "m0medu": "ex 01x xxx xxxx", "m1grT/": "How To Add Store homepage?", "m23EPQ": "Deleting Products", "m4dLxA": "Explore all shipping details about {shippingCompany}", "m5MD2q": "Delivery min cycle (Upper Egypt, Touristique (Qena, Luxor, Aswan, Red Sea, South Sinai, North Coast, Matrouh))", "m65+3w": "Add menu item", "m6sBul": "{count, plural, =0 {} one {1 item} two {# items} few {# items} many {# items} other {# items}}", "m8g4Ty": "Search reviews", "m9x7ro": "Wuilt Shipments Part 1", "mAuEI1": "Wrong Pickup Location", "mB8Cy2": "your domain {domain} is not available", "mBdBun": "{columnsCount} columns", "mBkJYe": "Create a new store", "mBxmG8": "Delivery min cycle (Cairo & Giza)", "mE3gXi": "Discount type is required", "mEZHr1": "Create first collection", "mF9ktD": "Tags updated successfully", "mH6i6b": "Control how product images zoom works on your product pages", "mHydVl": "Failed to add contact info", "mJC4DB": "X Large Box", "mJT+7o": "Total Orders", "mJq8xd": "Company compensation policy", "mJur65": "Promo code not valid", "mK2WsH": "Max. per cart", "mK9dEc": "el aaqola", "mKXmr2": "Value added!", "mNvle2": "variants", "mOE4QC": "Vehicles Spare-parts & Tires.", "mOFG3K": "Start", "mPWeT6": "Add new Credit/Debit Card", "mPpkGg": "Add Shipping \"{percentage}%\" Discount to total Shipping value", "mQaM9d": "Enter account number", "mR6ggl": "Add one to withdraw your money.", "mRX42F": "<b>2</b> Online Payment integrations", "mYFxrB": "Write your store title", "mYIjhi": "Fonts", "mYYZdS": "Select a store to upgrade", "mZ4BvF": "Please assign some permissions for this user", "mc8teO": "Weight (kg)", "mce1wb": "You're about to switch the store currency from EGP.", "mcxDAU": "Online Store", "mdHVUZ": "<PERSON><PERSON>", "mfgE3v": "Please choose a text color", "mfhtV7": "Delivery date", "mgaB0k": "<PERSON><PERSON><PERSON> / <PERSON><PERSON>", "mhypMF": "Invalid cart min", "mmPSWH": "Read Only", "moJBvO": "Map you custom shipping cities to ensure correct order data syncing to Bosta", "mqTVmO": "<PERSON><PERSON>", "msfaNv": "Invalid regular price", "mto+d9": "The amount has been deducted from your wallet balance, and transaction details have been updated.", "muc/r9": "To change rate value, you need to activate advanced settings", "mx7ve/": "{cardType} ending in {lastDigits}", "my1MLn": "Shopping", "mzTA9J": "Invalid Transaction Details.", "mzX5yw": "Tax percentage must be greater than 0", "n+Gwbu": "Discounts", "n29Xud": "Free Shipping", "n2cM4h": "Amount Off", "n5QvJy": "Start date", "n5pCwD": "CONTACT", "n7uKz5": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "n8n5en": "ex. Our Featured Attributes", "n9PTXk": "ex. Our Featured Embeds", "n9fdaJ": "Unverified", "n9sEeb": "Failed to print airway bills {message}", "nA4XGc": "(Optional)", "nAzXpi": "Select variant", "nBRSfI": "{count, plural, =0 {There are no countries} one {One country is} two {# countries are} few {# countries are} many {# countries are} other {# countries are}} covered by your shipping zones", "nC9Yxs": "Edit the regional settings for your store.", "nCARbK": "Agiba", "nEMwDk": "Partially Paid", "nF3xy6": "Product Data", "nF55eB": "<PERSON><PERSON><PERSON>", "nFBiST": "<PERSON><PERSON><PERSON>", "nFQbxh": "Payment Method", "nFTi+N": "techie and modern template", "nFtBmT": "Meta Pixel is updated", "nIwarN": "See all selected products ({count})", "nJTpkP": "Shipping zone created!", "nM4oel": "Product price is required", "nMgpbI": "Add Collections row", "nQKiSH": "Changes saved", "nSTKQ9": "Delete Products", "nT1u71": "Deleting Zone", "nUttvP": "ACCEPTED PAYMENT METHODS", "nVENEr": "Custom Percentage", "na/9iF": "Select customer list", "nasUZ9": "PAY & GET DOMAIN", "nd9gfS": "Robots.txt file deleted", "nf973y": "Entered address has insufficient parameters.", "nfVPxN": "Canal", "nfY0+j": "Email Confirmation", "ngSO6+": "Save Customer", "njrG1J": "Abu Dhabi Islamic Bank – Egypt (ADIB)", "nkg1nG": "Replace File", "nlLPmZ": "Manual shipping is not allowed", "nmpevl": "Discard", "nn2/47": "Upgrade your account to enable advanced shipping settings", "nojvkb": "Please note that “{city}” is now mapped to {mappedTo} in Bosta", "npwdTx": "E-mail only", "nrELbA": "Ensure you upload the screenshot of the transaction invoice correctly for the top-up to be processed.", "nta847": "Edit Cost", "nvfYg6": "Wallet successfully charged {amount}", "nxndnu": "<b>2</b> Staff accounts", "nzyw7z": "Payment card", "o+Bbbd": "Where can I get the {title}?", "o+C60n": "Variant updated!", "o+m6/6": "Expected COD amount", "o0IFAx": "Custom discount", "o1K9KX": "Cannot return non completed shipment", "o4faHn": "<PERSON> bahari", "o5Ce9Y": "Cart recovery email scheduled", "o5LDK+": "Bank Installments", "o7UwVa": "Are you sure you want to delete the {collection} collection", "o8/J84": "Current step", "o8M55y": "Edit pickup location", "o8iEQ9": "Ezbit Hagag", "o8wvhz": "Your name will not be visible on the reply, instead, your reply will be “Reply from [store_name]”", "oA0ogD": "Both", "oAFgOq": "Domain", "oArHqj": "Shipment on hold warehouse", "oB33Lr": "Google Fonts (Free to Use)", "oBC1fG": "Search by amount, details, transaction ID", "oCR9+O": "Delivery min cycle (Delta, Remote areas of Cairo (Al Obour, 10th of Ramadan, Badr, Al Shorouk, Al Sadat, Shoubra El Khema))", "oD3Gja": "Request a demo", "oE74ZZ": "Add custom region", "oF4NJf": "Update Custom Cities", "oFKjjC": "Cash on delivery transaction", "oGntPO": "Add your note", "oI6tZY": "Page content", "oImFBd": "Pending Customer Signature", "oIx4RX": "Cancel airway bill", "oK0S4l": "Transaction ID", "oKkJDM": "Are you sure you want to delete these products", "oLBci1": "In transit", "oLpv29": "Please enter a valid email address", "oND8KT": "Attempts completed", "oNaFAJ": "Taxpayer name", "oNnYSM": "<PERSON><PERSON>", "oQ9WRm": "The discount amount is invalid.", "oR2Zwh": "Phone number is invalid", "oS/nae": "Building", "oU9dfi": "Edit Shipping details", "oV6sMn": "Add/Edit shipping region to sell physical products in your store.", "oV89R2": "<PERSON><PERSON><PERSON>", "oVZ5we": "You don't have sufficient balance", "oVi98s": "Unlimited Storage", "oZjVSA": "Ismailia", "obH5jA": "Migrate from Shopify", "obQxKb": "External ID: {externalId}", "ocNgP+": "Add collection", "oe4vI2": "Select coupon", "ogqePC": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "oi8Om9": "Delete pickup location", "oiTT8X": "Are you sure you want to delete the {zone} zone", "oiwUEc": "Snapchat", "oj9pyQ": "Enter discount value", "okMeu5": "{count} YEAR Subscription", "okZL7c": "<PERSON><PERSON>", "onCUQn": "Enter your subdomain", "opkn3z": "Advanced shipping settings disabled successfully", "otmfuw": "Going to pickup", "ouSDJg": "Aramex", "ounqE1": "Cash on Delivery", "ovJ26C": "Medium", "owRf8v": "Select State", "owebKF": "Filtered", "ox304v": "An error occurred", "oycHaZ": "Customize your store homepage sections", "p1P+hQ": "Request", "p556q3": "<PERSON>pied", "p57IIU": "Automatic discounts", "p5e52I": "Partially refunded transaction", "p63OOR": "Color", "p6DIvF": "Tax Code", "p8uAa5": "Sharq at Tafriah", "p98kOH": "Our Products", "pAW6jI": "Al Ahli Bank of Kuwait – Egypt (ABK)", "pAopCP": "Display your featured collection list in the homepage", "pEGY88": "<PERSON><PERSON>", "pFIeXp": "{fixedAmount} discount on shipping for {minimumPurchase} on the total invoice", "pG751I": "Free Custom Domain", "pG9MmR": "Enter account owner name", "pHiGUh": "Order packaging", "pKCPUA": "Sections reorderd successfully", "pL1yNz": "Invalid cost price", "pM3XK8": "Coupon drafted successfully", "pNoavX": "Preview Store", "pONqz8": "First name", "pOr1fW": "Archived orders", "pPk+ar": "From:", "pPvwfj": "Migrate from <span>WooCommerce</span>", "pQQcL0": "<PERSON><PERSON>", "pQVfC0": "Add staff short description placeholder.", "pQyviX": "Retina Supported (website will look beautiful and crisp on modern displays)", "pXMUk7": "Track order", "pZfOKy": ", + {count} Countries", "paJqxr": "Reason for refund", "pexlh/": "Wuilt shipments activated successfully", "phAZoj": "Collection", "pi4Ko8": "Some deliveries failed, please fix them and upload again.", "piTljS": "No Orders", "pjdHzp": "Select Region", "plUYbT": "Have sent orders", "pmzm1P": "Default value", "pnKB6D": "Shipping rate created!", "prBAWB": "Please enter a valid email", "psV62I": "Cancel the order", "psgCy6": "Payment amount greater than refund amount", "puKUGz": "Collection url is duplicate", "pumhRK": "Heavy bulky (Package size)", "pwbEJ9": "ElWadi Spring", "pxxz4o": "Delivery min cycle (Alex)", "q0+n0s": "Push order failed", "q1WWIr": "In progress", "q1uPfe": "Go to the Domain registrar", "q4umBe": "Tick on all permissions, then click <b>Save</b>", "q4zggk": "Discard Attribute", "q5Xl0M": "Verify", "q5qMIw": "The address deleted!", "q6AxQ+": "Current Year", "q6SPrH": "Apply your own store branding Colors, Fonts & Logo", "q7Sy9w": "Zone Destinations", "q8NNop": "There are a pickup location not supported", "qA2eJk": "Coupon", "qA3TOZ": "Pending COD BREAKDOWN", "qAJuaI": "Online payments BREAKDOWN", "qAeOA4": "CSV for Excel", "qAhUUO": "Thursday", "qBST+n": "Card number", "qF6/01": "A pie chart displaying the percentage distribution of order statuses", "qFXbYL": "{selectedStates} of {allStates} Governorates/Cities", "qIPMiY": "Minimum remaining balance is required", "qIoIv3": "Please upload the transaction invoice", "qJcjXZ": "Are you sure you want to cancel the airway bill?", "qK9GW3": "Insurance", "qL8bp9": "Accept Cash on Delivery orders", "qN4eSR": "Analytics & Reports", "qQNR37": "Google Tag Manager is now connected", "qRA559": "Bosta app settings", "qToGnb": "Cash on Delivery - COD", "qUE8J1": "Specific Products", "qUQWxo": "First name must be at least 2 characters", "qVGRIE": "Quantity", "qVWQkF": "<PERSON><PERSON><PERSON>", "qVsv3y": "200", "qXDNAV": "Up to 100 products", "qXgp/s": "Select at least one product", "qYXjCo": "3-5 working days", "qZFOtu": "Re-attempt pickup", "qZbedW": "Fees", "qaFO3G": "National Bank of Kuwait – Egypt (NBK)", "qbF9TE": "Last Update:", "qbaw60": "The quantity is not available.", "qc3nLu": "Add options", "qeDqFg": "Export products", "qfV4De": "elmansheya el gededa", "qg7HwZ": "Re-attempt delivery ready", "qgAd0Z": "Introducing our new shipment feature with seamless integration to top shipping companies like <b> Aramex, Egypt Post, J&T Express, Bosta and Mylerz</b>. With just a simple click.", "qiWHtE": "Only csv files are allowed!", "qj1uhz": "Disconnect", "qj9kOm": "Route Assigned", "qk3/JO": "Branch name is required", "qk54qZ": "<PERSON><PERSON><PERSON>", "qkFwbV": "Create a collection", "qlb+Ch": "<PERSON><PERSON><PERSON><PERSON>", "qlcuNQ": "ID", "qmIhJj": "{brand} ending in {last4}", "qn4o4k": "ElIkhsas", "qoFGTH": "Invalid customer identifier", "qpHGmm": "We're Here for You", "qq+qTe": "small", "qsomrL": "SKU - Stock Keeping Unit", "qsychK": "If you need help, or have any questions, contact <b>support</b>", "qu2FPQ": "Payment amount", "qwFQy1": "Search & Filters", "qyJtWy": "Show less", "qyRflV": "No payment methods", "qzc8vR": "<span>{admin}</span> created this order for {customer}", "r+8QBX": "4.5/5", "r+dgiv": "Taxes", "r/2ODb": "No customers available", "r/crHx": "Store Info", "r/jfXB": "Current Currency", "r0NCXn": "Manage what can your staff see or do in your store", "r0u4TP": "Coupon code", "r1c6sS": "Expires on {month}/{year}", "r2TGMK": "{storeName} Shopper", "r3xZce": "<b><PERSON><PERSON><PERSON></b>", "r4GYlU": "Liquid Soap.", "r5wto6": "Sports Weapons (Karate, Shouting, Fencing, etc.)", "r6b8Vo": "Compare to", "r7od1k": "Correction of Transaction Errors", "r97m2o": "Revoke Permissions", "rCwyAs": "<PERSON> as Fully paid", "rG4OWC": "Store Settings", "rGqlaT": "Formats: .csv", "rGuW+R": "Free SSL Certificate", "rIAJlE": "Added", "rIRBAz": "UnPaid", "rJw1TG": "Expiry {month}/{year}", "rLDdbB": "Material requested", "rLMky7": "Delete coupon", "rMou3l": "Request airway bill", "rOcW3/": "Wallet balance", "rOvvCc": "Leave this page", "rPaafm": "CVC", "rPijV8": "Oops, An Error occurred. please try again", "rQR5Nb": "Can't remove last pickup location", "rR3jEa": "Our Collections", "rSKXXa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rSfrfz": "Please enter a valid link", "rVbKiP": "Continue editing", "rVpn7G": "(1% from the extra amount) + 14% VAT", "raH+z0": "Value {number}", "rb+JMa": "Old URL should start with your store domain", "rbrahO": "Close", "rcVxQz": "<PERSON>sh<PERSON><PERSON> - <PERSON>", "re6Hq0": "• {count} cities", "reuEOz": "Upgrade your store now and accept online payments 💳", "riqsTL": "Delivery max cycle", "riw/yc": "Beta", "rk9sr2": "Free custom domain", "rkLKdV": "Custom cities under {stateName} updated successfully", "rkghz2": "Oops, An Error occurred. Please try again", "rla2G6": "Non working day", "rm/ZAZ": "Need Permission from Authorities", "rmO1rm": "Ask any question & Get the help you need.", "roJJvO": "Searched customers", "rpN22R": "Shipment on hold", "rs6XNw": "Product name", "ruOqXy": "Show collections names", "rvirM2": "Location", "rxIoIg": "<PERSON><PERSON>-<PERSON><PERSON>.", "rxNddi": "Homepage", "ryjZp4": "Size: 1600px x 500px - aspect ratio: 16/5", "ryneQr": "Precision", "rzbYbE": "Target", "s/NpG0": "{secretKeyFieldLabel} is required", "s/qo4z": "Please enter your last name", "s0glFA": "Display row as", "s1m+lf": "This action is irreversible. The change in currency affects the following:", "s1u1Vq": "Refresh status", "s27KS9": "COLLECTIONS", "s2M2a3": "Shipping company changed from", "s3X0AG": "Order #", "s5TEg0": "CODs settlement days", "s885jr": "Calculating", "s9ShkJ": "The cost of {areaName} has been updated successfully", "sGRcrq": "Desktop View", "sGT01a": "Buyer", "sINxVy": "Your products are uploading now", "sIzMj8": "Shipping region level enabled successfully", "sLNfJp": "<PERSON><PERSON><PERSON>", "sN0Rw5": "<PERSON>", "sO80N0": "Change the currency?", "sReoPX": "Motorbike Spare Parts, Tires, & Helmet.", "sU+nFf": "Store name", "sUdEHt": "Disable if you want to show images without names", "sUenU0": "Material order not found", "sV2v5L": "Instructions", "sVueAC": "Search discounts", "sWdxt/": "<PERSON><PERSON>", "sYtGMu": "A domain is your own unique address on the internet, like wuilt.com", "sanCGA": "Abis Alkawmayah", "sbmoUZ": "A page's meta description tag is meant to give the user an idea of the content that exists within the page.", "sdlktX": "Al-Khattabah", "shbR8S": "1 working day", "siLlN5": "Product url must not be empty", "sj5Kku": "Please add at least one banner", "skymR6": "<PERSON><PERSON><PERSON>", "slWGQy": "Request material", "smB5Dn": "There was a problem loading payment", "sn6xfQ": "Create anything, a full multi-lingual website or an e-commerce store", "so6k0k": "URL redirect added", "ss2Frm": "is already sent for shipment by {shippingProvider}", "ss6kle": "Reset to default", "ssiQfP": "Select at least one country", "stZrrE": "You should have a business location before creating an order.", "sveUDx": "Collection updated!", "swBv9/": "El<PERSON><PERSON>", "swMISG": "Added:", "swXR1x": "Robots.txt editor", "sx5n/j": "Activate Wuilt Shipments", "sx8bq9": "Abandoned checkouts by date", "sy+pv5": "Email", "syEQFE": "Publish", "syJdaW": "Canceled payout", "szZ0Zm": "Almanshaya", "t/PrA1": "Enter discount amount", "t0rfk6": "Cut off time for Tuesdays requests", "t11OJw": "Inconsistent pricing", "t14PJk": "Quantifiable", "t1BLIB": "Edit store details", "t3/rZC": "Shipping fees", "t350u5": "Order #{orderSerial} airway bill created successfully", "t3ydR5": "Mobile App (Soon)", "t5xW2q": "Import products by CSV", "t6Jky2": "Alnahda", "t7dj6c": "3 working days", "t8jzy5": "This week (Saturday to Friday)", "t9FyIq": "Main Template Features:", "t9RgxQ": "<PERSON><PERSON><PERSON>", "t9qEPu": "on", "tB5ukP": "COD collection fees", "tBeL9O": "Attijariwafa Bank Egypt (BBE)", "tBecGP": "<PERSON><PERSON>", "tCYgg5": "Total Revenue", "tEK070": "Select rate", "tEWFBB": "Change Store Currency", "tEfTgX": "<PERSON><PERSON><PERSON>", "tGHzxH": "SSL Protection", "tI+3g7": "Pickup requested", "tLqygx": "Are you sure you want to refund this payment?", "tMJmMg": "Manage Categories", "tNcHxJ": "Edit reply", "tNg0x4": "Edit API Key", "tNseQe": "Investigation", "tO8n4k": "Define your shipping regions and how rates are calculated", "tOMabM": "Product Information", "tOxaBR": "North Sinai", "tPHT7D": "Use this to import or edit multiple products in one go", "tQ5ODw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tThlE9": "Notes are required", "tV4ZQX": "/annually", "tVQgs3": "Reviewing Instapay top-up", "tWzoXo": "Select Precision", "tXt4y+": "Text row updated!", "taHUpL": "Fatoora Installed successfully", "tbSX+S": "Enable Order created handling flow", "tckgqY": "<PERSON><PERSON><PERSON>", "teIVqz": "End time must be after the start time", "teLZyZ": "Height", "tf1lIh": "Free", "tf8ldX": "{IDFieldString} is required", "tfaxEy": "<PERSON><PERSON><PERSON><PERSON>", "tiVXug": "Please enter your city", "tkwCac": "E-mail", "tlzCDY": "No Transactions", "tmBapV": "Variant image", "tmNzgM": "<PERSON><PERSON>", "tmkpiN": "Email confirmation notes updated", "tmwdb4": "<PERSON><PERSON>", "tnRDuU": "Revoke", "tnuBMt": "Page title", "tnwFLK": "Main Slider", "to8oPs": "<PERSON><PERSON><PERSON>", "toP7Jt": "Deleting Page", "tpWAYD": "Minimum purchase amount is required.", "tpZBad": "<PERSON><PERSON>", "tr": "Turkish", "ttF1SF": "Advanced Shipping Settings", "ttF91g": "Customers can pay you offline, When their order is shipped", "ttaMk4": "Ao'nayba", "tw7cZ2": "Please wait for collection image upload to complete", "twzZ6V": "Lost Package", "txUL0F": "Last name", "tyF1wo": "Select option", "tyINVj": "Street Code Not Found.", "tygEJX": "Date range", "tzMNF3": "Status", "u/byB+": "This process will delete the {discountName} discount. Are you sure you want to continue?", "u/mXFJ": "Helioples", "u/vOPu": "Paid", "u0cul1": "Theme installed successfully", "u1ETNe": "Add Image", "u1N1FC": "Create your Wuilt Store", "u3LN6o": "<PERSON><PERSON><PERSON>", "u7ViLF": "{pageTitle} Page", "u9/+gO": "Collections rows", "uA6RXI": "Product url is duplicate", "uBDPzD": "Import More Products", "uERn70": "Invalid package type", "uHMFJA": "You need to draft other {type} discounts before activating this one.", "uHavCZ": "Can the user choose more than one item for the same product?", "uJ3GPg": "HSBC Bank Egypt S.A.E (HSBC)", "uJ3gjX": "Large flyer/box", "uKFo5U": "Maximum compensation per lost shipment", "uLN8od": "Search products by name", "uLWjMz": "Deleting Collection", "uMYxSz": "Customer contact method", "uOn0Or": "Add Products", "uQnfNR": "Page url must not be empty", "uS4sop": "Type:", "uTSSg3": "Access token", "uWGUO4": "You can edit the product data in the variants card below", "uY+I6z": "No payouts", "uZSztz": "Allow the buyer to open the package before accepting it.", "uaFEqY": "To connect {integrationName}, log in to your {integrationName} account and import the XML file you see below. You may also paste a link to it. The products are synchronized with the file automatically.", "ubmFc8": "Install", "uc4F3A": "Page url is duplicate", "ud+iqr": "Bo<PERSON>", "ud1y+5": "Enter option name: ex. color", "udFe3T": "Renew certificate", "udT6zB": "Not Recovered", "uds7Aq": "Last week (Saturday to Friday)", "ueK/QO": "This is the theme customers see when they visit your store.", "uhC613": "Easy navigation", "ujH6ND": "Invalid CVC number", "ukLV1Y": "Attribute Information", "ukQpDs": "Folder", "ukao9T": "Are you sure you want to delete this review reply?", "ukmTYK": "Option must be unique", "ulh3kf": "Collections", "ulp3g5": "Edit tags", "um9g4U": "Picked up from consignee", "umKn01": "Calculate Shipping Cost", "un+VWt": "Search products", "unWaK2": "Shipping Cost Adjustment (EGP {diff})", "uoVWQY": "Disable Product Reviews on your store?", "uogaDM": "Social integrations (Facebook, TikTok GMC)", "uowcqA": "Izbat Al Mustamarah", "up+Lh8": "Row {number}", "uq9tOP": "Collection products updated!, {addedProductsCount} added - {removedProductsCount} removed", "uqe58g": "Breakdown", "uqnvet": "Products row added!", "urNHKr": "Slide {number}: Button 2 text", "us4b+K": "What kind of website do you need?", "usNEmn": "(<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>)", "uuiZBK": "Picking up from consignee", "uwCBIB": "Status Update Not Received", "ux9RQQ": "Export format", "uxPkZS": "Social Integration", "uzl7eS": "KNET", "v/R4eo": "Add Rate", "v0E2LM": "Charge customer for shipping cost difference", "v2XPJz": "+{collectionsCount} more", "v3hawk": "No collections", "v3tQCl": "Delivery address", "v5fTRo": "Alrizka<PERSON>", "v7EelG": "<PERSON> <PERSON><PERSON><PERSON>", "v8+1Cb": "Free shipping is applied, the buyer can’t be charged in this case.", "vAb8Ew": "Canceled transaction", "vBCJu8": "Cancel shipment", "vBJayL": "Joined {duration} (Registered {date})", "vBMNDE": "Minimum purchase amount", "vEYtiq": "Category Name", "vJjBi5": "Electricity Panels & Wires.", "vKugLL": "Cairo, Giza", "vO+dod": "Show header", "vONi+O": "Country", "vQGsiY": "Search or add new tag", "vQZAZm": "Top up amount must be less than {max}", "vRg9fy": "<PERSON><PERSON><PERSON><PERSON>", "vUEuL2": "Have different providers", "vX/Qmy": "From Easy Orders dashboard, click on <b>Settings</b>", "vXCeIi": "Failed", "vXx7Xz": "Load More Products", "vZO4p4": "Transaction details", "vamRfi": "Discount value must be less than {amount}{translatedCurrency}", "vdZl5x": "Return shipment processing", "vguU4x": "No, Not Quantifiable", "vguWdz": "CouponCode deleted successfully", "vhGdKG": "Customer Orders", "vjio3/": "A few details about your store.", "vlGou/": "<PERSON><PERSON>", "vly+SU": "Know all the transactions in your wallet all the time.", "vmQPWt": "Max. quantity", "vonprJ": "Cart recovery email not sent", "vqB5te": "Having a problem? Submit a ticket", "vrPyYQ": "Are you sure you want to update the order?", "vuKrlW": "Stock", "vup8LM": "qaraya 5", "vvH91b": "Zone Rates", "vwF29O": "Mashreq Bank (MASH)", "vwHg0V": "Store Language", "vwxVnf": "Excel", "vx0nkZ": "Privacy Policy", "vxoSTO": "ElQasr Area", "vyzVc5": "Tuesday 5:00 PM", "vz4MNh": "Pickup from store", "vzqhaw": "Enable if you want to remove the card background", "w+3+kc": "Vodafone Store Plus", "w+QuA/": "Buy {customerBuyAmount} and get {customerGetCount} with {percentage}% discount on selected products", "w04QYe": "Alemam Malek Village", "w1qf/B": "Abandoned checkout emails", "w3C+Tz": "Request withdraw from your wallet", "w40QJx": "PS: Activating City & Region levels, affects the customers checkout process.", "w5T+nH": "Variant price is required", "w6FdJK": "Announcement bar is displayed on top of the website", "w70AbP": "Cart recovery email sent", "w7tf2z": "Published", "w841j4": "Remove tags", "w9oFH1": "Simple Product", "wB7RIG": "Download screenshot", "wCeu0r": "{count, plural, =0 {} one {<b>1</b> Banner} two {<b>#</b> Banners} few {<b>#</b> Banners} many {<b>#</b> Banners} other {<b>#</b> Banners}}", "wCy/Tc": "Translate", "wEQDC6": "Edit", "wFmAFC": "Page", "wH7V5A": "<PERSON><PERSON><PERSON>", "wHi9g1": "Refund successfully processed", "wI1j8E": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wIFzhH": "Qaryat Al Kifah", "wIl5yq": "<PERSON><PERSON>", "wJakn+": "Add/Remove Regions", "wK9N5i": "Abis 05", "wKLR/+": "<PERSON><PERSON><PERSON><PERSON>", "wL7VAE": "Actions", "wMfmhf": "Search products by category", "wMjup3": "Public Id", "wN03mS": "Expiry {expMonth}/{expYear}", "wNYmzg": "Received at warehouse", "wOFJFO": "No data available", "wPoRub": "Avg. Order Value", "wQweCr": "Delete Zone", "wR7bG8": "Headers font family", "wRLOIX": "here <link>www.robotstxt.org</link> If populated, this will be added to all of your pages.", "wTNySU": "by", "wTornV": "<PERSON><PERSON>", "wYsv4Z": "Monthly", "wZ4ppn": "Trees", "wbU4tD": "Unpublished", "wbWBPw": "Confirm Order", "wbcwKd": "View All", "wbsq7O": "Usage", "wdsniY": "Watch video", "we0SKl": "Add new page", "wea7Be": "You can't apply discount when order total is edited", "weeTDP": "Societe Arabe Internationale De Banque (SAIB)", "wf61iI": "Write your unique confirmation message for your customer.", "wfQoAv": "Are you sure you want to top up your wallet with <b>{topUpAmount}EGP</b>?", "wjyP1l": "Attributes row", "wkz/Ia": "View shipping analytics for your store.", "wm96Jx": "Returned", "wmirkP": "Document", "wmsvPo": "Select pickup location city", "wprjyA": "<PERSON><PERSON><PERSON><PERSON>", "wqoHDK": "Product Display", "wqodIB": "Need help with your Wuilt store? Request a demo with a Wuilt expert", "ws//HC": "130 Reviews", "wsVKCR": "Suggested domain names", "wso36g": "After Kilo 28", "wsq0ZX": "Enable Customer sign-up handling flow", "wtfk/S": "The city of order #{orderSerial} is not supported by {shippingProvider}", "wvEEkU": "Collection Failed", "wxEGYB": "Enable users to pick their region within a city on checkout, Gives you more control on showing, hiding and pricing the regions you serve", "wyAwfy": "Alatta", "x+AMtQ": "<b>Pending Pickup:</b> Orders requests that are sent to the shipping company and the pickup is in progress.", "x+H3tp": "Reason for refund is required", "x+t1Jf": "In Stock", "x/ZVlU": "Product", "x1+Cbe": "ElBostan", "x1dzOK": "Gold in any Shape.", "x4hPYq": "We’re migrating your products. <span>Don’t close this window </span>— it’ll cancel the process.", "x5rks2": "Your first name", "x7XgOg": "Button title is required", "xAo+Da": "<b>Airway Bill Cost:</b> The total cost required to issue AWBs for all unshipped orders.", "xG/8vo": "Mark order as Partially Paid", "xK1vpE": "No abandoned checkouts", "xKV+E0": "Hide Items", "xLU+Rp": "Generate API Key long description in page header", "xMURNQ": "Shipping state", "xMgehQ": "Lock acquisition failed", "xNg7nj": "Keep it", "xOgLHM": "<PERSON><PERSON>", "xPVfnY": "Please enter some text", "xPcMfD": "<PERSON><PERSON>", "xUWkvt": "Dataset ID", "xV8Iut": "Alexandria", "xXLfJl": "ex. Our latest offers", "xa85Df": "Deactivating this shipping company will deactivate Wuilt shipments for your store.", "xcGQJS": "<b>Request needed:</b> Pending orders that have not been shipped yet.", "xcKr33": "Hang tight! 🚀", "xdzer4": "Ageba", "xe05Cm": "Next Steps", "xe3qfV": "Wuilt shipments has been activated, start shipping your orders.", "xh29Xa": "Filtered products", "xhYLWj": "PREMIUM", "xho34O": "Allow opening the package", "xk7NFj": "Customers by date", "xmcVZ0": "Search", "xmgXFz": "Select Products", "xngKFZ": "Set as {badge}", "xpAIVj": "Charge specific tax on this product", "xs98Ig": "Please choose a border color", "xsjIBY": "Update payment", "xtLfJM": "Enter Instapay address", "xtf49p": "Abandoned Checkouts", "xto/1L": "Invalid robots.txt format detected.", "xuTqgN": "Payment Methods", "xuugrN": "ElAbid Bridge", "xx364+": "Invalid Phone Number.", "xyE71K": "ex. https://www.snapchat.com/YOURSTORE", "xyH0ON": "Click to upload", "xyVDm4": "After payment methods", "y0lz5N": "Rate cost is required", "y2siZN": "By activating Wuilt shipments, all shipping zones in Egypt will be automatically covered.", "y3mP6U": "Rate name is required", "y4pvko": "You are already an owner", "y5gsAL": "Specific Collections", "y5oHix": "Fill in your contact information to own your domain", "y8J2v4": "About and contact pages", "y8dkt9": "Max. file size 10MB", "y9ZbeM": "Last month", "y9xHR9": "Payouts", "yEAOs7": "Size: 1080px x 1080px - aspect ratio: 1/1", "yEL0aP": "Genifa", "yF8l83": "Bigger than", "yG84qv": "Product updated!", "yGzTUx": "Buy a new domain name", "yH1yQb": "Automatic withdrawal", "yH7dbl": "{amount} {currency} successfully paid via <span>Wuilt Pay</span> ({brand} ending in {last4})", "yIF0OV": "These products are missing weight information. Please provide it before shipping to avoid potential changes in shipping cost.", "yJWdDO": "TAGS", "yKltLr": "<PERSON><PERSON><PERSON>", "yN7hve": "Top Selling Products", "yNb1sX": "isArchived", "yNmV/R": "Items", "yOqw9D": "Here you’ll receive & fulfill orders, collect payments, and track orders statuses", "yPr4Ut": "Enter account IBAN", "ySS8b3": "Pickup request sent successfully", "yT/GAp": "Tracking number", "yZwsPr": "National Bank of Egypt (NBE)", "yZxT6K": "Special Discount - {value} Off", "yaWf30": "Update fulfillment", "ybb7qz": "You’re adding custom states under {countryName}", "ycMPM2": "MM/YY", "yelMPA": "Are you sure you want to remove your store domain?", "yfqk6a": "No Online payments transactions are available", "ygYkZY": "Custom Regions", "yggxJU": "<PERSON><PERSON><PERSON> Eldweka - Al Abagaya", "yhcPXr": "Abu Dhabi Commercial Bank – Egypt (ADCB)", "yn6boO": "Activate now", "yn6fTE": "Once you start shipping orders, your statistics will appear here. Monitor delivery outcomes, costs, and progress in one place.", "ynNFFC": "The language in the URL will be detected and used for product migration.", "ynrDjK": "ID must not contain spaces", "yphS5C": "Processing transaction", "yqdUTP": "Material not found", "yqgNdO": "e.g. yourdomain.com", "yrOmmJ": "Assigned to warehouse", "ytE2v5": "Chat support", "ytiMXg": "Hight (cm)", "yuiyES": "General Settings", "yvVaZJ": "ElFarafra Oasis", "yxWB8I": "<PERSON><PERSON><PERSON> De3bs", "yzc4dR": "Are you sue you want to disable product reviews on your store?", "z/2AZY": "Discount type", "z0Fn1Z": "Select at least one product.", "z0I0PP": "You need to have at least one required category with preselected product", "z0gN/s": "El Ganayen Area", "z2RRic": "Domain connected successfully!", "z2RzqF": "with {provider}", "z5PyDa": "Custom build products", "z6PZHh": "<span>{admin}</span> issued a refund", "z7+8kB": "View order", "z70C4z": "Reviewing withdraw request", "z8sRaY": "Button link", "z9nNr5": "Button border color", "z9tPF2": "Yes, Quantifiable", "zAGlVG": "Transaction not found", "zAYRG7": "Export Development Bank of Egypt (EDBE)", "zBD7YT": "Pickup location address cannot contain special characters except '-'", "zBRrHt": "Size", "zCGyLO": "Cash on delivery transactions", "zEXU3h": "{count} Regions", "zEfpUR": "({count} of {total} Selected)", "zF7Cth": "Guest", "zGQmLP": "there are some custom cities that needs your attention", "zHC0Ab": "Cash on delivery", "zIHfQy": "Se<PERSON><PERSON>", "zINlao": "Owner", "zK5FtV": "Pay the return rate", "zKASbT": "You will need to handle the refund manually with the customer.", "zKmH1p": "• The imported products are in <b>Draft</b> status", "zLFfk+": "COD to be collected", "zLcI3v": "Shipping analytics", "zMIjlC": "Egyptian Gulf Bank (EGB)", "zMVQXl": "Cairo Alex Desert Road", "zMkAv6": "Discount schedule start date not provided", "zND/qF": "Custom code", "zQvVDJ": "All", "zSfSPm": "Apply Discount", "zTbLfn": "Parent", "zUvAhU": "Filtered orders", "zVcj1h": "needed", "zWNvn1": "Edit payout settings", "zWgbGg": "Today", "zXUzWT": "Old URL is already used", "zZ6jlw": "Your store is not online!", "zZAufZ": "Product page", "zZgyh5": "Please edit the quantity of this product, <b>available stock: {availableQuantity}</b>", "zbY6es": "Outside Ismailia", "zerEZt": "Your store’s integration with [Google Analytics/Meta Analytics] has been disconnected to be replaced with a better tool!", "zf8qY+": "Shipping zones", "zfs6a2": "Upload logo", "zgrORK": "Customer details", "zjkoqu": "<PERSON><PERSON><PERSON> fel <PERSON>", "zjziSL": "Add Shipping Providers", "zko5mn": "By charging specific tax on this product, it will override the Store taxes settings, and will be applied to this product only.", "zmImBf": "1 {currencyCode} = {exchangeRate} {newCurrencyCode}", "zmqpgN": "Google Tag Manager is updated", "zpU3Ts": "Your Current Plan Info.", "zrLoJf": "Online payments transactions", "zsPa3b": "Add collection", "ztNAHz": "<PERSON><PERSON><PERSON>", "zwitnV": "Plot identification number", "zwtCRs": "Add attribute value", "zx7NUv": "The selections of {areaName} updated successfully", "zx9nIY": "Add URL redirect", "zyGeGr": "You can only choose {count} image per variant", "zyQczV": "{items, plural, =0 {0 items} one {1 item} two {# items} few {# items} many {# items} other {# items}}", "zz6ObK": "Rest<PERSON>", "zzDPx2": "Go live! when you’re ready", "zzatZr": "You haven’t added any videos"}