import React from "react";
import { WidgetRenderer } from "../screens/WidgetRenderer";
import { StrictMode } from "react";
import * as ReactDOM from "react-dom/client";
import { TWidgetSettings } from "../shared/types";
import { LocaleEnum } from "../main";

interface WidgetProps {
  settings: TWidgetSettings;
  alwaysShow?: boolean;
  locale: LocaleEnum;
}

export const Widget: React.FC<WidgetProps> = ({
  settings,
  alwaysShow,
  locale,
}) => {
  return (
    <WidgetRenderer
      settings={settings}
      alwaysShow={alwaysShow}
      locale={locale}
    />
  );
};

export function initWidget(settings: TWidgetSettings, locale: LocaleEnum) {
  let div = document.getElementById("widget-renderer-root");
  if (!div) {
    div = document.createElement("div");
    div.id = "widget-renderer-root";
    document.body.append(div);
  }
  const root = ReactDOM.createRoot(div);
  root.render(
    <StrictMode>
      <Widget settings={settings} locale={locale} />
    </StrictMode>
  );
}
