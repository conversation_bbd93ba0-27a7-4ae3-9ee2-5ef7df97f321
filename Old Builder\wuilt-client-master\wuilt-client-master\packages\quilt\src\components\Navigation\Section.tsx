import React from "react";
import styled from "styled-components";
import { Stack } from "../Stack/Stack";

const StyledTitle = styled.h4`
  padding: 5px 22px;
  color: ${(props) => props.theme.palette.ink.light};
  font-size: ${(props) => props.theme.base.fontSize.sm};
  text-transform: uppercase;
`;

export interface SectionProps {
  children?: React.ReactNode;
  title?: React.ReactNode;
}

const Section: React.FC<SectionProps> = ({ children, title }) => {
  return (
    <Stack spacing="tight">
      <StyledTitle>{title}</StyledTitle>
      {children}
    </Stack>
  );
};

Section.displayName = "NavigationSection";
export { Section };
