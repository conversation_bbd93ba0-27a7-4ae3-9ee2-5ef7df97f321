import React from "react";
import styled, { css } from "styled-components";
import {
  layout,
  LayoutProps,
  position,
  PositionProps,
  flexbox,
  FlexboxProps,
  grid,
  GridProps,
} from "styled-system";
import { Ref } from "../../common/types";
import mediaQueries from "../../utils/mediaQuery";
import { DEVICES } from "../../utils/mediaQuery/consts";
import {
  Color,
  ColorProps,
  Typography,
  TypographyProps,
  TextAlignmentFn,
  TextAlignmentProps,
  Space,
  SpaceProps,
  BoxShadow,
  ShadowProps,
  Border,
  BorderProps,
} from "../../themes/property-overriding";
import overwriteStyles from "../../utils/mediaQuery/overwriteStyles";
import { getMediaQueryStyles } from "../../utils/mediaQuery/getMediaQueryStyles";
import { Global } from "../../common/types";
import { UnstyledLink } from "../UnstyledLink";
// import { UnstyledLink } from "../UnstyledLink";

export interface BoxContainerStyles
  extends TextAlignmentProps,
    ColorProps,
    SpaceProps,
    ShadowProps,
    TypographyProps,
    LayoutProps,
    PositionProps,
    BorderProps,
    FlexboxProps,
    GridProps {
  readonly style?: React.CSSProperties;
}

interface GeneralBoxProps extends Global {
  readonly children?: React.ReactNode;
  readonly cursor?: React.CSSProperties["cursor"];
  readonly className?: string;
  readonly id?: string;
  readonly asComponent?: React.ComponentType;
  readonly href?: string;
  readonly external?: boolean;
  readonly to?: string;
  readonly title?: string;
  readonly innerHTML?: React.ReactNode;
  readonly onClick?: React.MouseEventHandler<HTMLDivElement>;
  readonly onHover?: BoxContainerStyles;
  readonly onScroll?: React.UIEventHandler<HTMLDivElement>;
}

export interface BoxProps extends BoxContainerStyles, Ref, GeneralBoxProps {
  readonly smallMobile?: BoxContainerStyles;
  readonly mediumMobile?: BoxContainerStyles;
  readonly largeMobile?: BoxContainerStyles;
  readonly tablet?: BoxContainerStyles;
  readonly desktop?: BoxContainerStyles;
}

const Box: React.FC<BoxProps> = (props) => {
  const {
    ref,
    dataTest,
    children,
    className,
    id,
    title,
    cursor,
    asComponent = props.href ? "a" : props.to ? UnstyledLink : "div",
    href,
    to,
    innerHTML,
    external,
    onClick,
    onScroll,
    onHover,
    style,
    smallMobile,
    mediumMobile,
    largeMobile,
    tablet,
    desktop,
    ...largeDesktop
  } = props;

  return (
    <StyledContainer
      className={className}
      dataTest={dataTest}
      id={id}
      ref={ref}
      Component={asComponent}
      cursor={cursor}
      href={href}
      to={to}
      innerHTML={innerHTML}
      external={external}
      onClick={onClick}
      onScroll={onScroll}
      smallMobile={overwriteStyles(largeDesktop, smallMobile)}
      mediumMobile={overwriteStyles(largeDesktop, mediumMobile)}
      largeMobile={overwriteStyles(largeDesktop, largeMobile)}
      tablet={overwriteStyles(largeDesktop, tablet)}
      desktop={overwriteStyles(largeDesktop, desktop)}
      largeDesktop={largeDesktop}
      onHover={onHover}
      title={title}
      styles={getMediaQueryStyles({
        largeDesktop: style,
        desktop: desktop?.style,
        tablet: tablet?.style,
        largeMobile: largeMobile?.style,
        mediumMobile: mediumMobile?.style,
        smallMobile: smallMobile?.style,
      })}
    >
      {children}
    </StyledContainer>
  );
};

export { Box };

/**
 *
 * styles
 *
 */

const getBoxStyles = (viewport) => (props) => {
  const cssProps = { ...props[viewport], theme: props.theme };
  return css`
    ${Color(cssProps)};
    ${TextAlignmentFn(cssProps)}
    ${Typography(cssProps)}
	${BoxShadow(cssProps)}
  ${Space(cssProps)}
  ${layout(cssProps)}
	${position(cssProps)}
	${Border(cssProps)}
	${flexbox(cssProps)}
	${grid(cssProps)}
  `;
};

const boxMediaQueries = (props) =>
  DEVICES.map((viewport) =>
    viewport in mediaQueries
      ? mediaQueries[viewport](css`
          ${props[viewport] && getBoxStyles(viewport)}
        `)
      : viewport === "largeDesktop" &&
        css`
          ${getBoxStyles(viewport)}
        `
  );

const boxOnHoverStyles = ({ onHover, theme }) => {
  const cssProps = { ...onHover, theme };
  return css`
    ${Color(cssProps)};
    ${TextAlignmentFn(cssProps)}
    ${Typography(cssProps)}
		${BoxShadow(cssProps)}
		${Space(cssProps)}
		${layout(cssProps)}
		${position(cssProps)}
		${Border(cssProps)}
		${flexbox(cssProps)}
		${grid(cssProps)}
  `;
};

const StyledContainer = styled(
  React.forwardRef<HTMLElement, any>(
    (
      {
        className,
        id,
        Component,
        children,
        onClick,
        onScroll,
        href,
        to,
        external,
        dataTest,
        innerHTML,
        title,
      },
      ref
    ) => (
      // eslint-disable-next-line react/no-danger-with-children
      <Component
        ref={ref}
        data-test={dataTest}
        className={className}
        id={id}
        onClick={onClick}
        onScroll={onScroll}
        href={href}
        to={to}
        title={title}
        target={(href || to) && external ? "_blank" : undefined}
        children={children}
        dangerouslySetInnerHTML={innerHTML ? { __html: innerHTML } : undefined}
      />
    )
  )
)`
  display: block;
  text-decoration: none;
  box-sizing: border-box;
  ${boxMediaQueries}
  ${({ styles }) => styles};
  ${({ cursor }) => cursor && `cursor: ${cursor}`};

  &:hover,
  &:active {
    ${boxOnHoverStyles}
  }
`;
