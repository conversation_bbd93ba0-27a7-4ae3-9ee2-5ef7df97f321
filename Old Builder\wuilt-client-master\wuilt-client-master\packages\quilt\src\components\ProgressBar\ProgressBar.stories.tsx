import React from "react";
import { text, select, number } from "@storybook/addon-knobs";
import { Size } from "common/types";
import { ProgressBar } from "./ProgressBar";

export default {
  title: "Components/ProgressBar",
  component: ProgressBar,
};

const sizes: Size[] = ["small", "normal", "large"];

export const Playground = () => {
  return (
    <ProgressBar
      size={select("size", sizes, "small", "Component")}
      text={text("text", "Completed", "Component")}
      count={number("count", 7, undefined, "Component")}
      completed={number("completed", 2, undefined, "Component")}
    />
  );
};
