import React, { createContext, useState } from "react";
import cuid from "cuid";
import { Stack, StackProps, StackSpacing } from "../Stack";

export const RadioContext = createContext({
  name: "",
  // eslint-disable-next-line no-constant-binary-expression
  value: 0 as any,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onChange: (v: any) => {
    //
  },
});

export interface RadioGroupProps<Type = string> {
  alignment?: "horizontal" | "vertical";
  spacing?: StackSpacing;
  name?: string;
  value?: Type;
  defaultValue?: Type;
  children?: any;
  onChange?: (value: Type) => void;
  allowUseEffectChange?: boolean;
  stackProps?: StackProps;
}

function RadioGroup<Type>({
  alignment = "vertical",
  spacing,
  children,
  name = cuid(),
  value,
  defaultValue,
  stackProps,
  onChange,
  allowUseEffectChange,
}: RadioGroupProps<Type>) {
  const [selectedValue, setSelectedValue] = useState<Type>(
    (defaultValue || value) as Type
  );

  const handleChange = React.useCallback(
    (val: Type) => {
      setSelectedValue(val);
      if (onChange) {
        onChange(val);
      }
    },
    [onChange]
  );

  React.useEffect(() => {
    allowUseEffectChange && value && handleChange(value);
  }, [handleChange, allowUseEffectChange, value]);

  return (
    <RadioContext.Provider
      value={{ name, value: selectedValue, onChange: handleChange }}
    >
      <Stack
        spacing={spacing}
        direction={alignment === "horizontal" ? "row" : "column"}
        inline
        {...stackProps}
      >
        {children}
      </Stack>
    </RadioContext.Provider>
  );
}

RadioGroup.displayName = "RadioGroup";
export { RadioGroup };
