import { useEffect, useRef } from "react";
import _isEqual from "lodash/isEqual";

export function useDeepCompareEffect(callback: () => any, dependencies: any[]) {
  const currentDependenciesRef = useRef<any>();

  if (!_isEqual(currentDependenciesRef.current, dependencies)) {
    currentDependenciesRef.current = dependencies;
  }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(callback, [currentDependenciesRef.current]);
}
