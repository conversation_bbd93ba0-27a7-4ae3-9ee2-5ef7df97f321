import React from "react";
import styled, { css } from "styled-components";
import { ColorProps, Color } from "../../themes/property-overriding";
import { ICON_SIZES } from "./consts";

export const getSize = ({ size }) => {
  return ICON_SIZES[size] || ICON_SIZES.md;
};

const styles = ({
  width,
  height,
  customColor,
  reverseOnRtl,
  theme,
  rotate = 0,
}) => css`
  width: ${width || getSize};
  height: ${height || getSize};
  flex-shrink: 0; // prevent shrinking when used in flex-box
  vertical-align: middle;
  fill: currentColor;
  ${Color}
  ${customColor && `color:${customColor}`};
  transform: rotate(${rotate}deg) ${reverseOnRtl && theme.rtl && "scale(-1, 1)"};
`;

const StyledIcon = styled(
  ({
    Component,
    className,
    viewBox,
    dataTest,
    children,
    ariaHidden,
    ariaLabel,
    title,
    svgString,
  }) => {
    return (
      // eslint-disable-next-line react/no-danger-with-children
      <Component
        className={className}
        viewBox={viewBox}
        data-test={dataTest}
        preserveAspectRatio="xMidYMid meet"
        aria-hidden={ariaHidden ? "true" : undefined}
        aria-label={ariaLabel}
        dangerouslySetInnerHTML={svgString ? { __html: svgString } : undefined}
        title={svgString ? title : undefined}
        children={
          children ? (
            <>
              {title && <title>{title}</title>}
              {children}
            </>
          ) : undefined
        }
      />
    );
  }
)`
  ${styles}
  svg {
    ${styles}
  }
`;

export interface IconProps {
  size?: keyof typeof ICON_SIZES;
  width?: string;
  height?: string;
  color?: ColorProps["color"];
  customColor?: string;
  className?: string;
  children?: React.ReactNode;
  viewBox?: string;
  dataTest?: any;
  ariaHidden?: boolean;
  reverseOnRtl?: boolean;
  ariaLabel?: string;
  title?: React.ReactNode;
  svgString?: string | null;
  rotate?: number;
}
const Icon: React.FC<IconProps> = (props) => {
  const {
    size = "md",
    width = props.svgString ? "auto" : props.width,
    height = props.svgString ? "auto" : props.height,
    color,
    customColor,
    className,
    children,
    viewBox,
    dataTest,
    ariaHidden,
    reverseOnRtl,
    ariaLabel,
    title,
    svgString,
    rotate,
  } = props;
  return (
    <StyledIcon
      Component={svgString ? "span" : "svg"}
      svgString={svgString}
      viewBox={viewBox}
      size={size}
      width={width}
      height={height}
      className={className}
      dataTest={dataTest}
      customColor={customColor}
      color={color}
      ariaHidden={ariaHidden}
      reverseOnRtl={reverseOnRtl}
      ariaLabel={ariaLabel}
      title={title}
      rotate={rotate}
    >
      {children}
    </StyledIcon>
  );
};

Icon.defaultProps = {
  size: "md",
};

export { Icon };
