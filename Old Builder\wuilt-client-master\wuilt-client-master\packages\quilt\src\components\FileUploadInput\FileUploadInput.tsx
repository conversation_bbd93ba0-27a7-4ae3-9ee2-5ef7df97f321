import React, { useRef } from "react";
import styled from "styled-components";
import { rtl } from "../../utils";
import { ImportIcon, TrashIcon } from "../icons";
import { GeneralInputWrapper } from "../InputField/InputField";

export interface InputUploadFieldProps
  extends Omit<React.HtmlHTMLAttributes<HTMLInputElement>, "value"> {
  isError?: boolean;
  value: File;
  buttonText: React.ReactNode;
  noFileText: React.ReactNode;
  deleteFile: () => void;
}

const FileUploadInput = ({
  isError,
  buttonText,
  noFileText,
  value,
  onChange,
  deleteFile,
}: InputUploadFieldProps) => {
  const uploadInput = useRef<HTMLInputElement>(null);
  const onButtonClick = () => {
    uploadInput.current?.click();
  };

  return (
    <FileUploadContainer>
      <GeneralInputWrapper isError={isError}>
        <FileName>{value?.name || noFileText}</FileName>
      </GeneralInputWrapper>
      {value ? (
        <FileUploadBtn onClick={deleteFile}>
          <TrashIcon />
        </FileUploadBtn>
      ) : (
        <FileUploadBtn aria-hidden="true" onClick={onButtonClick}>
          <input type="file" ref={uploadInput} onChange={onChange} />
          <ImportIcon color="transparent" />
          <span>{buttonText}</span>
        </FileUploadBtn>
      )}
    </FileUploadContainer>
  );
};

export { FileUploadInput };
const FileUploadBtn = styled.div`
  display: flex;
  gap: 5px;
  font-family: inherit;
  flex: 1;
  align-self: stretch;
  justify-content: center;
  align-items: center;
  padding: 0 12px;
  margin-top: 4px;
  border-top-right-radius: ${rtl("0", "4px")};
  border-bottom-right-radius: ${rtl("0", "4px")};
  border-top-left-radius: ${rtl("4px", "0px")};
  border-bottom-left-radius: ${rtl("4px", "0px")};
  cursor: pointer;
  width: max-content;
  border: 1px solid ${({ theme }) => theme.palette.ink.lighter};
  border-left: ${rtl(
    `1px solid ${({ theme }) => theme.palette.ink.lighter}`,
    "none"
  )};
  border-right: ${rtl(
    "none",
    `1px solid ${({ theme }) => theme.palette.ink.lighter}`
  )};

  input {
    display: none;
  }
`;

const FileUploadContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  & > div:first-of-type {
    width: 100%;
    border-top-right-radius: ${rtl("4px", "0")};
    border-bottom-right-radius: ${rtl("4px", "0")};
    border-top-left-radius: ${rtl("0", "4px")};
    border-bottom-left-radius: ${rtl("0", "4px")};
    cursor: default;
    justify-content: space-between;
  }

  .file-upload-btn {
    display: flex;
    gap: 5px;
    font-family: inherit;
    flex: 1;
    align-self: stretch;
    justify-content: center;
    align-items: center;
    padding: 0 12px;
    margin-top: 4px;
    border-top-right-radius: ${rtl("0", "4px")};
    border-bottom-right-radius: ${rtl("0", "4px")};
    border-top-left-radius: ${rtl("4px", "0px")};
    border-bottom-left-radius: ${rtl("4px", "0px")};
    cursor: pointer;
    width: max-content;
    border: 1px solid ${({ theme }) => theme.palette.ink.lighter};
    border-left: ${rtl(
      `1px solid ${({ theme }) => theme.palette.ink.lighter}`,
      "none"
    )};
    border-right: ${rtl(
      "none",
      `1px solid ${({ theme }) => theme.palette.ink.lighter}`
    )};
  }
`;

const FileName = styled.span`
  font-size: 16px;
  font-weight: 400;
  color: ${({ theme }) => theme.palette.ink.normal};
  opacity: 0.5;
`;
