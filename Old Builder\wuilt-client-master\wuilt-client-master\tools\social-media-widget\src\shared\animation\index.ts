import { css } from "styled-components";
import * as Animations from "./animation-css";
import { AnimationEnum } from "../types";

export const AnimationBasisStyle = css`
  animation-duration: 2s;
  animation-timing-function: ease;
  animation-delay: 1s;
  animation-iteration-count: infinite;
  animation-direction: normal;
  animation-fill-mode: none;
  animation-play-state: running;
  animation-timeline: auto;
`;

export const AnimationMapper = {
  [AnimationEnum.None]: "",
  [AnimationEnum.Bounce]: Animations.bounce,
  [AnimationEnum.DoubleRing]: Animations.doubleRing,
  [AnimationEnum.HeadShake]: Animations.headShake,
  [AnimationEnum.Heartbeat]: Animations.heartBeat,
  [AnimationEnum.Jello]: Animations.jello,
  [AnimationEnum.MoveIn]: Animations.moveIn,
  [AnimationEnum.Pulse]: Animations.pulse,
  [AnimationEnum.Ring]: Animations.ring,
  [AnimationEnum.RubberBand]: Animations.rubberBand,
  [AnimationEnum.Tada]: Animations.tada,
  [AnimationEnum.Wobble]: Animations.wobble,
};
