import React from "react";
import {
  Props as ReactSelectProps,
  OnChangeValue,
  ActionMeta,
  Options as RSOptionsType,
  SelectComponentsConfig,
  StylesConfig,
  ControlProps,
  GroupProps,
  InputProps,
  MenuProps,
  MultiValueProps,
  OptionProps as ReactSelectOptionProps,
  PlaceholderProps,
  SingleValueProps,
  ValueContainerProps,
  ClearIndicatorProps,
} from "react-select";
// import { AsyncProps as ReactAsyncSelectProps } from "react-select/async";
type ReactAsyncSelectProps = any;
export type ValidationState = "default" | "error" | "success";

export interface OptionType {
  label: React.ReactNode;
  value: string | number;
  [key: string]: any;
}

export type OptionsType<Option = OptionType> = RSOptionsType<Option>;

export interface OptionProps<Option = OptionType | null>
  extends ReactSelectOptionProps<Option> {
  [key: string]: any;
  Icon?: React.ComponentType<{
    label: string;
    // label?: string;
    size?: "small" | "medium" | "large" | "xlarge";
    onClick?: (e: MouseEvent) => void;
    primaryColor?: string;
    secondaryColor?: string;
  }>;
  isDisabled: boolean;
  isFocused: boolean;
  isSelected: boolean;
}

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
export interface SelectProps<OptionType>
  extends ReactSelectProps<OptionType | null> {
  /* This prop affects the height of the select control. Compact is gridSize() * 4, default is gridSize * 5  */
  spacing?: "compact" | "default";
  /* The state of validation if used in a form */
  validationState?: ValidationState;
  // for setting background color from outside
  bgColor?: string;
  // indicating an error in the user input
  isError?: boolean;
  // defaultValue prop
  defaultValue?: OptionType | null;
  // cast onChange value type
  onChange?: (value: OptionType, actionMeta: ActionMeta<OptionType>) => void;
}

type SelectValueType<T> = OnChangeValue<T, boolean>;

export {
  ActionMeta,
  ControlProps,
  GroupProps,
  InputProps,
  MenuProps,
  MultiValueProps,
  PlaceholderProps,
  ReactAsyncSelectProps,
  ReactSelectProps,
  SelectComponentsConfig,
  SingleValueProps,
  StylesConfig,
  ValueContainerProps,
  OnChangeValue,
  SelectValueType,
  ClearIndicatorProps,
};
