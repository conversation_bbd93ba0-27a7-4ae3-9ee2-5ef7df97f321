import React from "react";
import styled from "styled-components";
import { Card, CardProps } from "../Card";
import { Stack } from "../Stack";

const Container = styled.div<{ padding }>`
  padding: ${({ padding }) => padding};
`;

const StyledHeading = styled.h2`
  color: ${({ theme }) => theme.palette.ink.normal};
  text-align: center;
`;

const StyledNote = styled.p`
  color: ${({ theme }) => theme.palette.ink.light};
  text-align: center;
`;

export interface EmptyStateProps {
  title?: React.ReactNode;
  note?: React.ReactNode;
  primaryAction?: React.ReactNode;
  illustration?: React.ReactNode;
  cardProps?: CardProps;
  padding?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  primaryAction,
  illustration,
  note,
  cardProps,
  padding = "60px 30px",
}) => {
  return (
    <Card {...cardProps}>
      <Container padding={padding}>
        <Stack direction="column" align="center" justify="center">
          {illustration && illustration}
          <StyledHeading>{title && title}</StyledHeading>
          <StyledNote>{note && note}</StyledNote>
          {primaryAction && primaryAction}
        </Stack>
      </Container>
    </Card>
  );
};

EmptyState.displayName = "EmptyState";

export { EmptyState };
