import { css } from "styled-components";

import { ALIGNS } from "../consts";

const resolvePopoverHorizontal = ({
  anchor,
  containerLeft,
  containerWidth,
  popoverWidth,
}) => {
  if (anchor === ALIGNS.START) {
    return css`
      left: ${Math.floor(containerLeft)}px;
    `;
  }
  if (anchor === ALIGNS.CENTER) {
    return css`
      left: ${Math.floor(
        containerLeft + containerWidth / 2 - popoverWidth / 2
      )}px; /* TODO: use token */
    `;
  }
  if (anchor === ALIGNS.END) {
    return css`
      left: ${Math.floor(
        containerLeft + containerWidth - popoverWidth
      )}px; /* TODO: use token */
    `;
  }
  return null;
};

export default resolvePopoverHorizontal;
