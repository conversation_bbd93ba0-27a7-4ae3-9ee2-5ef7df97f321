import {
  system,
  TypographyProps as SystemProps,
  typography as SystemTypography,
  compose,
} from "styled-system";
import { theme } from "../theme";

const OverrideTypography = system({
  fontSize: {
    property: "fontSize",
    scale: "base.fontSize",
  },
  fontWeight: {
    property: "fontWeight",
    scale: "base.fontWeight",
  },
  transform: {
    property: "textTransform",
    scale: "base.textTransform",
  },
});

export const Typography = compose(SystemTypography, OverrideTypography);

export type TypographyProps = Omit<SystemProps, "textAlign"> & {
  fontSize?: keyof typeof theme.base.fontSize;
  fontWeight?: keyof typeof theme.base.fontWeight;
  transform?: keyof typeof theme.base.textTransform;
};
