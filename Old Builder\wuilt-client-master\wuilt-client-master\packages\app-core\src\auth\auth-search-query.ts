export function getSearchQueries() {
  const query = new URLSearchParams(window.location.search);
  const loginAction = query.get("loginAction");
  const fpr = query.get("fpr") || localStorage.getItem("fpr") || "";
  const redirect = localStorage.getItem("loginRedirect");
  const redirectOut =
    query.get("redirectOut") || localStorage.getItem("redirectOut");
  return { loginAction, redirect, fpr, redirectOut };
}

export function setSearchQueries(
  fpr: string,
  currentPath?: string | null,
  redirectOut?: string | null
) {
  if (currentPath && currentPath !== "/") {
    localStorage.setItem("loginRedirect", currentPath);
  }
  if (fpr) {
    localStorage.setItem("fpr", fpr);
  }
  if (redirectOut) {
    localStorage.setItem("redirectOut", redirectOut);
  }
}

export function clearSearchQueries() {
  localStorage.removeItem("loginRedirect");
  localStorage.removeItem("redirectOut");
  localStorage.removeItem("fpr");
}
