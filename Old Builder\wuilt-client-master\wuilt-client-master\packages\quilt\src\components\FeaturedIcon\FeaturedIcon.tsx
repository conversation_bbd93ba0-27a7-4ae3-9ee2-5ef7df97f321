import React from "react";
import { Stack } from "../Stack";

export enum IconType {
  danger = "danger",
  success = "success",
  warning = "warning",
  info = "info",
}

const theme = {
  success: {
    bgColor: { product: "lightHover" },
    borderColor: { product: "light" },
  },
  danger: {
    bgColor: { red: "lightHover" },
    borderColor: { red: "light" },
  },
  warning: {
    bgColor: { orange: "lightHover" },
    borderColor: { orange: "light" },
  },
  info: {
    bgColor: { cloud: "lightHover" },
    borderColor: { cloud: "light" },
  },
};

export interface FeaturedIconProps {
  type: keyof typeof IconType;
  icon?: React.ReactNode;
}
const FeaturedIcon: React.FC<FeaturedIconProps> = ({ icon, type }) => {
  return (
    <Stack
      bgColor={theme[type].bgColor}
      border="8px solid"
      borderColor={theme[type].borderColor}
      padding="6px"
      align="center"
      justify="center"
      width="fit-content"
      borderRadius="50%"
    >
      {icon}
    </Stack>
  );
};
export { FeaturedIcon };
