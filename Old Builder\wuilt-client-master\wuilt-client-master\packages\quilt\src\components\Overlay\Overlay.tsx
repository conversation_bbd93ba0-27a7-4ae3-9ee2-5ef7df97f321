import React from "react";
import styled, { useTheme } from "styled-components";
import { convertHexToRgba } from "../../utils";
import { BoxProps, Box } from "../Box";

export interface OverlayProps extends BoxProps {
  percentage?: number;
  hide?: boolean;
}

const Overlay: React.FC<OverlayProps> = ({
  children,
  percentage = 50,
  color: colorProp = "overlay",
  hide,
  ...restProps
}) => {
  const theme = useTheme();
  let color;
  if (typeof colorProp === "string") {
    color = theme?.base?.colors?.[colorProp];
  } else {
    const mainColor = Object.keys(colorProp)[0];
    const variantColor = Object.values(colorProp)[0] as string;
    color = theme?.palette?.[mainColor]?.[variantColor];
  }

  if (hide) {
    return null;
  }

  return (
    <StyledBox {...restProps} color={color} percentage={percentage}>
      {children}
    </StyledBox>
  );
};

Overlay.displayName = "Overlay";
export { Overlay };

const StyledBox = styled(Box)<{ percentage; color }>`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  background: ${({ percentage, color }) => convertHexToRgba(color, percentage)};
`;
