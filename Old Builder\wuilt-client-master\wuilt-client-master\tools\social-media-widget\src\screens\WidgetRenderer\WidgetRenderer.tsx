import React, { useState } from "react";
import { AppsEnum, TWidgetSettings } from "../../shared/types";
import WidgetContainer from "./WidgetContainer";
import { useShouldRender } from "./utils";
import WidgetApps from "./WidgetApps";
import WidgetController from "./WidgetController";
import WhatsAppWidget from "./WhatsAppWidget";
import { LocaleEnum } from "../../main";

interface WidgetRendererProps {
  maxHeight?: string;
  settings: TWidgetSettings;
  alwaysShow?: boolean;
  locale: LocaleEnum;
}

const WidgetRenderer: React.FC<WidgetRendererProps> = ({
  settings,
  maxHeight,
  alwaysShow,
  locale,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isWhatsAppOpen, setIsWhatsAppOpen] = useState(false);
  const shouldRender = useShouldRender(settings);

  const onToggle = () => setIsOpen((prev) => !prev);
  const onClose = () => setIsOpen(false);

  const handleCloseWhatsappWidget = () => {
    setIsWhatsAppOpen(false);
  };

  if (!alwaysShow && !shouldRender) {
    return null;
  }

  return (
    <WidgetContainer settings={settings} locale={locale}>
      <WidgetController
        isOpen={isOpen}
        settings={settings}
        onToggle={onToggle}
      />
      <WidgetApps
        maxHeight={maxHeight}
        isOpen={isOpen}
        onClose={onClose}
        settings={settings}
        openWhatsappWidget={() => setIsWhatsAppOpen(true)}
      />
      <WhatsAppWidget
        widget={settings}
        app={settings?.apps?.find((app) => app?.name === AppsEnum.Whatsapp)}
        isOpen={isWhatsAppOpen}
        onClose={handleCloseWhatsappWidget}
      />
    </WidgetContainer>
  );
};

export { WidgetRenderer };
