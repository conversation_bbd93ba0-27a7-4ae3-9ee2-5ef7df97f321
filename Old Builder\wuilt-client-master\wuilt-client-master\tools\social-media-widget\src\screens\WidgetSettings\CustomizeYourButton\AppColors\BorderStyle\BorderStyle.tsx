import {
  Box,
  InputField,
  InputGroup,
  Stack,
  Text,
  ToggleButton,
} from "@wuilt/quilt";
import React, { useState } from "react";
import {
  TWidgetAppearance,
  TWidgetSettings,
} from "../../../../../shared/types";
import ColorInput from "../../../ChooseMessaging/ColorInput";
import { FormattedMessage } from "react-intl";
interface BorderStyleProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function BorderStyle({ appearance, update }: BorderStyleProps) {
  const {
    style: { border },
  } = appearance;
  const [toggleBorder, setToggleBorder] = useState(false);
  return (
    <Box mt="16px">
      <Stack
        justify="between"
        align="center"
        minHeight="44px"
        direction="row"
        desktop={{ direction: "column", align: "start" }}
      >
        <Box width="224px">
          <ToggleButton
            activeColor="#0E9384"
            disableColor="#F2F4F7"
            sliderBoxShadow="0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A"
            label={
              <Text
                fontSize="sm"
                fontWeight="medium"
                style={{ color: "#1D2939" }}
              >
                <FormattedMessage defaultMessage="Border" id="sGaEsd" />
              </Text>
            }
            hideIcons
            value={toggleBorder}
            onChange={(value) => {
              setToggleBorder(value);
              if (!value) {
                update({
                  appearance: {
                    ...appearance,
                    style: {
                      ...appearance.style,
                      border: {
                        color: "#FFFFFF",
                        thickness: "0px",
                      },
                    },
                  },
                });
              }
            }}
          />
        </Box>

        {toggleBorder && (
          <Stack boxShadow="0px 1px 2px 0px #1018280D" spacing="0" width="100%">
            <InputGroup borderRadius="8px" ElementsFlexGrow={[1, 0]}>
              <ColorInput
                value={border?.color || "transparent"}
                customStyle={{
                  borderStartEndRadius: "0",
                  borderEndEndRadius: "0",
                  borderInlineEnd: "none",
                }}
                onChange={(color) => {
                  setToggleBorder(true);
                  update({
                    appearance: {
                      ...appearance,
                      style: {
                        ...appearance.style,
                        border: { ...border!, color: color },
                      },
                    },
                  });
                }}
              />

              <InputField
                type="number"
                width="100px"
                minWidth="30px"
                suffix={<Text fontSize="medium" children="px" />}
                value={border?.thickness?.replace("px", "") || "0"}
                onChange={(value) => {
                  setToggleBorder(true);
                  update({
                    appearance: {
                      ...appearance,
                      style: {
                        ...appearance.style,
                        border: { ...border!, thickness: `${value}px` },
                      },
                    },
                  });
                }}
              />
            </InputGroup>
          </Stack>
        )}
      </Stack>
    </Box>
  );
}

export default BorderStyle;
