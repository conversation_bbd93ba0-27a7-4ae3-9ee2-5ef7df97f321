import React from "react";
import styled from "styled-components";
import { Accordion } from "./Accordion";
import { Stack } from "../Stack/Stack";
import { Text } from "../Text";
import { Heading } from "../Heading";
import { color } from "../../utils";

export default {
  title: "Components/Accordion",
  component: Accordion,
};

export const Playground = () => {
  const [isOpen, setIsOpen] = React.useState(false);

  const [active, setActive] = React.useState(-1);

  return (
    <div>
      <Stack>
        <Accordion>
          <Accordion.Header>
            <Heading>Accordion 1 - Controlled from inside</Heading>
          </Accordion.Header>
          <Accordion.Body>
            <Text>
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Ratione
              atque soluta enim natus corporis ullam vero? Cumque optio
            </Text>
          </Accordion.Body>
        </Accordion>

        <Accordion value={isOpen} onChange={() => setIsOpen((prev) => !prev)}>
          <Accordion.Header>
            <Heading>Accordion 2 - Controlled from outside</Heading>
          </Accordion.Header>
          <Accordion.Body>
            <Text>
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Ratione
              atque soluta enim natus corporis ullam vero? Cumque optio
            </Text>
          </Accordion.Body>
        </Accordion>

        <StyledAccordion initial hasCustomStyle>
          <Accordion.Header openOnIconClickOnly>
            <Heading>Accordion 3 - Custom and initially opened</Heading>
          </Accordion.Header>
          <Accordion.Body>
            <Text>
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Ratione
              atque soluta enim natus corporis ullam vero? Cumque optio
            </Text>
          </Accordion.Body>
        </StyledAccordion>
      </Stack>
      <br />
      <br />
      <Heading>Group of Accordions</Heading>
      <br />

      <Accordion
        value={active === 1}
        onChange={(value) => setActive(value ? 1 : -1)}
      >
        <Accordion.Header>
          <Heading>Accordion 1 </Heading>
        </Accordion.Header>
        <Accordion.Body>
          <Text>
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Ratione
            atque soluta enim natus corporis ullam vero? Cumque optio
          </Text>
        </Accordion.Body>
      </Accordion>

      <Accordion
        value={active === 2}
        onChange={(value) => setActive(value ? 2 : -1)}
      >
        <Accordion.Header>
          <Heading>Accordion 2 </Heading>
        </Accordion.Header>
        <Accordion.Body>
          <Text>
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Ratione
            atque soluta enim natus corporis ullam vero? Cumque optio
          </Text>
        </Accordion.Body>
      </Accordion>

      <Accordion
        value={active === 3}
        onChange={(value) => setActive(value ? 3 : -1)}
      >
        <Accordion.Header>
          <Heading>Accordion 3</Heading>
        </Accordion.Header>
        <Accordion.Body>
          <Text>
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Ratione
            atque soluta enim natus corporis ullam vero? Cumque optio
          </Text>
        </Accordion.Body>
      </Accordion>
    </div>
  );
};

const StyledAccordion = styled(Accordion)`
  background-color: ${color("green", "light")};

  &:hover {
    background-color: ${color("green", "lightHover")};
  }
`;
