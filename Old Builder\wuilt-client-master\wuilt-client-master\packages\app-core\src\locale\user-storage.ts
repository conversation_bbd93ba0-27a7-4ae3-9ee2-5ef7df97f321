import type { Locale } from "./local-provider";

declare global {
  interface Window {
    Beamer: any;
    beamer_config: any;
  }
}

type BeamerUserInfo = {
  user_firstname?: string;
  user_lastname?: string;
  user_email?: string;
  user_id?: string;
};

function updateBeamer(
  display: string,
  language: string,
  user: BeamerUserInfo = {}
) {
  window.Beamer?.update({
    display,
    language,
    ...user,
  });

  window.beamer_config = {
    ...window.beamer_config,
    display,
    language,
  };
}

function updateDocument(dir: "ltr" | "rtl") {
  document.body.classList.toggle("rtl", dir === "rtl");
  document.body.classList.toggle("ltr", dir !== "rtl");
}

function updateStorage(locale: string) {
  localStorage.setItem("locale", JSON.stringify(locale));
}

export function updateUserStorage(locale: Locale) {
  updateBeamer(locale.display, locale.code.toUpperCase());
  updateStorage(locale.locale);
  updateDocument(locale.dir);
}
