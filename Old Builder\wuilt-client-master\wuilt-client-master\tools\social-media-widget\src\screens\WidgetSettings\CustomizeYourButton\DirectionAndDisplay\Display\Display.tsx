import { Box, Checkbox, DesktopIcon, MobileIcon, Stack } from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import {
  TWidgetAppearance,
  TWidgetSettings,
} from "../../../../../shared/types";
import CustomText from "../../../ui/CustomText";
interface DisplayProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function Display({ appearance, update }: DisplayProps) {
  const showOnDesktop = appearance.display.showOnDesktop;
  const showOnMobile = appearance.display.showOnMobile;
  return (
    <Box width="100%">
      <CustomText>
        <FormattedMessage defaultMessage="Display" id="64NAin" />
      </CustomText>
      <Stack mt="6px">
        <Checkbox
          value={showOnDesktop}
          onChange={(value) => {
            update({
              appearance: {
                ...appearance,
                display: { ...appearance.display, showOnDesktop: value },
              },
            });
          }}
          label={
            <Stack spacing="condensed" align="center" direction="row">
              <DesktopIcon
                customColor="#667085"
                className="direction-and-display-icon"
                viewBox="0 0 20 20"
              />
              <CustomText>
                <FormattedMessage defaultMessage="Desktop" id="tHSb8p" />
              </CustomText>
            </Stack>
          }
        />
        <Checkbox
          value={showOnMobile}
          onChange={(value) => {
            update({
              appearance: {
                ...appearance,
                display: { ...appearance.display, showOnMobile: value },
              },
            });
          }}
          label={
            <Stack spacing="condensed" align="center" direction="row">
              <MobileIcon
                customColor="#667085"
                className="direction-and-display-icon"
                viewBox="0 0 20 20"
              />
              <CustomText>
                <FormattedMessage defaultMessage="Mobile" id="GWtmtu" />
              </CustomText>
            </Stack>
          }
        />
      </Stack>
    </Box>
  );
}

export default Display;
