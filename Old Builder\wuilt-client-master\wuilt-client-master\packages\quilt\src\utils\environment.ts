const TLD_STAGE_MAPPER = {
  dev: "canary",
  in: "staging",
  com: "master",
};

export function browserEnv() {
  let TLD = "";
  let domain = "";
  if (typeof window !== "undefined") {
    domain = window.location.hostname;
    const arr = domain.split(".");
    TLD = arr[arr.length - 1];
  }

  return {
    TLD,
    domain,
    env: process.env.NODE_ENV,
    isDev: domain.startsWith("local"),
    stage: TLD_STAGE_MAPPER[TLD],
  };
}
