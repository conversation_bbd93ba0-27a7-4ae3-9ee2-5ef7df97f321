import { Heading, LaunchingIllustration, Stack } from "@wuilt/quilt";
import React from "react";
import { GetButton } from "../GetButton";
import { LivePreview } from "../LivePreview";
import { FormattedMessage } from "react-intl";
import { LocaleEnum } from "../../../main";
interface ActivateYourButtonProps {
  locale: LocaleEnum;
}

const ActivateYourButton: React.FC<ActivateYourButtonProps> = ({ locale }) => {
  return (
    <Stack>
      <Stack direction="row" align="center">
        <LaunchingIllustration className="title-icon" color="red" />
        <Heading variant="h1" fontSize="xl" fontWeight="semiBold">
          <FormattedMessage
            defaultMessage="Activate your button and add it to your website"
            id="Cm0xrY"
          />
        </Heading>
      </Stack>
      <Stack tablet={{ direction: "column" }} direction="row" spacing="comfy">
        <GetButton />
        <LivePreview locale={locale} />
      </Stack>
    </Stack>
  );
};

export { ActivateYourButton };
