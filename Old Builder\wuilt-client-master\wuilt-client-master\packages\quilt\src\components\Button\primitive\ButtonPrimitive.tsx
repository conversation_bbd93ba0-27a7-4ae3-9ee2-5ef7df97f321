import React from "react";
import styled, { css, DefaultTheme } from "styled-components";
import { ButtonPrimitiveProps } from "./ButtonPrimitive.types";
import Content from "./Content";
import IconContainer, { StyledIconContainer } from "./IconContainer";
import ContentChildren from "./ContentChildren";
import { Spinner } from "../../Spinner/Spinner";
import getContentAlign from "../helpers/getContentAlign";
import getSpacingToken from "../../../common/getSpacingToken";
import { UnstyledLink } from "../../UnstyledLink";
import SpinnerContainer from "./SpinnerContainer";

function filterButtonProps(
  props: ButtonPrimitiveProps & { onlyIcon?: boolean }
) {
  const {
    asComponent,
    ariaControls,
    ariaExpanded,
    children,
    squared,
    rounded,
    stopOpacity,
    disabled,
    external,
    fullWidth,
    href,
    to,
    prefixIcon,
    suffixIcon,
    loading,
    onClick,
    role,
    size,
    submit,
    contentAlign,
    contentWidth,
    title,
    tabIndex,
    width,
    height,
    fontSize,
    foreground,
    foregroundHover,
    foregroundActive,
    foregroundFocus,
    background,
    backgroundHover,
    backgroundActive,
    backgroundFocus,
    boxShadow,
    boxShadowHover,
    boxShadowActive,
    boxShadowFocus,
    padding,
    fontWeight,
    icons,
    borderColor,
    borderColorActive,
    borderColorFocus,
    borderColorHover,
    onlyIcon,
    underlined,
    borderRadius,
    ...restProps
  } = props;
  return restProps;
}

const iconContainerColor = (color?: string) => css`
  ${StyledIconContainer} {
    color: ${color};
  }
`;

const Button = ({
  asComponent = "button",
  dataTest,
  submit,
  disabled,
  forwardedRef,
  ariaControls,
  ariaExpanded,
  title,
  className,
  rel,
  href,
  target,
  external,
  tabIndex,
  onClick,
  role,
  to,
  ...props
}) => {
  const Component: any = href ? "a" : to ? UnstyledLink : asComponent;
  const buttonType = submit ? "submit" : "button";
  const isLink = href || to;

  return (
    <Component
      ref={forwardedRef}
      data-test={dataTest}
      aria-controls={ariaControls}
      aria-expanded={ariaExpanded}
      aria-label={title}
      type={to || href ? undefined : buttonType}
      className={className}
      disabled={disabled}
      href={!disabled ? href : null}
      to={!disabled ? to : null}
      target={!disabled && isLink && external ? "_blank" : undefined}
      rel={!disabled && isLink && external ? "noopener noreferrer" : undefined}
      tabIndex={tabIndex}
      onClick={disabled ? undefined : onClick}
      role={role}
      title={title}
      {...filterButtonProps(props)}
    >
      {props.children}
    </Component>
  );
};

const buttonStyles = ({
  foreground,
  disabled,
  fullWidth,
  href,
  theme,
  asComponent,
  squared,
  padding,
  background,
  fontWeight,
  fontSize,
  height,
  width,
  onlyIcon,
  icons,
  contentAlign,
  foregroundHover,
  foregroundActive,
  foregroundFocus,
  backgroundHover,
  backgroundActive,
  backgroundFocus,
  borderColor,
  borderColorHover,
  borderColorFocus,
  borderColorActive,
  boxShadow,
  boxShadowHover,
  boxShadowFocus,
  boxShadowActive,
  underlined,
  spaceAfter,
  cursor,
  loading,
  borderRadius,
}: Partial<ButtonPrimitiveProps> & { theme: DefaultTheme }) => {
  const containerColor = iconContainerColor(
    (icons && icons.foregroundHover) || undefined
  );

  return css`
    height: ${height};
    position: relative;
    display: ${href || asComponent === "a" ? "inline-flex" : "flex"};
    justify-content: space-between;
    box-sizing: border-box;
    appearance: none;
    text-decoration: ${underlined ? "underline" : "none"};
    flex: ${fullWidth ? "1 1 auto" : "0 0 auto"};
    /* to ensure that Buttons content wraps in IE */
    max-width: 100%;
    background: ${background};
    opacity: ${disabled && !loading ? 0.5 : 1};
    color: ${foreground};
    border: ${borderColor ? `1px solid ${borderColor}` : `0`};
    padding: ${padding};
    border-radius: ${squared ? "2px" : height};
    /* @ts-ignore */
    font-family: ${theme.base.fontFamily};
    font-weight: ${fontWeight || theme.base.fontWeight.medium};
    font-size: ${fontSize};
    /* preventing inheriting with safe value */
    line-height: 1.4;
    cursor: ${cursor || (disabled ? "not-allowed" : "pointer")};
    outline: none;
    width: ${fullWidth ? "100%" : width || (onlyIcon && height) || "auto"};
    box-shadow: ${boxShadow};
    text-align: ${getContentAlign(contentAlign || "center")};
    margin-bottom: ${getSpacingToken({ spaceAfter, theme })};
    ${borderRadius && `border-radius:${borderRadius}`};
    ${containerColor};

    &:hover {
      ${!disabled &&
      css`
        outline: none;
        background: ${backgroundHover};
        color: ${foregroundHover};
        border-color: ${borderColorHover};
        box-shadow: ${boxShadowHover};
        text-decoration: none;
        ${containerColor};
      `};
    }

    &:active {
      ${!disabled &&
      css`
        outline: none;
        background: ${backgroundActive};
        box-shadow: ${boxShadowActive};
        color: ${foregroundActive};
        border-color: ${borderColorActive};
        text-decoration: none;
        ${containerColor};
      `};
    }

    :focus {
      outline: none;
      box-shadow: ${boxShadowFocus};
      background: ${backgroundFocus};
      color: ${foregroundFocus};
      border-color: ${borderColorFocus};
      text-decoration: none;
      ${containerColor};
    }

    :visited {
      outline: none;
      box-shadow: ${boxShadowFocus};
      background: ${backgroundFocus};
      color: ${foregroundFocus};
      border-color: ${borderColorFocus};
      text-decoration: none;
      ${containerColor};
    }
  `;
};

export const StyledButtonPrimitive = styled(Button)`
  ${buttonStyles};
`;

const ButtonPrimitive = React.forwardRef<
  HTMLButtonElement,
  ButtonPrimitiveProps
>((props, ref) => {
  const {
    loading,
    loadingtext = "",
    disabled,
    children,
    prefixIcon,
    suffixIcon,
    icons = {
      width: null,
      height: null,
      leftMargin: null,
      rightMargin: null,
    },
    contentAlign = "center",
    contentWidth,
  } = props;
  const { width, height, leftMargin, rightMargin } = icons;
  const isDisabled = loading || disabled;
  const onlyIcon = Boolean(prefixIcon && !children);

  return (
    <StyledButtonPrimitive
      forwardedRef={ref}
      onlyIcon={onlyIcon}
      {...props}
      disabled={isDisabled}
    >
      <Content contentAlign={contentAlign}>
        {prefixIcon && !loading && (
          <IconContainer width={width} height={height} margin={leftMargin}>
            {prefixIcon}
          </IconContainer>
        )}

        {loading && (
          <SpinnerContainer>
            <Spinner color={props.foreground || undefined} size="xsmall" />
            {loadingtext && (
              <span style={{ margin: "4px" }}> {loadingtext} </span>
            )}
          </SpinnerContainer>
        )}
        {children && (
          <ContentChildren
            hasIcon={Boolean(prefixIcon || suffixIcon)}
            contentWidth={contentWidth}
            isLoading={loading}
          >
            {children}
          </ContentChildren>
        )}

        {!onlyIcon && suffixIcon && !loading && (
          <IconContainer width={width} height={height} margin={rightMargin}>
            {suffixIcon}
          </IconContainer>
        )}
      </Content>
    </StyledButtonPrimitive>
  );
});

ButtonPrimitive.displayName = "ButtonPrimitive";

export { ButtonPrimitive };
