echo "%~dp0..\packages\"
wt.exe ^
-p PowerShell -d "%~dp0../packages/stores-admin" --title stores-admin pwsh.exe -c "yarn start"; ^
split-pane -V -p PowerShell -d "%~dp0../packages/stores-admin" --title stores-admin-ws ; ^
new-tab -p PowerShell -d "%~dp0../packages/quilt" --title quilt pwsh.exe -c "yarn storybook"; ^
split-pane -V -p PowerShell -d "%~dp0../packages/quilt" --title quilt-ws ; ^
focus-tab -t 0