import React, { ReactElement } from "react";
import styled, { CSSProperties } from "styled-components";
import { AddImageIllustration } from "../../illustrations";
import { Button } from "../../Button";

export interface FileUploadProps {
  actionText?: any;
  formatsNote?: any;
  note?: any;
  disabled?: boolean;
  illustration?: ReactElement;
  hideActionButton?: boolean;
  backgroundColor?: CSSProperties["background"];
  width?: CSSProperties["width"];
  height?: CSSProperties["height"];
}

const FileUploadContainer = styled.div<{
  backgroundColor?: CSSProperties["background"];
  width?: CSSProperties["width"];
  height?: CSSProperties["height"];
}>`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='7' ry='7' stroke='%23dfe3e8' stroke-width='5' stroke-dasharray='6%2c 14' stroke-dashoffset='22' stroke-linecap='square'/%3e%3c/svg%3e");
  border-radius: 7px;
  padding: ${(props) => (props.width || props.height ? "0px" : "32px")};
  background-color: ${(props) => props.backgroundColor || "none"};
  width: ${(props) => props.width || "auto"};
  height: ${(props) => props.height || "auto"};
`;

export const FileUpload: React.FC<FileUploadProps> = (props) => (
  <FileUploadContainer
    backgroundColor={props.backgroundColor}
    width={props.width}
    height={props.height}
  >
    {props.illustration || <AddImageIllustration />}
    {!props.hideActionButton && (
      <Button disabled={props.disabled}>{props.actionText}</Button>
    )}
    {props.formatsNote && (
      <p style={{ color: "#bac7d5" }}>{props.formatsNote}</p>
    )}
    {props.note && <p style={{ color: "#637381" }}>{props.note}</p>}
  </FileUploadContainer>
);
