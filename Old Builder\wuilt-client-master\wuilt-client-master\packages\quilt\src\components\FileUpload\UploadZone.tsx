import React from "react";
import debounce from "lodash/debounce";
import { FileUpload } from "./FileUpload";
import { isServer } from "../../utils/target";
import { useToggle } from "../../hooks/useToggle";
import { fileAccepted, getDataTransferFiles } from "./utils";
import { DropZoneContext } from "./context";
import { useUniqueId } from "../../utils/unique-id";
import { VisuallyHidden } from "../VisuallyHidden";
import { Global } from "../../common/types";

type Type = "file" | "image";

export interface UploadZoneProps extends Global {
  /** Label for the file input */
  label?: string;
  /** Adds an action to the label */
  labelAction?: any; // LabelledProps['action'];
  /** Visually hide the label */
  labelHidden?: boolean;
  /** ID for file input */
  id?: string;
  /** Allowed file types */
  accept?: string;
  /**
   * Whether is a file or an image
   * @default 'file'
   */
  type?: Type;
  /** Sets an active state */
  active?: boolean;
  /** Sets an error state */
  error?: boolean;
  /**
   * Displays an outline border
   * @default true
   */
  outline?: boolean;
  /**
   * Displays an overlay on hover
   * @default true
   */
  overlay?: boolean;
  /** Text that appears in the overlay */
  overlayText?: string;
  /** Text that appears in the overlay when set in error state */
  errorOverlayText?: string;
  /**
   * Allows multiple files to be uploaded at once
   * @default true
   */
  allowMultiple?: boolean;
  /** Sets a disabled state */
  disabled?: boolean;
  /** The child elements to render in the dropzone. */
  children?: string | React.ReactNode;
  /** Allows a file to be dropped anywhere on the page */
  dropOnPage?: boolean;
  /** Sets the default file dialog state */
  openFileDialog?: boolean;
  /** Adds custom validations */
  customValidator?(file: File): boolean;
  /** Callback triggered on click */
  onClick?(event: React.MouseEvent<HTMLElement>): void;
  /** Callback triggered on any file drop */
  onDrop?(
    files: File[],
    acceptedFiles: File[],
    rejectedFiles: { file: File; error: string }[]
  ): void;
  /** Callback triggered when at least one of the files dropped was accepted */
  onDropAccepted?(acceptedFiles: File[]): void;
  /** Callback triggered when at least one of the files dropped was rejected */
  onDropRejected?(rejectedFiles: File[]): void;
  /** Callback triggered when one or more files are dragging over the drag area */
  onDragOver?(): void;
  /** Callback triggered when one or more files entered the drag area */
  onDragEnter?(): void;
  /** Callback triggered when one or more files left the drag area */
  onDragLeave?(): void;
  /** Callback triggered when the file dialog is canceled */
  onFileDialogClose?(): void;
}

export enum FileUploadError {
  FILE_SIZE_TOO_LARGE = "FILE_SIZE_TOO_LARGE",
  FILE_TYPE_NOT_ACCEPTED = "FILE_TYPE_NOT_ACCEPTED",
}

// TypeScript can't generate types that correctly infer the typing of
// subcomponents so explicitly state the subcomponents in the type definition.
// Letting this be implicit works in this project but fails in projects that use
// generated *.d.ts files.
export const UploadZone: React.FunctionComponent<UploadZoneProps> & {
  FileUpload: typeof FileUpload;
} = function UploadZone({
  dropOnPage,
  dataTest = "upload-image",
  children,
  disabled = false,
  accept,
  allowMultiple = true,
  id: idProp,
  type = "file",
  onClick,
  openFileDialog,
  onFileDialogClose,
  customValidator,
  onDrop,
  onDropAccepted,
  onDropRejected,
  onDragEnter,
  onDragOver,
  onDragLeave,
}: UploadZoneProps) {
  const node = React.useRef<HTMLDivElement>(null);
  const dragTargets = React.useRef<EventTarget[]>([]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const adjustSize = React.useCallback(
    debounce(
      () => {
        if (!node.current) {
          return;
        }

        let size = "extraLarge";
        const { current } = node;
        const t = current.getBoundingClientRect();
        const { width } = t;

        if (width < 100) {
          size = "small";
        } else if (width < 160) {
          size = "medium";
        } else if (width < 300) {
          size = "large";
        }

        setSize(size);
        measuring && setMeasuring(false);
      },
      50,
      { trailing: true }
    ),
    []
  );

  const [dragging, setDragging] = React.useState(false);
  const [, setInternalError] = React.useState(false);
  const {
    value: focused,
    setTrue: handleFocus,
    setFalse: handleBlur,
  } = useToggle(false);
  const [size, setSize] = React.useState("extraLarge");
  const [measuring, setMeasuring] = React.useState(true);

  // const i18n = useI18n();

  const getValidatedFiles = React.useCallback(
    (files: File[] | DataTransferItem[]) => {
      const acceptedFiles: File[] = [];
      const rejectedFiles: { file: File; error: string }[] = [];

      Array.from(files as File[]).forEach((file: File) => {
        if (file.size > 10000000) {
          rejectedFiles.push({
            file,
            error: FileUploadError.FILE_SIZE_TOO_LARGE,
          });
        } else if (
          !fileAccepted(file, accept) ||
          (customValidator && !customValidator(file))
        ) {
          rejectedFiles.push({
            file,
            error: FileUploadError.FILE_TYPE_NOT_ACCEPTED,
          });
        } else {
          acceptedFiles.push(file);
        }
      });

      if (!allowMultiple) {
        // Keep only the first accepted file
        const firstAcceptedFile = acceptedFiles[0];
        acceptedFiles.length = 0;
        if (firstAcceptedFile) {
          acceptedFiles.push(firstAcceptedFile);
        }

        // Add all other files to rejected files with appropriate error
        acceptedFiles.slice(1).forEach((file) => {
          rejectedFiles.push({
            file,
            error: FileUploadError.FILE_TYPE_NOT_ACCEPTED,
          });
        });
      }

      return { files, acceptedFiles, rejectedFiles };
    },
    [accept, allowMultiple, customValidator]
  );

  const handleDrop = React.useCallback(
    (event: DragEvent) => {
      stopEvent(event);
      if (disabled) return;

      const fileList = getDataTransferFiles(event);

      const { files, acceptedFiles, rejectedFiles } =
        getValidatedFiles(fileList);

      dragTargets.current = [];

      setDragging(false);
      setInternalError(rejectedFiles.length > 0);

      onDrop && onDrop(files as File[], acceptedFiles, rejectedFiles);
      onDropAccepted && acceptedFiles.length && onDropAccepted(acceptedFiles);
      onDropRejected &&
        rejectedFiles.length &&
        onDropRejected(rejectedFiles.map((rf) => rf.file));

      (event.target as HTMLInputElement).value = "";
    },
    [disabled, getValidatedFiles, onDrop, onDropAccepted, onDropRejected]
  );

  const handleDragEnter = React.useCallback(
    (event: DragEvent) => {
      stopEvent(event);
      if (disabled) return;

      const fileList = getDataTransferFiles(event);

      if (event.target && !dragTargets.current.includes(event.target)) {
        dragTargets.current.push(event.target);
      }

      if (dragging) return;

      const { rejectedFiles } = getValidatedFiles(fileList);

      setDragging(true);
      setInternalError(rejectedFiles.length > 0);

      onDragEnter && onDragEnter();
    },
    [disabled, dragging, getValidatedFiles, onDragEnter]
  );

  const handleDragOver = React.useCallback(
    (event: DragEvent) => {
      stopEvent(event);
      if (disabled) return;
      onDragOver && onDragOver();
    },
    [disabled, onDragOver]
  );

  const handleDragLeave = React.useCallback(
    (event: DragEvent) => {
      event.preventDefault();

      if (disabled) return;

      dragTargets.current = dragTargets.current.filter(
        (el: EventTarget | Node) => {
          const compareNode = dropOnPage && !isServer ? document : node.current;
          return (
            el !== event.target &&
            compareNode &&
            compareNode.contains(el as Node)
          );
        }
      );

      if (dragTargets.current.length > 0) return;

      setDragging(false);
      setInternalError(false);

      onDragLeave && onDragLeave();
    },
    [dropOnPage, disabled, onDragLeave]
  );

  React.useEffect(() => {
    const dropNode = dropOnPage ? document : node.current;

    if (!dropNode) return;

    dropNode.addEventListener("drop", handleDrop as EventListener);
    dropNode.addEventListener("dragover", handleDragOver as EventListener);
    dropNode.addEventListener("dragenter", handleDragEnter as EventListener);
    dropNode.addEventListener("dragleave", handleDragLeave as EventListener);
    window.addEventListener("resize", adjustSize);

    return () => {
      dropNode.removeEventListener("drop", handleDrop as EventListener);
      dropNode.removeEventListener("dragover", handleDragOver as EventListener);
      dropNode.removeEventListener(
        "dragenter",
        handleDragEnter as EventListener
      );
      dropNode.removeEventListener(
        "dragleave",
        handleDragLeave as EventListener
      );
      window.removeEventListener("resize", adjustSize);
    };
  }, [
    dropOnPage,
    handleDrop,
    handleDragOver,
    handleDragEnter,
    handleDragLeave,
    adjustSize,
  ]);

  const id = useUniqueId("DropZone", idProp);

  const inputAttributes = {
    id,
    dataTest,
    accept,
    disabled,
    type: "file" as const,
    multiple: allowMultiple,
    onChange: handleDrop,
    onFocus: handleFocus,
    onBlur: handleBlur,
  };

  const context = React.useMemo(
    () => ({
      disabled,
      focused,
      size,
      type: type || "file",
      measuring,
    }),
    [disabled, focused, measuring, size, type]
  );

  return (
    <DropZoneContext.Provider value={context}>
      <div
        ref={node}
        aria-disabled={disabled}
        onClick={handleClick}
        onDragStart={stopEvent}
        style={{ width: "100%" }}
      >
        <div className="styles.Container">{children}</div>
        <VisuallyHidden>
          <DropZoneInput
            {...inputAttributes}
            openFileDialog={openFileDialog}
            onFileDialogClose={onFileDialogClose}
          />
        </VisuallyHidden>
      </div>
    </DropZoneContext.Provider>
  );

  function open() {
    const fileInputNode = node.current && node.current.querySelector(`#${id}`);
    fileInputNode &&
      fileInputNode instanceof HTMLElement &&
      fileInputNode.click();
  }

  function handleClick(event: React.MouseEvent<HTMLElement>) {
    if (disabled) return;

    return onClick ? onClick(event) : open();
  }
};

function stopEvent(event: DragEvent | React.DragEvent) {
  event.preventDefault();
  event.stopPropagation();
}

UploadZone.FileUpload = FileUpload;

interface DropZoneInputProps {
  id: string;
  dataTest: string;
  accept?: string;
  disabled: boolean;
  type: Type;
  multiple: boolean;
  openFileDialog?: boolean;
  onChange(event: DragEvent | React.ChangeEvent<HTMLInputElement>): void;
  onFocus(): void;
  onBlur(): void;
  onFileDialogClose?(): void;
}

// Due to security reasons, browsers do not allow file inputs to be opened artificially.
// For example `useEffect(() => { ref.click() })`. Oddly enough react class-based components bi-pass this.
class DropZoneInput extends React.Component<DropZoneInputProps, never> {
  private fileInputNode = React.createRef<HTMLInputElement>();

  componentDidMount() {
    this.props.openFileDialog && this.triggerFileDialog();
  }

  componentDidUpdate() {
    this.props.openFileDialog && this.triggerFileDialog();
  }

  render() {
    const { openFileDialog, onFileDialogClose, dataTest, ...inputProps } =
      this.props;
    return (
      <input
        {...inputProps}
        ref={this.fileInputNode}
        data-test={dataTest}
        autoComplete="off"
      />
    );
  }

  private triggerFileDialog = () => {
    this.open();
    this.props.onFileDialogClose && this.props.onFileDialogClose();
  };

  private open = () => {
    if (!this.fileInputNode.current) return;
    this.fileInputNode.current.click();
  };
}
