import React from "react";
import {
  AnimationEnum,
  TWidgetAppearance,
  TWidgetSettings,
} from "../../../../../shared/types";
import { Box, ChevronDownIcon, Stack, Text } from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import { Popover } from "../../../ui";
import { MessageChatSquareWhite } from "../../../../../shared/IconsList/ButtonIcons";
import styled from "styled-components";
import {
  AnimationMapper,
  AnimationBasisStyle,
} from "../../../../../shared/animation";
import CustomText from "../../../ui/CustomText";

const ANIMATION = Object.keys(AnimationEnum) as AnimationEnum[];
interface ChooseAnimationProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}

const animationsName = {
  [AnimationEnum.None]: <FormattedMessage id="450Fty" defaultMessage="None" />,

  [AnimationEnum.Bounce]: (
    <FormattedMessage id="wBSk/Y" defaultMessage="Bounce" />
  ),

  [AnimationEnum.DoubleRing]: (
    <FormattedMessage id="E5tx8T" defaultMessage="DoubleRing" />
  ),

  [AnimationEnum.HeadShake]: (
    <FormattedMessage id="J7cq60" defaultMessage="HeadShake" />
  ),

  [AnimationEnum.Heartbeat]: (
    <FormattedMessage id="Ia8BRm" defaultMessage="Heartbeat" />
  ),

  [AnimationEnum.Jello]: (
    <FormattedMessage id="5yR/cm" defaultMessage="Jello" />
  ),

  [AnimationEnum.MoveIn]: (
    <FormattedMessage id="8Zi7Ne" defaultMessage="MoveIn" />
  ),

  [AnimationEnum.Pulse]: (
    <FormattedMessage id="DTM010" defaultMessage="Pulse" />
  ),

  [AnimationEnum.Ring]: <FormattedMessage id="IHE6Tr" defaultMessage="Ring" />,

  [AnimationEnum.RubberBand]: (
    <FormattedMessage id="mruVwo" defaultMessage="RubberBand" />
  ),

  [AnimationEnum.Tada]: <FormattedMessage id="Qd0mWR" defaultMessage="Tada" />,

  [AnimationEnum.Wobble]: (
    <FormattedMessage id="/pkFjD" defaultMessage="Wobble" />
  ),
};

function ChooseAnimation({ appearance, update }: ChooseAnimationProps) {
  const {
    style: { animation },
  } = appearance;

  return (
    <Box width="100%">
      <CustomText>
        <FormattedMessage defaultMessage="Animation" id="g80VV8" />
      </CustomText>
      <Stack width="100%" mt="6px">
        <Popover
          translateX={50}
          withArrow={false}
          button={
            <Stack
              boxShadow="0px 1px 2px 0px #1018280D"
              cursor="pointer"
              borderRadius="8px"
              align="center"
              justify="between"
              border="1px solid  #D0D5DD"
              spacing="tight"
              height="40px"
              direction="row"
              padding="0 10px"
            >
              <Box>
                <Text color={"black"} fontSize="sm" fontWeight="semiBold">
                  {animation}
                </Text>
              </Box>
              <Box>
                <ChevronDownIcon />
              </Box>
            </Stack>
          }
          content={
            <Stack width="350px" spacing="none" height="400px">
              <Box p="16px" pb="0">
                <Text
                  fontWeight="semiBold"
                  fontSize="sm"
                  style={{ color: "#1D2939" }}
                >
                  <FormattedMessage
                    defaultMessage="Choose animation"
                    id="B8xzj/"
                  />
                </Text>
              </Box>
              <Stack
                className="chooseContainer"
                height="350px"
                overflowY="scroll"
                style={{ gap: "16px", paddingInlineEnd: "8px" }}
                spacing="none"
                padding="16px"
                pb="0px"
                wrap
                direction="row"
              >
                {ANIMATION.map((animationName) => {
                  return (
                    <Stack
                      key={animationName}
                      cursor="pointer"
                      justify="center"
                      align="center"
                      spacing="condensed"
                      style={{ backgroundColor: "#F9FAFB", zIndex: "0" }}
                      border={
                        animation === animationName ? "2px solid #0E9384" : ""
                      }
                      height="98px"
                      width="95px"
                      borderRadius="10px"
                      onClick={() => {
                        update({
                          appearance: {
                            ...appearance,
                            style: {
                              ...appearance.style,
                              animation: animationName,
                            },
                          },
                        });
                      }}
                    >
                      <StyledAnimation
                        animationName={animationName}
                        size="48px"
                        color="#0E9384"
                      >
                        <MessageChatSquareWhite color="white" />
                      </StyledAnimation>
                      <Text
                        fontWeight="medium"
                        style={{
                          color:
                            animation === animationName ? "#1D2939" : "#667085",
                        }}
                      >
                        {animationsName[animationName]}
                      </Text>
                    </Stack>
                  );
                })}
              </Stack>
            </Stack>
          }
        />
      </Stack>
    </Box>
  );
}

export default ChooseAnimation;

/**
 * Styles
 */

const StyledAnimation = styled.div<{
  animationName: AnimationEnum;
  size: string;
  color: string;
}>`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: ${({ size }) => size};
  width: ${({ size }) => size};
  border-radius: 50%;
  background: ${({ color }) => color};
  ${AnimationBasisStyle}
  ${({ animationName }) => AnimationMapper[animationName]}
`;
