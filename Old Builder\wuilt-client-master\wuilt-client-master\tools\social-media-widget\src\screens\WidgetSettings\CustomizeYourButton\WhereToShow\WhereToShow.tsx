import {
  Box,
  Heading,
  RadioButton,
  RadioGroup,
  Stack,
  Text,
  TextArea,
} from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import {
  TWidgetAppearance,
  TWidgetSettings,
  WidgetPagesEnum,
} from "../../../../shared/types";

interface WhereToShowProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function WhereToShow({ appearance, update }: WhereToShowProps) {
  const SelectedPages =
    appearance.display.pages.type === WidgetPagesEnum.SelectedPages;
  return (
    <Stack
      boxShadow="0px 1px 2px 0px #1018280D"
      width="100%"
      border="1px solid #EAECF0"
      borderRadius="10px"
      padding="16px"
      spacing="none"
      minHeight="106px"
    >
      <Stack
        spacing="comfy"
        direction="row"
        align="center"
        desktop={{ direction: "column", align: "start", spacing: "natural" }}
      >
        <Heading color="black" fontSize="sm" fontWeight="semiBold">
          <FormattedMessage defaultMessage="Where to show" id="0IBcya" />
        </Heading>
        <RadioGroup
          defaultValue={WidgetPagesEnum.AllPages}
          onChange={(value: WidgetPagesEnum) => {
            update({
              appearance: {
                ...appearance,
                display: {
                  ...appearance.display,
                  pages: {
                    ...appearance.display.pages,
                    type: value,
                    displayOn: [],
                    hideOn: [],
                  },
                },
              },
            });
          }}
          alignment="horizontal"
        >
          <RadioButton
            value={WidgetPagesEnum.AllPages}
            label={
              <Text
                style={{ color: "#1D2939" }}
                fontSize="sm"
                fontWeight="medium"
              >
                <FormattedMessage defaultMessage="All Pages" id="0D7341" />
              </Text>
            }
          />
          <RadioButton
            value={WidgetPagesEnum.SelectedPages}
            label={
              <Text
                style={{ color: "#1D2939" }}
                fontSize="sm"
                fontWeight="medium"
              >
                <FormattedMessage defaultMessage="Select pages" id="HG+4RY" />
              </Text>
            }
          />
        </RadioGroup>
      </Stack>
      {SelectedPages ? (
        <>
          <Box mt="16px">
            <Box mb="6px">
              <Text fontSize="sm" fontWeight="medium">
                <FormattedMessage defaultMessage="Show on" id="tdx9r4" />
              </Text>
            </Box>
            <TextArea
              className="text-area"
              defaultValue={appearance.display.pages.displayOn.join("\n")}
              onBlur={(e) => {
                const selectedPages = e.target.value.split("\n");

                update({
                  appearance: {
                    ...appearance,
                    display: {
                      ...appearance.display,
                      pages: {
                        ...appearance.display.pages,
                        displayOn: selectedPages,
                      },
                    },
                  },
                });
              }}
              rows={4}
            ></TextArea>
          </Box>
        </>
      ) : (
        <Box mt="16px">
          <Box mb="6px">
            <Text fontSize="sm" fontWeight="medium">
              <FormattedMessage defaultMessage="Don't Show on" id="YvrKIn" />
            </Text>
          </Box>
          <TextArea
            className="text-area"
            defaultValue={appearance.display.pages.hideOn.join("\n")}
            onBlur={(e) => {
              const selectedPages = e.target.value.split("\n");

              update({
                appearance: {
                  ...appearance,
                  display: {
                    ...appearance.display,
                    pages: {
                      ...appearance.display.pages,
                      hideOn: selectedPages,
                    },
                  },
                },
              });
            }}
            rows={4}
          ></TextArea>
        </Box>
      )}
      <Stack mt="6px" spacing="tight">
        <Text>
          <FormattedMessage
            defaultMessage="Write each url in a separate line."
            id="j9mLn2"
          />
        </Text>
        <Text>
          <FormattedMessage
            defaultMessage="Ex: https://wuilt.com/pricing/"
            id="i/EEyC"
          />
        </Text>
      </Stack>
    </Stack>
  );
}

export default WhereToShow;
