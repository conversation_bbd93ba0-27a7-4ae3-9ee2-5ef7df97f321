import React, { cloneElement, ReactElement } from "react";
import { Transition, TransitionGroup } from "react-transition-group";
import { useTheme } from "styled-components";
import { Global } from "../../common/types";

type TransitionStylesType = {
  entering: React.CSSProperties;
  entered: React.CSSProperties;
  exiting: React.CSSProperties;
  exited: React.CSSProperties;
};

const defaultElementStyles = (duration: number) => ({
  transition: `all ${duration}ms`,
});

const defaultTransitionStyles = (isRtl: boolean) => ({
  entering: {
    transform: `translateX(${isRtl ? "5rem" : "-5rem"})`,
    opacity: 0,
  },
  entered: { transform: `translateX(0)`, opacity: 1 },
  exiting: { transform: `translateX(${isRtl ? "-5rem" : "5rem"})`, opacity: 0 },
  exited: { transform: `translateX(${isRtl ? "-5rem" : "5rem"})`, opacity: 0 },
});

export interface AnimatedListProps extends Global {
  children?: ReactElement[];
  ids: (string | number | undefined)[];
  transitionDuration?: number;
  customElementStyles?: React.CSSProperties;
  customTransitionStyles?: TransitionStylesType;
  className?: string;
}

const AnimatedList: React.FC<AnimatedListProps> = ({
  children,
  ids,
  transitionDuration = 200,
  customElementStyles,
  customTransitionStyles,
  className,
  dataTest,
}) => {
  const { rtl: isRtl } = useTheme();
  if (!children) return null;
  return (
    <TransitionGroup className={className} data-test={dataTest}>
      {children.map((Child: ReactElement, index) => (
        <Transition key={ids[index]} timeout={500}>
          {(transitionStatus) =>
            cloneElement(Child, {
              style: {
                ...defaultElementStyles(transitionDuration),
                ...customElementStyles,
                ...(customTransitionStyles || defaultTransitionStyles(isRtl))[
                  transitionStatus
                ],
              },
            })
          }
        </Transition>
      ))}
    </TransitionGroup>
  );
};

AnimatedList.displayName = "AnimatedList";

export { AnimatedList };
