import React, { useEffect, useRef, useState } from "react";
import { FixedSizeList, Layout, ListOnScrollProps } from "react-window";
import { SearchIcon } from "../icons";
import { InputField } from "../InputField";
import { Box, BoxProps } from "../Box";

export type VirtualizedChildrenProps<Type> = {
  style: React.CSSProperties;
  index: number;
  data: Type[];
};

export interface VirtualizedListProps<Type = any> {
  items: Type[];
  placeholder?: string;
  itemsTotalCount?: number;
  itemHeight?: number;
  listHeight?: number;
  listWidth?: number;
  isLoading?: boolean;
  hideSearchBar?: boolean;
  layout?: Layout;
  boxStyle?: BoxProps;
  searchKey?: string;
  onFetchMore?: () => void;
  onFilter?: (searchQuery: any) => void;
  children: (args: VirtualizedChildrenProps<Type>) => React.ReactElement;
}

function VirtualizedList<Type>({
  children,
  items,
  placeholder,
  itemsTotalCount,
  itemHeight = 65,
  listHeight = 300,
  listWidth = 300,
  isLoading,
  hideSearchBar,
  layout = "vertical",
  boxStyle,
  searchKey = "title",
  onFetchMore,
  onFilter,
  ...listProps
}: VirtualizedListProps<Type>) {
  const [filteredItems, setFilteredItems] = useState<Type[]>(items);
  const previousItemsLength = useRef(0);

  let threshold: number = (itemHeight * items.length - listHeight) * 0.75;

  useEffect(() => {
    setFilteredItems(items);
  }, [items]);

  const handleFilter = (value: string) => {
    if (onFilter) {
      onFilter(value);
    } else {
      const tempItems = items.filter((elem) => {
        const compareTo = typeof elem === "string" ? elem : elem?.[searchKey];
        return compareTo?.toLowerCase()?.includes(value);
      });
      setFilteredItems(tempItems);
      if (onFetchMore) {
        threshold = (itemHeight * tempItems.length - listHeight) * 0.75;
      }
    }
  };

  const handleScroll = (state: ListOnScrollProps) => {
    if (
      onFetchMore &&
      itemsTotalCount &&
      state.scrollOffset > threshold &&
      filteredItems.length < itemsTotalCount &&
      previousItemsLength.current !== items.length
    ) {
      previousItemsLength.current = items.length;
      onFetchMore();
    }
  };

  return (
    <Box width="100%">
      {!hideSearchBar && (
        <InputField
          type="text"
          debounce={200}
          onChange={({ target: { value } }) =>
            handleFilter(value?.toLocaleLowerCase())
          }
          prefix={<SearchIcon color="secondary" />}
          placeholder={placeholder}
          loading={isLoading}
        />
      )}

      <Box
        m="5px 0"
        boxShadow="xxs"
        overflow="auto"
        borderRadius="4px"
        {...boxStyle}
      >
        <FixedSizeList
          itemData={filteredItems}
          itemCount={filteredItems.length}
          layout={layout}
          itemSize={itemHeight}
          height={layout === "vertical" ? listHeight : "100%"}
          width={layout === "vertical" ? "100%" : listWidth}
          useIsScrolling
          onScroll={handleScroll}
          {...listProps}
        >
          {children}
        </FixedSizeList>
      </Box>
    </Box>
  );
}

export { VirtualizedList };
