import React, { ReactElement } from "react";
import styled, { css } from "styled-components";
import { mediaQueries } from "../../utils";
import { GeneralInputWrapper } from "../InputField/InputField";

export interface InputGroupProps {
  children: ReactElement[];
  ElementsFlexGrow?: number[];
  direction?: "row" | "column";
  ignoreOnMobile?: boolean;
  borderRadius?: string;
}

const InputGroup: React.FC<InputGroupProps> = ({
  children,
  ElementsFlexGrow,
  direction = "row",
  ignoreOnMobile,
  borderRadius = "4px",
}) => {
  const fieldsCount: number = children?.length || 1;

  return (
    <StyledWrapper
      direction={direction}
      ignoreOnMobile={ignoreOnMobile || false}
    >
      {children?.map((child: React.ReactNode, index: number) => (
        <StyledInput
          key={index}
          index={index}
          fieldsCount={fieldsCount}
          borderRadius={borderRadius}
          direction={direction}
          flex={ElementsFlexGrow ? ElementsFlexGrow[index] : 1}
          ignoreOnMobile={ignoreOnMobile || false}
        >
          {child}
        </StyledInput>
      ))}
    </StyledWrapper>
  );
};

InputGroup.displayName = "InputGroup";

export { InputGroup };

/**
 *
 *
 * Styles
 *
 *
 */

const StyledWrapper = styled.div<{
  direction: string;
  ignoreOnMobile: boolean;
}>`
  display: flex;
  flex-direction: ${({ direction }) => direction};
  ${mediaQueries.largeMobile(css<{ ignoreOnMobile: boolean }>`
    ${({ ignoreOnMobile }) => ignoreOnMobile && "flex-direction: column;"}
  `)}
`;

const RowStyles = ({ index, fieldsCount, theme, borderRadius }) =>
  index === 0
    ? css`
        ${theme.rtl ? "border-left: none" : "border-right: none"};
        ${theme.rtl
          ? `border-radius: 0 ${borderRadius} ${borderRadius} 0`
          : `border-radius: ${borderRadius} 0 0 ${borderRadius}`};
      `
    : index === fieldsCount - 1
    ? css`
        ${theme.rtl
          ? `border-radius: ${borderRadius} 0 0 ${borderRadius}`
          : `border-radius: 0 ${borderRadius} ${borderRadius} 0`};
      `
    : css`
        ${theme.rtl ? "border-right: 1px " : "border-left: 1px "};
        border-color: ${theme.palette.ink.lighter};
        border-style: solid;
        ${theme.rtl ? "border-left: none" : "border-right: none"};
      `;

const ColumnStyles = ({ index, fieldsCount, theme }) =>
  index === 0
    ? css`
        border-bottom: none;
        border-radius: 4px 4px 0 0;
      `
    : index === fieldsCount - 1
    ? css`
        border-top: none;
        border-radius: 0 0 4px 4px;
      `
    : css`
        border-width: 1px;
        border-style: solid;
        border-color: ${theme.palette.ink.lighter};
      `;

const IgnoreStyles = css`
  border: 1px solid ${({ theme }) => theme.palette.ink.lighter};
  border-radius: 4px;
`;

const StyledInput = styled.div<{
  index: number;
  fieldsCount: number;
  flex: number;
  direction: string;
  ignoreOnMobile: boolean;
  borderRadius: string;
}>`
  flex: ${({ flex }) => flex};

  ${GeneralInputWrapper} {
    border-radius: 0;
    ${({ direction }) => (direction === "column" ? ColumnStyles : RowStyles)}
    ${mediaQueries.largeMobile(css<{ ignoreOnMobile: boolean }>`
      ${({ ignoreOnMobile }) => ignoreOnMobile && IgnoreStyles}
    `)}
  }
`;
