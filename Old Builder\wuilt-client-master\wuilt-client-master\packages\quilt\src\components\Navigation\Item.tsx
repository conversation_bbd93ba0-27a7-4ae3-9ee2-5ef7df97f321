// @ts-nocheck
import React from "react";
import styled, { css, useTheme } from "styled-components";
import { ButtonPrimitiveProps } from "components/Button/primitive/ButtonPrimitive.types";
import { ButtonPrimitive } from "../Button/primitive";
import getIconContainer from "../Button/helpers/getIconContainer";
import getCommonProps from "../Button/helpers/getCommonProps";
import { color, borderRadius, left } from "../../utils";
import { Accordion } from "../Accordion";
import { Box } from "../Box";
import { ArrowDownFilledIcon, ArrowUpFilledIcon } from "../icons";
import * as Common from "../../common/types";

export interface ItemProps extends ButtonPrimitiveProps {
  url?: string;
  label?: React.ReactNode;
  icon?: React.ReactNode;
  badge?: React.ReactNode;
  isActive?: boolean;
  asComponent?: Common.Component;
}

const Item: React.FC<ItemProps> = (props) => {
  const { icon, label, isActive, badge, children, ...restProps } = props;
  const theme = useTheme();
  const propsWithTheme = { theme, ...props };
  const commonProps = getCommonProps({
    prefixIcon: icon,
    width: "100%",
    children: label,
    theme,
    suffixIcon: badge,
    contentAlign: props.contentAlign,
  });
  const styleActive = {
    background: theme.palette.product.lightHover,
    backgroundHover: theme.palette.product.lightHover,
    backgroundActive: theme.palette.product.lightHover,
    backgroundFocus: theme.palette.product.lightHover,
    foreground: theme.palette.product.normal,
    foregroundHover: theme.palette.product.normal,
    foregroundActive: theme.palette.product.normal,
    foregroundFocus: theme.palette.product.normal,
  };
  const styleNormal = {
    background: "transparent",
    backgroundHover: theme.palette.cloud.dark,
    backgroundActive: theme.palette.cloud.dark,
    backgroundFocus: theme.palette.cloud.dark,
    foreground: theme.palette.ink.normal,
    foregroundHover: theme.palette.ink.normal,
    foregroundActive: theme.palette.ink.normal,
    foregroundFocus: theme.palette.ink.normal,
  };
  const styles = isActive ? styleActive : styleNormal;
  const icons = getIconContainer({
    ...propsWithTheme,
    iconForeground: {
      foreground: isActive ? styles.foreground : theme.palette.ink.light,
      foregroundHover: styles.foregroundHover,
      foregroundActive: styles.foregroundActive,
      foregroundFocus: styles.foregroundFocus,
    },
  } as any);

  const isOneChildrenActive = children?.length
    ? !!children?.find((child) => child?.props?.isActive)
    : !!children?.props?.isActive;

  const isOpen = isActive || isOneChildrenActive;
  const arrowIcon = isOpen ? (
    <ArrowUpFilledIcon size="xxs" />
  ) : (
    <ArrowDownFilledIcon size="xxs" />
  );
  const suffixIcon = children ? arrowIcon : badge;

  const parentItem = (
    <Box position="relative" p="0 22px" width="100%">
      <StyledSelected isActive={isActive} />
      <ButtonPrimitive
        squared
        prefixIcon={icon}
        suffixIcon={suffixIcon}
        {...restProps}
        {...styles}
        {...commonProps}
        {...icons}
        padding="0 10px"
        fontWeight={600}
      >
        {label}
      </ButtonPrimitive>
    </Box>
  );

  const childItems = children && (
    <Accordion hasCustomStyle value={isOpen}>
      <Accordion.Header p="0" hideIcons>
        {parentItem}
      </Accordion.Header>
      <Accordion.Body p="0">{children}</Accordion.Body>
    </Accordion>
  );

  return childItems || parentItem;
};

Item.displayName = "NavigationItem";

export { Item };

/**
 *
 * Styles
 *
 */

const StyledSelected = styled.div<{ isActive: boolean }>`
  position: absolute;
  top: 0;
  ${left} :0;
  z-index: 1;
  width: 6px;
  height: 100%;
  background: ${color("product")};
  border-radius: ${borderRadius("0 6px 6px 0")};
  opacity: 0;
  ${({ isActive }) =>
    isActive &&
    css`
      opacity: 1;
    `}
`;
