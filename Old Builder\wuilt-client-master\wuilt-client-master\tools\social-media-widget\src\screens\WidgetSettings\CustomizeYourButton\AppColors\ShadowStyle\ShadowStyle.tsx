import {
  Box,
  InputField,
  InputGroup,
  Stack,
  Text,
  ToggleButton,
} from "@wuilt/quilt";
import React, { useState } from "react";
import { FormattedMessage } from "react-intl";
import ColorInput from "../../../ChooseMessaging/ColorInput";
import {
  TWidgetAppearance,
  TWidgetSettings,
} from "../../../../../shared/types";

interface ShadowStyleProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function ShadowStyle({ appearance, update }: ShadowStyleProps) {
  const [toggleShadow, setToggleShadow] = useState(true);
  const {
    style: { shadow },
  } = appearance;

  return (
    <Box mt="16px">
      <Stack
        justify="between"
        align="center"
        minHeight="44px"
        direction="row"
        desktop={{ direction: "column", align: "start" }}
      >
        <Box width="224px">
          <ToggleButton
            activeColor="#0E9384"
            disableColor="#F2F4F7"
            sliderBoxShadow="0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A"
            label={
              <Text
                fontSize="sm"
                fontWeight="medium"
                style={{ color: "#1D2939" }}
              >
                <FormattedMessage defaultMessage="Shadow" id="u4jNiO" />
              </Text>
            }
            hideIcons
            value={toggleShadow}
            onChange={(value) => {
              setToggleShadow(value);
              if (!value) {
                update({
                  appearance: {
                    ...appearance,
                    style: {
                      ...appearance.style,
                      shadow: {
                        color: "#FFFFF",
                        opacity: 0,
                      },
                    },
                  },
                });
              }
            }}
          />
        </Box>

        {toggleShadow && (
          <Stack boxShadow="0px 1px 2px 0px #1018280D" spacing="0" width="100%">
            <InputGroup borderRadius="8px" ElementsFlexGrow={[1, 0]}>
              <ColorInput
                value={shadow?.color || "transparent"}
                customStyle={{
                  borderStartEndRadius: "0",
                  borderEndEndRadius: "0",
                  borderInlineEnd: "none",
                }}
                onChange={(color) => {
                  setToggleShadow(true);
                  update({
                    appearance: {
                      ...appearance,
                      style: {
                        ...appearance.style,
                        shadow: { ...shadow!, color: color },
                      },
                    },
                  });
                }}
              />
              <InputField
                type="number"
                width="100px"
                minWidth="30px"
                suffix={<Text fontSize="medium" children="%" />}
                value={shadow?.opacity || 0}
                onChange={(value) => {
                  setToggleShadow(true);
                  update({
                    appearance: {
                      ...appearance,
                      style: {
                        ...appearance.style,
                        shadow: { ...shadow!, opacity: +value },
                      },
                    },
                  });
                }}
              />
            </InputGroup>
          </Stack>
        )}
      </Stack>
    </Box>
  );
}

export default ShadowStyle;
