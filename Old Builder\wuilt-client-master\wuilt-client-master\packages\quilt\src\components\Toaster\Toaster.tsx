import React, { useEffect } from "react";
import styled, { useTheme } from "styled-components";
import { Fill, Slot } from "../SlotFill";
import { rtl } from "../../utils";
import { Config, Toast } from "./types";
import { AnimatedList } from "../AnimatedList";
import ToastItem from "./ToastItem";

const SLOT_NAME = "toaster";

export interface ToasterProps {
  items: Toast[];
  defaultConfig?: Config;
  className?: string;
  style?: React.CSSProperties;
  setDefaultConfig: (defaultConfig: Config) => void;
  removeToast: (id: string) => void;
}

const Toaster: React.FC<ToasterProps> = ({
  items,
  defaultConfig,
  className,
  style,
  setDefaultConfig,
  removeToast,
}) => {
  // @ts-ignore
  const { rtl: isRtl } = useTheme();

  useEffect(() => {
    if (defaultConfig) {
      setDefaultConfig(defaultConfig);
    } else {
      setDefaultConfig({
        appearance: "info",
        autoDismissTimeout: 4000,
        autoDismiss: true,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultConfig]);

  return (
    <Fill name={SLOT_NAME}>
      <StyledContainer className={className} style={style}>
        <AnimatedList
          ids={items.map((item) => item.id)}
          customTransitionStyles={TransitionStyles(isRtl)}
        >
          {items.map((toast) => (
            <div key={toast.id}>
              <ToastItem toast={toast} removeToast={removeToast} />
            </div>
          ))}
        </AnimatedList>
      </StyledContainer>
    </Fill>
  );
};

Toaster.displayName = "Toaster";
export { Toaster };

export const ToasterSlot = ({ name = SLOT_NAME }) => {
  return <Slot name={name} />;
};

/**
 *
 * Styles
 *
 */

const TransitionStyles = (isRtl: boolean) => ({
  entering: {
    transform: `translateX(${isRtl ? "-5rem" : "5rem"})`,
    opacity: 0,
  },
  entered: { transform: "translateX(0)", opacity: 1 },
  exiting: {
    transform: `translateX(${isRtl ? "-5rem" : "5rem"})`,
    opacity: 0,
  },
  exited: {
    transform: `translateX(${isRtl ? "-5rem" : "5rem"})`,
    opacity: 0,
  },
});

const StyledContainer = styled.div`
  position: fixed;
  top: 74px;
  ${rtl("left: 10px", "right: 10px")};
  z-index: 1020; // above modals
`;
