export const boundingClientRect = (ref) => {
  // TODO: revert iframe portals
  // const window = ref.current.ownerDocument.defaultView;
  if (
    ref &&
    ref.current &&
    typeof ref.current.getBoundingClientRect === "function" &&
    typeof window !== "undefined"
  ) {
    const { height, width, top, left, right, bottom } =
      ref.current.getBoundingClientRect();
    return {
      top: top + (window.scrollY || window.pageYOffset),
      right: right + (window.scrollX || window.pageXOffset),
      bottom: bottom + (window.scrollY || window.pageYOffset),
      left: left + (window.scrollX || window.pageXOffset),
      pureTop: top,
      pureLeft: left,
      pureRight: right,
      pureBottom: bottom,
      height,
      width,
    };
  }
  return null;
};
