import React from "react";
import {
  TWidgetAppearance,
  TWidgetSettings,
} from "../../../../../shared/types";
import { Box, InputField, Stack } from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import CustomText from "../../../ui/CustomText";
interface ButtonWithTextProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function ButtonWithText({ appearance, update }: ButtonWithTextProps) {
  const { content } = appearance;
  return (
    <Box mt="16px">
      <Stack direction="row">
        <Box width="100%">
          <CustomText>
            <FormattedMessage defaultMessage="Button text" id="BlGveL" />
          </CustomText>
          <Stack
            boxShadow="0px 1px 2px 0px #1018280D"
            borderRadius="4px"
            mt="6px"
          >
            <InputField
              borderRadius="8px"
              height="40px"
              value={content.openText}
              onBlur={(e: any) => {
                update({
                  appearance: {
                    ...appearance,
                    content: { ...content, openText: e.target.value },
                  },
                });
              }}
            />
          </Stack>
        </Box>
        <Box width="100%">
          <CustomText>
            <FormattedMessage defaultMessage="Close text" id="RcGPHa" />
          </CustomText>
          <Stack
            boxShadow="0px 1px 2px 0px #1018280D"
            borderRadius="4px"
            mt="6px"
          >
            <InputField
              borderRadius="8px"
              height="40px"
              value={content.closeText}
              onBlur={(e: any) => {
                update({
                  appearance: {
                    ...appearance,
                    content: { ...content, closeText: e.target.value },
                  },
                });
              }}
            />
          </Stack>
        </Box>
      </Stack>
    </Box>
  );
}

export default ButtonWithText;
