import { TIconType } from "../types";

export function MessageCircle({ size = 40, width, height, color }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.5 10C17.5 14.1421 14.1421 17.5 10 17.5C9.00238 17.5 8.05025 17.3052 7.17958 16.9516C7.01294 16.8839 6.92962 16.8501 6.86227 16.835C6.79638 16.8202 6.74763 16.8148 6.68011 16.8148C6.61109 16.8148 6.53591 16.8273 6.38554 16.8524L3.42063 17.3466C3.11015 17.3983 2.95491 17.4242 2.84265 17.376C2.7444 17.3339 2.66611 17.2556 2.62397 17.1573C2.57582 17.0451 2.60169 16.8898 2.65344 16.5794L3.14759 13.6145C3.17265 13.4641 3.18518 13.3889 3.18517 13.3199C3.18516 13.2524 3.17976 13.2036 3.165 13.1377C3.1499 13.0704 3.11606 12.9871 3.04839 12.8204C2.69478 11.9497 2.5 10.9976 2.5 10C2.5 5.85786 5.85786 2.5 10 2.5C14.1421 2.5 17.5 5.85786 17.5 10Z"
        stroke={color || "currentColor"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
