import React from "react";
import { ComponentMeta, ComponentStory } from "@storybook/react";
import { ImageCropper, ImageCropperValue } from "./ImageCropper";
import { Field, Form, FormSubmit } from "../Form";

// https://storybook.js.org/docs/react/essentials/controls
export default {
  title: "Components/ImageCropper",
  component: ImageCropper,

  argTypes: {},
} as ComponentMeta<typeof ImageCropper>;

const Template: ComponentStory<typeof ImageCropper> = ({ ...restProps }) => {
  const onSubmit = (values) => {
    console.log(values.image);
  };
  return (
    <Form onSubmit={onSubmit}>
      {({ formProps }) => (
        <form {...formProps}>
          <Field<ImageCropperValue>
            label="Image"
            name="image"
            defaultValue={{
              originalSrc:
                "https://images.unsplash.com/photo-1525609004556-c46c7d6cf023?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NHx8Y2Fyc3xlbnwwfHwwfHw%3D&w=1000&q=80",
            }}
          >
            {({ fieldProps }) => (
              <ImageCropper {...restProps} {...fieldProps} />
            )}
          </Field>
          <FormSubmit>Submit</FormSubmit>
        </form>
      )}
    </Form>
  );
};

export const ImageCropperStory = Template.bind({});
ImageCropperStory.args = {};
