import { TIconType } from "../types";

export function Custom({ size = 40, width, height, color }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.7076 25.8639L19.2933 27.2781C17.3407 29.2308 14.1749 29.2308 12.2223 27.2781C10.2697 25.3255 10.2697 22.1597 12.2223 20.2071L13.6365 18.7929M26.3644 20.2071L27.7786 18.7929C29.7312 16.8402 29.7312 13.6744 27.7786 11.7218C25.826 9.76917 22.6602 9.76917 20.7076 11.7218L19.2933 13.136M16.5005 22.9999L23.5005 15.9999"
        stroke={color || "currentColor"}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
