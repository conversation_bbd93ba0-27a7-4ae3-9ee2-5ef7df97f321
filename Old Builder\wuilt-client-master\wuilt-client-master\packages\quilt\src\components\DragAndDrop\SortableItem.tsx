import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import React from "react";
import { BoxProps } from "../Box";
import { StyledSortableItem } from "./styles";

export interface SortableItemChildrenArgs<Type> {
  item: Type;
  index: number;
  isDragging: boolean;
  isSorting: boolean;
}

export interface SortableItemComponentProps<Type> {
  id: string;
  index: number;
  item: Type;
  children:
    | ((args: SortableItemChildrenArgs<Type>) => React.ReactNode)
    | React.ReactNode;
  useHandleOnly?: boolean;
  disabled?: boolean;
  boxProps?: BoxProps;
  className?: string;
  style?: React.CSSProperties;
}

export function SortableItem<Type>({
  id,
  index,
  item,
  children,
  disabled,
  useHandleOnly,
  className,
  style: styleProp,
}: SortableItemComponentProps<Type>) {
  const {
    setNodeRef,
    transform,
    transition,
    isDragging,
    isSorting,
    attributes,
    listeners,
  } = useSortable({
    id,
    disabled,
  });
  const style = {
    ...styleProp,
    transition,
    transform: CSS.Transform.toString(transform),
  };

  const dragProps = useHandleOnly ? {} : { ...attributes, ...listeners };

  return (
    <StyledSortableItem
      ref={setNodeRef}
      isDragging={isDragging}
      isSorting={isSorting}
      style={style}
      className={className}
      {...dragProps}
    >
      {typeof children === "function"
        ? children({ item, index, isDragging, isSorting })
        : children}
    </StyledSortableItem>
  );
}
