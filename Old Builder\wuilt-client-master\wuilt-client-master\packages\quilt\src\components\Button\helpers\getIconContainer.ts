import { SIZE_OPTIONS } from "../consts";
import { rtlSpacing } from "../../../utils/rtl";
import { base } from "../../../themes/original";

const iconMarginOptions = {
  small: base.space.xxs,
  normal: base.space.xs,
  large: base.space.xs,
};

const getIconSpacing = (onlyIcon, size, theme) => {
  if (onlyIcon) {
    return null;
  }
  return {
    leftMargin: rtlSpacing(`0 ${iconMarginOptions[size]} 0 0`)({
      theme,
    }),
    rightMargin: rtlSpacing(`0 0 0 ${iconMarginOptions[size]}`)({
      theme,
    }),
  };
};

const getIconContainer = ({
  prefixIcon,
  children,
  theme,
  size = SIZE_OPTIONS.NORMAL,
  iconForeground,
}) => {
  const onlyIcon = Boolean(prefixIcon && !children);
  return {
    icons: {
      ...getIconSpacing(onlyIcon, size, theme),
      ...iconForeground,
    },
  };
};

export default getIconContainer;
