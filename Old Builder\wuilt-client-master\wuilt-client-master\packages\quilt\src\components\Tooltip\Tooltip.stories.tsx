import React from "react";
import { ComponentStory, ComponentMeta } from "@storybook/react";
import { Tooltip } from "./Tooltip";
import { Button } from "../Button";
import { Box } from "../Box";

// https://storybook.js.org/docs/react/essentials/controls
export default {
  title: "Components/Tooltip",
  component: Tooltip,
  argTypes: {
    className: { control: false },
  },
} as ComponentMeta<typeof Tooltip>;

const Template: ComponentStory<typeof Tooltip> = (props) => {
  return (
    <Box p="50px">
      <Tooltip {...props}>
        <Button>Hover Me</Button>
      </Tooltip>
    </Box>
  );
};

export const Playground = Template.bind({});
Playground.args = {
  content: "Tooltip Content",
};
