import React, { useState } from "react";
import { ComponentStory, ComponentMeta } from "@storybook/react";
import styled from "styled-components";
import { Popup } from "./Popup";
import { Box } from "../Box";
import { Heading } from "../Heading";
import { Text } from "../Text";
import { Button } from "../Button";
import { DropMenu } from "../DropMenu";
import { Tooltip } from "../Tooltip";
import { Divider } from "../Divider";
import { Stack } from "../Stack";
import { ChevronLeftIcon } from "../icons";

// https://storybook.js.org/docs/react/essentials/controls
export default {
  title: "Components/Popup",
  component: Popup,

  argTypes: {},
} as ComponentMeta<typeof Popup>;

const Template: ComponentStory<typeof Popup> = ({ ...restProps }) => {
  const [open, setOpen] = useState(false);

  return (
    <>
      <h4>Normal Usage</h4>
      <Box>
        <Popup
          {...restProps}
          activator={<Button onClick={() => {}}>Click me</Button>}
          onPopupOpen={() => {}}
          onPopupClose={() => {}}
        >
          <Popup.Header>
            <Heading color="white">Section Settings</Heading>
          </Popup.Header>
          <Popup.Body>
            <Box p="50px">
              <Text>Body</Text>
            </Box>
          </Popup.Body>
        </Popup>
      </Box>

      <br />
      <Divider />

      <h4>Controllable header and close button</h4>
      <Box>
        <Popup
          {...restProps}
          activator={<Button onClick={() => {}}>Click me</Button>}
        >
          <Popup.Header
            width="400px"
            closeButtonControls={{ alignSelf: "start" }}
            headerContentControls={{ alignItems: "end" }}
          >
            <Button
              plain
              compact
              transparent
              color="white"
              prefixIcon={<ChevronLeftIcon reverseOnRtl />}
            >
              Back
            </Button>
            <Stack>
              <Heading color="white" margin="0px 20px">
                Section Settings
              </Heading>
              <Heading color="white" margin="0px 20px">
                Section Settings
              </Heading>
            </Stack>
            <Button compact transparent plain color="white">
              Edit
            </Button>
          </Popup.Header>
          <Popup.Body>
            <Box p="50px">
              <Text>Body</Text>
            </Box>
          </Popup.Body>
        </Popup>
      </Box>

      <br />
      <Divider />

      <h4>Preserve location</h4>
      <Box>
        <Popup
          {...restProps}
          preserveLocation
          activator={<Button>Click me</Button>}
        >
          <Popup.Header>
            <Heading color="white">
              When moving me, I will save my location
            </Heading>
          </Popup.Header>
          <Popup.Body>
            <Box p="50px">
              <Text>close me and open again</Text>
            </Box>
          </Popup.Body>
        </Popup>
      </Box>

      <br />
      <Divider />

      <h4>Open below the element</h4>
      <Box>
        <Popup
          {...restProps}
          referToElement
          activator={<Button>Click me</Button>}
        >
          <Popup.Header>
            <Heading color="white">just below the element</Heading>
          </Popup.Header>
          <Popup.Body>
            <Box p="50px">
              <Text>You can control my placement and other props too</Text>
            </Box>
          </Popup.Body>
        </Popup>
      </Box>

      <br />
      <Divider />

      <h4>Inside Dropmenu - not working</h4>
      <DropMenu>
        <Popup {...restProps} activator={<Button>Click me</Button>}>
          <Popup.Header>
            <Heading color="white">Section Settings</Heading>
          </Popup.Header>
          <Popup.Body>
            <Box p="50px">
              <Text>Body</Text>
            </Box>
          </Popup.Body>
        </Popup>
      </DropMenu>

      <br />
      <Divider />

      <h4>Inside Hovered Box</h4>
      <StyledHoverBox>
        <StyledDropMenu>
          <Popup {...restProps} activator={<Button>Click me</Button>}>
            <Popup.Header>
              <Heading color="white">Section Settings</Heading>
            </Popup.Header>
            <Popup.Body>
              <Box p="50px">
                <Text>Body</Text>
              </Box>
            </Popup.Body>
          </Popup>
        </StyledDropMenu>
        HOVER ME
      </StyledHoverBox>

      <br />
      <Divider />

      <h4>using close callback function</h4>
      <Box>
        <Popup {...restProps} activator={<Button>Click me</Button>}>
          {({ closePopup }) => (
            <>
              <Popup.Header>
                <Heading color="white">Section Settings</Heading>
              </Popup.Header>
              <Popup.Body>
                <Box p="50px">
                  <Tooltip content="Close">
                    <Button onClick={closePopup}>Click to close</Button>
                  </Tooltip>
                </Box>
              </Popup.Body>
            </>
          )}
        </Popup>
      </Box>

      <br />
      <Divider />

      <h4>controllable from parent component</h4>
      <Box position="relative">
        <div
          onClick={() => setOpen(true)}
          style={{
            position: "absolute",
            inset: 0,
            zIndex: 1,
            cursor: "pointer",
          }}
        />

        <Popup
          {...restProps}
          open={open}
          setOpen={setOpen}
          activator={<Button>Click me</Button>}
        >
          <Popup.Header>
            <Heading color="white">Section Settings</Heading>
          </Popup.Header>
          <Popup.Body>
            <Box p="50px">
              <Tooltip content="Close">
                <Button onClick={() => setOpen(false)}>Click to close</Button>
              </Tooltip>
            </Box>
          </Popup.Body>
        </Popup>
      </Box>
    </>
  );
};

export const PopupStory = Template.bind({});
PopupStory.args = {};

/**
 * Styled
 */

const StyledDropMenu = styled.div`
  position: absolute;
  right: 10px;
  top: -20px;
  display: flex;
  z-index: 5;
  height: 50px;
`;

const StyledHoverBox = styled.div`
  position: relative;
  width: 100px;
  height: 100px;
  font-size: 20px;
  border: solid 4px black;

  ${StyledDropMenu} {
    display: none;
  }

  &:hover {
    border: solid 4px green;
    ${StyledDropMenu} {
      display: flex;
    }
  }
`;
