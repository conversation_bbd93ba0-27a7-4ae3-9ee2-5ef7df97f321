import { ThemeType as GlobalThemeType } from "../themes";

type ThemeType = { theme: GlobalThemeType };

type LeftToRight = <T1, T2>(
  left: T1,
  right: T2
) => (theme: ThemeType) => T1 | T2;
const leftToRight: LeftToRight =
  (left, right) =>
  ({ theme }) =>
    theme.rtl ? right : left;

type RtlSpacing = (value: string) => (theme: ThemeType) => string;
export const rtlSpacing: RtlSpacing =
  (value) =>
  ({ theme }) => {
    if (!theme.rtl) {
      return value;
    }
    const parts = value
      .split(" ")
      .filter((part) => !Number.isNaN(parseFloat(part)) && part);
    return parts.length === 4
      ? [parts[0], parts[3], parts[2], parts[1]].join(" ")
      : value;
  };

export const left = leftToRight("left", "right");

export const right = leftToRight("right", "left");

export function rtl(rtlValue: string, value: string) {
  return ({ theme: { rtl } }) => (rtl ? rtlValue : value);
}

type BorderRadius = (value: string) => (theme: ThemeType) => string;
export const borderRadius: BorderRadius =
  (value) =>
  ({ theme }) => {
    if (!theme.rtl) {
      return value;
    }
    const parts = value
      .split(" ")
      .filter((part) => !Number.isNaN(parseFloat(part)) && part);

    return parts.length === 4
      ? [parts[1], parts[0], parts[3], parts[2]].join(" ")
      : value;
  };

type TextAlign = (
  value: "left" | "right"
) => (theme: ThemeType) => string | LeftToRight;
export const textAlign: TextAlign =
  (value) =>
  ({ theme }) => {
    if (theme.rtl) {
      if (value === "left") {
        return leftToRight("left", "right")({ theme });
      }
      if (value === "right") {
        return leftToRight("right", "left")({ theme });
      }
    }
    return value;
  };

type Translate3d = (value: string) => (themeProps: ThemeType) => string;
export const translate3d: Translate3d =
  (value) =>
  ({ theme }) => {
    if (!theme.rtl) {
      return `translate3d(${value})`;
    }
    const parts = value
      .split(",")
      .filter((part) => !Number.isNaN(parseFloat(part)) && part);
    const x = parts[0];
    const newX = x[0] === "-" ? x.slice(1) : `-${x}`;
    return `translate3d(${newX},${parts[1]},${parts[2]})`;
  };

export const marginRight =
  (value) =>
  ({ theme }) => {
    if (theme.rtl) {
      return `margin-left: ${value}`;
    }
    return `margin-right: ${value}`;
  };

export const marginLeft =
  (value) =>
  ({ theme }) => {
    if (theme.rtl) {
      return `margin-right: ${value}`;
    }
    return `margin-left: ${value}`;
  };

export const paddingRight =
  (value) =>
  ({ theme }) => {
    if (theme.rtl) {
      return `padding-left: ${value}`;
    }
    return `padding-right: ${value}`;
  };

export const paddingLeft =
  (value) =>
  ({ theme }) => {
    if (theme.rtl) {
      return `padding-right: ${value}`;
    }
    return `padding-left: ${value}`;
  };
