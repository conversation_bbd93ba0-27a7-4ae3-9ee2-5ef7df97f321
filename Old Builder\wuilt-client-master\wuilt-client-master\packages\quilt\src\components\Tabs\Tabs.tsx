import React from "react";
import styled from "styled-components";
import { Tab } from "./Tab";
import { Box } from "../Box";
import { color as getColor } from "../../utils";

type TabDescriptor<T> = T & {
  id: string | number;
  content: React.ReactNode;
  panelID?: string;
};

export interface TabsProps<T> {
  selected?: TabDescriptor<T>;
  tabs: TabDescriptor<T>[];
  children?: React.ReactNode;
  onSelect?(selectedTabIndex: TabDescriptor<T>): void;
}

function Tabs<T>(props: TabsProps<T>) {
  const { tabs, selected, onSelect, children } = props;

  const panelMarkup = children
    ? tabs?.map((tab, index) =>
        selected?.id === tab?.id ? (
          <div key={index} tabIndex={-1}>
            {children}
          </div>
        ) : (
          <div key={index} tabIndex={-1} hidden />
        )
      )
    : null;

  const tabsMarkup = tabs.map((tab, index) => (
    <Tab
      key={index}
      id={`${tab.id}`}
      measuring={false}
      panelID={tab.panelID || `${tab.id}-panel`}
      selected={tab?.id === selected?.id}
      onClick={() => {
        if (onSelect) onSelect(tab);
      }}
    >
      {tab.content}
    </Tab>
  ));

  return (
    <Box>
      <StyledWrapper>
        <StyledTabList role="tablist">{tabsMarkup}</StyledTabList>
      </StyledWrapper>
      {panelMarkup}
    </Box>
  );
}

export { Tabs };

/**
 * Styles
 */

const StyledWrapper = styled.div`
  border-bottom: 1px solid ${getColor("cloud", "darker")};
  padding: 0 4px;
  overflow-x: auto;
  overflow-y: hidden;
`;

const StyledTabList = styled.ul`
  display: flex;
  margin: 0;
  padding: 0;
  list-style: none;
`;
