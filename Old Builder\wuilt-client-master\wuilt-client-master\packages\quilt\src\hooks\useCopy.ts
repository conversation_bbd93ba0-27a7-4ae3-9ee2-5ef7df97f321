import { useEffect } from "react";
import { useStateWithTimeout } from "./useStateWithTimeout";

export function useCopy() {
  const [isCopied, setIsCopied, setStateWithTimeout, clearStateTimeout] =
    useStateWithTimeout(false, 2000);

  const handleCopy = async (text: string) => {
    if (!text) return;
    setIsCopied(false);
    await navigator?.clipboard?.writeText?.(text);
    setIsCopied(true);
    setStateWithTimeout(false);
  };

  useEffect(() => clearStateTimeout(), [clearStateTimeout]);

  return [isCopied, handleCopy] as const;
}
