declare const window: any;

async function createPromoterLead({
  email,
  uid,
  apiKey,
  tid = undefined,
  ref_id = undefined,
}) {
  const form = new URLSearchParams();

  if (email) form.append("email", email);
  if (uid) form.append("uid", uid);
  if (tid) form.append("tid", tid);
  if (ref_id) form.append("ref_id", ref_id);

  fetch(`https://firstpromoter.com/api/v1/track/signup`, {
    method: "POST",
    body: form,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      "x-api-key": apiKey!,
    },
  }).catch((error) => {
    //
  });
}

export async function trackFpr(
  user: any,
  fpr: string,
  fprTid: string,
  apiKey: string
) {
  if (!user?.id || !fpr) {
    return null;
  }
  const fpromKey = fprTid ? "tid" : "ref_id";
  const fpromValue = fprTid ?? fpr;
  return createPromoterLead({
    email: user.email,
    uid: user.decodedId,
    [fpromKey]: fpromValue,
    apiKey,
  });
}

export function getFprTid() {
  if (typeof window !== "undefined" && "FPROM" in window) {
    return window?.FPROM?.data?.tid;
  }
  return undefined;
}
