import React from "react";
import ChooseMessaging from "./ChooseMessaging";
import { ActivateYourButton } from "./ActivateYourButton";
import CustomizeYourButton from "./CustomizeYourButton";
import { YourButtonArrow } from "./YourButtonArrow";
import styled from "styled-components";
import { LocaleEnum } from "../../main";
interface WidgetSettingsProps {
  locale: LocaleEnum;
}

const WidgetSettings: React.FC<WidgetSettingsProps> = ({ locale }) => {
  return (
    <Container>
      <ChooseMessaging />
      <CustomizeYourButton />
      <ActivateYourButton locale={locale} />
      <YourButtonArrow />
    </Container>
  );
};

export { WidgetSettings };

const Container = styled.div`
  max-width: 1300px;
  margin: 0 auto;
  padding: 32px;

  input,
  input::placeholder {
    font-size: 16px;
  }
  .title-icon {
    height: 32px;
    width: 32px;
  }
  .position-and-shift-icon,
  .direction-and-display-icon {
    height: 24px;
    width: 24px;
  }
  .style-btn-icon {
    height: 30px;
    width: 30px;
  }
  .style-btnText-icon {
    width: 72px;
    height: 30px;
  }
  .chooseContainer::-webkit-scrollbar {
    width: 4px;
    background: #ffffff;
    border-radius: 10px;
  }
  .chooseContainer::-webkit-scrollbar-thumb {
    background: #98a2b3;
    border-radius: 10px;
    height: 53px;
  }
  .text-area {
    border-radius: 8px;
    height: 102px;
  }
  @media screen and (max-width: 768px) {
    max-width: 100%;
    padding: 28px 16px;
  }
`;
