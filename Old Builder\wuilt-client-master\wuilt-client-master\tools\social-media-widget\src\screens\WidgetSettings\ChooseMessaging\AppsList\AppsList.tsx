import React from "react";
import { Stack } from "@wuilt/quilt";
import SingleApp from "../SingleApp";
import { AppIconsList } from "../../../../shared/IconsList";

function AppsList() {
  return (
    <Stack
      boxShadow="0px 1px 2px 0px #1018280D"
      direction="row"
      wrap
      spacing="none"
      style={{ gap: "24px" }}
      padding="24px"
      border="1px solid #EAECF0"
      borderRadius="10px"
      flex="1 1"
    >
      {AppIconsList.map((app) => (
        <SingleApp key={app.name} app={app} />
      ))}
    </Stack>
  );
}

export default AppsList;
