import { Navigate } from "react-router-dom";
import { LoadingScreen } from "../components/loading-screen";
import { useAuth } from "./auth-provider";
import {
  getSearchQueries,
  clearSearchQueries,
  setSearchQueries,
} from "./auth-search-query";

interface PublicProps {
  children: any;
}
export const ShellPublic = ({ children }: PublicProps) => {
  const { loading, user } = useAuth();

  if (!loading && user) {
    // user is already logged in or has logged in
    const { redirect, redirectOut } = getSearchQueries();
    clearSearchQueries();

    if (redirectOut) window.location.href = redirectOut;

    return <Navigate to={redirect || "/"} replace />;
  }

  if (loading) return <LoadingScreen />;

  if (user) return null;

  // user will continue to auth screens (login/signup)
  const { fpr, redirectOut } = getSearchQueries();
  setSearchQueries(fpr, undefined, redirectOut);

  return children;
};
