import React from "react";
import { text, boolean } from "@storybook/addon-knobs";
import { action } from "@storybook/addon-actions";
import { InputField } from "./InputField";

export default {
  title: "Components/Form/InputField",
  component: InputField,
};

export const Playground = () => {
  const label = text("Label", "Label");
  const placeholder = text("Placeholder", "ex.");
  const prefix = text("Prefix", "$");
  const required = boolean("Required", false);

  return (
    <InputField
      type="text"
      label={label}
      prefix={prefix}
      placeholder={placeholder}
      isRequired={required}
      onChange={action("onChange")}
      loading
    />
  );
};
