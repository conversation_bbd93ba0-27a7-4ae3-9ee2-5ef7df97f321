import React from "react";
import { ResourceList } from "./ResourceList";
import { Thumbnail } from "../Thumbnail";
import { ResourceItem } from "./ResourceItem";
import { EmptyState } from "../EmptyState";

export default {
  title: "Components/ResourceList",
  component: ResourceList,
};

export const Playground = () => {
  return (
    <ResourceList
      // loading
      selectMode
      items={[
        {
          id: "341",
          url: "customers/341",
          name: "<PERSON> Jemis<PERSON>",
          location: "Decatur, USA",
          src: "https://images.unsplash.com/photo-1570118990526-e3c274f55a25?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=3934&q=80",
        },
        {
          id: "256",
          url: "customers/256",
          name: "<PERSON>cho<PERSON>",
          location: "Los Angeles, USA",
          src: "https://images.unsplash.com/photo-1570118990526-e3c274f55a25?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=3934&q=80",
        },
      ]}
      renderItem={renderItem}
      emptyState={
        <EmptyState illustration={null} primaryAction={null} title="Nothing" />
      }
    />
  );

  function renderItem(item) {
    const { id, url, name, src } = item;
    const media = <Thumbnail size="medium" src={src} />;
    // const shortcutActions = url
    //   ? [{ content: "View latest order", url }]
    //   : null;
    return (
      <ResourceItem
        id={id}
        media={media}
        // url={url}
        // accessibilityLabel={`View details for ${name}`}
        // shortcutActions={shortcutActions}
        // persistActions
      >
        <h3>
          <p>{name}</p>
        </h3>
        <div>
          <div>{url}</div>
          <div>{id}</div>
        </div>
      </ResourceItem>
    );
  }
};
