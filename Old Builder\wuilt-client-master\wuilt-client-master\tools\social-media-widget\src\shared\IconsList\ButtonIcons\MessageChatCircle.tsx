import { TIconType } from "../types";

export function MessageChatCircle({
  size = 40,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.07847 9.35741C5.02668 9.0236 4.99981 8.68163 4.99981 8.33342C4.99981 4.65152 8.00421 1.66675 11.7103 1.66675C15.4165 1.66675 18.4209 4.65152 18.4209 8.33342C18.4209 9.16514 18.2675 9.96129 17.9875 10.6955C17.9293 10.848 17.9002 10.9242 17.887 10.9838C17.8739 11.0427 17.8689 11.0842 17.8674 11.1446C17.866 11.2056 17.8743 11.2727 17.8908 11.407L18.2263 14.1322C18.2626 14.4272 18.2808 14.5747 18.2317 14.6819C18.1887 14.7759 18.1123 14.8505 18.0174 14.8913C17.9091 14.9379 17.762 14.9164 17.4679 14.8733L14.8135 14.4842C14.675 14.4639 14.6056 14.4537 14.5425 14.4541C14.4801 14.4544 14.4369 14.459 14.3758 14.4719C14.314 14.4849 14.2351 14.5144 14.0773 14.5735C13.3412 14.8492 12.5435 15.0001 11.7103 15.0001C11.3618 15.0001 11.0196 14.9737 10.6854 14.9228M6.35949 18.3334C8.83023 18.3334 10.8332 16.2814 10.8332 13.7501C10.8332 11.2188 8.83023 9.16675 6.35949 9.16675C3.88874 9.16675 1.8858 11.2188 1.8858 13.7501C1.8858 14.2589 1.96673 14.7484 2.11613 15.2057C2.17928 15.399 2.21085 15.4957 2.22122 15.5617C2.23204 15.6306 2.23393 15.6694 2.2299 15.739C2.22604 15.8057 2.20935 15.8812 2.17596 16.032L1.6665 18.3334L4.16217 17.9926C4.29839 17.974 4.3665 17.9647 4.42598 17.9651C4.4886 17.9655 4.52184 17.9689 4.58326 17.9811C4.64159 17.9928 4.7283 18.0234 4.90173 18.0846C5.35866 18.2458 5.84909 18.3334 6.35949 18.3334Z"
        stroke={color || "currentColor"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
