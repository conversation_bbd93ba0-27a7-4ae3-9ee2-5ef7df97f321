import {
  <PERSON><PERSON>,
  <PERSON>,
  Heading,
  SourceCodeIcon,
  Stack,
  Text,
} from "@wuilt/quilt";
import React, { useState } from "react";
import { FormattedMessage } from "react-intl";
import GetButtonModal from "./GetButtonModal";
import { useWidget } from "../../../context/widget-provider";

interface GetButtonProps {}

const GetButton: React.FC<GetButtonProps> = () => {
  const widget = useWidget();
  const [openModal, setOpenModal] = useState(false);

  const onClose = () => {
    setOpenModal(false);
  };

  const onOpen = () => {
    setOpenModal(true);
  };

  return (
    <>
      <Card
        borderRadius="10px"
        style={{
          background: "linear-gradient(94deg, #125D56 0%, #0E9384 99.91%)",
        }}
      >
        <Card.Header>
          <Stack spacing="tight">
            <Heading color="white" fontWeight="semiBold">
              <FormattedMessage defaultMessage="Install Widget" id="N03g5t" />
            </Heading>
            <Text
              color={{ product: "lightActive" }}
              fontSize="sm"
              fontWeight="medium"
            >
              <FormattedMessage
                defaultMessage="Copy the code and place it into your website"
                id="SRToXV"
              />
            </Text>
          </Stack>
        </Card.Header>
        <Card.Body>
          <Button
            outlined
            fullWidth
            borderRadius="8px"
            size="large"
            contentAlign="center"
            contentWidth="auto"
            prefixIcon={<SourceCodeIcon size="xl" />}
            onClick={onOpen}
          >
            <FormattedMessage defaultMessage="Get your Button" id="en8NdU" />
          </Button>
        </Card.Body>
      </Card>
      <GetButtonModal
        show={openModal}
        onClose={onClose}
        settings={widget.settings}
      />
    </>
  );
};

export { GetButton };
