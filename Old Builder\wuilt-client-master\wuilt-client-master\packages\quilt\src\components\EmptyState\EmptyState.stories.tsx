import React from "react";
import { EmptyState } from "./EmptyState";
import { Button } from "../Button";
import { AddNewCollectionIllustration } from "../illustrations";

export default {
  title: "Components/EmptyState ",
  component: EmptyState,
};

export const Playground = () => {
  return (
    <EmptyState
      primaryAction={<Button>Create First</Button>}
      title="test"
      illustration={<AddNewCollectionIllustration size="large" />}
      note="Empty state notes"
    />
  );
};
