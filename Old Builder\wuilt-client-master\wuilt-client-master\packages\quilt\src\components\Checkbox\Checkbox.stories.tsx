// import React from "react";
// import { Field, Form } from "../Form";

// import { Checkbox } from "./Checkbox";

// export default {
//   title: "Components/Form/Checkbox",
//   component: Checkbox,
// };

// const CheckBoxValues = [false, true, "indeterminate"] as const;

// export const Playground = () => {
//   const [state, setState] = React.useState(0);
//   return (
//     <Form onSubmit={() => {}}>
//       {({ formProps }) => (
//         <form {...formProps}>
//           <Field name="checkbox" defaultValue={0}>
//             {() => (
//               <Checkbox
//                 value={CheckBoxValues[state % 3]}
//                 onChange={() => setState((prev) => prev + 1)}
//                 label="Check me on!"
//                 fontWeight="bold"
//               />
//             )}
//           </Field>
//         </form>
//       )}
//     </Form>
//   );
// };
