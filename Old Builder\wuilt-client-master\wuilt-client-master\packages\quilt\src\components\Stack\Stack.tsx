import React from "react";
import styled, { css } from "styled-components";
import { LayoutProps, PositionProps } from "styled-system";
import mediaQueries from "../../utils/mediaQuery";
import { DIRECTIONS, SPACINGS, SPACINGS_AFTER } from "./consts";
import { DEVICES } from "../../utils/mediaQuery/consts";
import isDefined from "./helpers/isDefined";
import getViewportFlexStyles from "./helpers/getViewportFlexStyles";
import getChildrenMargin from "./helpers/getChildrenMargin";
import overwriteStyles from "../../utils/mediaQuery/overwriteStyles";
import { getMediaQueryStyles } from "../../utils/mediaQuery/getMediaQueryStyles";
import { Global } from "../../common/types";
import {
  ColorProps,
  TypographyProps,
  SpaceProps,
  ShadowProps,
  BorderProps,
} from "../../themes/property-overriding";
import getStackCSS from "./helpers/getStackCSS";
import { UnstyledLink } from "../UnstyledLink";

export type FlexDirection = "row" | "column" | "row-reverse" | "column-reverse";
type FlexAlign = "start" | "end" | "center" | "stretch" | "baseline";
type FlexJustify = "start" | "end" | "center" | "between" | "around";
export type StackSpacing =
  | "none"
  | "extraTight"
  | "tight"
  | "condensed"
  | "compact"
  | "natural"
  | "comfy"
  | "loose"
  | "extraLoose";

interface StackCSS
  extends ColorProps,
    TypographyProps,
    SpaceProps,
    ShadowProps,
    BorderProps,
    LayoutProps,
    PositionProps {}

export interface StackFlexStyles extends StackCSS {
  readonly inline?: boolean;
  readonly spacing?: StackSpacing;
  readonly align?: FlexAlign;
  readonly justify?: FlexJustify;
  readonly direction?: FlexDirection;
  readonly grow?: boolean;
  readonly wrap?: boolean;
  readonly shrink?: boolean;
  readonly basis?: string;
  readonly flex?: string | boolean;
  readonly style?: React.CSSProperties;
  readonly gap?: string;
}

interface GeneralStackProps extends Global {
  readonly className?: string;
  readonly cursor?: React.CSSProperties["cursor"];
  readonly as?: React.ComponentType | string | null;
  readonly href?: string;
  readonly external?: boolean;
  readonly to?: string;
  readonly children?: React.ReactNode;
  readonly onClick?: React.MouseEventHandler<HTMLDivElement>;
  readonly onHover?: StackCSS;
  readonly OnMouseEnter?: React.MouseEventHandler<HTMLDivElement>;
  readonly OnMouseLeave?: React.MouseEventHandler<HTMLDivElement>;
}

export interface StackProps extends StackFlexStyles, GeneralStackProps {
  readonly smallMobile?: StackFlexStyles;
  readonly mediumMobile?: StackFlexStyles;
  readonly largeMobile?: StackFlexStyles;
  readonly tablet?: StackFlexStyles;
  readonly desktop?: StackFlexStyles;
}

const Stack = React.forwardRef<HTMLElement, StackProps>((props, ref) => {
  const {
    direction = DIRECTIONS.COLUMN,
    spacing = SPACINGS.NATURAL,
    spaceAfter = SPACINGS_AFTER.NONE,
    grow,
    dataTest,
    inline,
    align,
    justify,
    wrap,
    shrink,
    className,
    basis,
    children,
    smallMobile,
    mediumMobile,
    largeMobile,
    tablet,
    desktop,
    as = props.href ? "a" : props.to ? UnstyledLink : "div",
    cursor,
    style,
    to,
    href,
    external,
    flex,
    onClick,
    onHover,
    OnMouseEnter,
    OnMouseLeave,
    gap,
    ...systemStyles
  } = props;

  const largeDesktop = {
    direction,
    align,
    justify,
    wrap,
    grow,
    basis,
    inline,
    shrink,
    spacing,
    spaceAfter,
    flex,
    ...systemStyles,
  };

  return (
    <StyledStack
      className={className}
      ref={ref}
      dataTest={dataTest}
      cursor={cursor}
      onClick={onClick}
      onHover={onHover}
      onMouseEnter={OnMouseEnter}
      onMouseLeave={OnMouseLeave}
      onMouse
      smallMobile={overwriteStyles(largeDesktop, smallMobile)}
      mediumMobile={overwriteStyles(largeDesktop, mediumMobile)}
      largeMobile={overwriteStyles(largeDesktop, largeMobile)}
      tablet={overwriteStyles(largeDesktop, tablet)}
      desktop={overwriteStyles(largeDesktop, desktop)}
      largeDesktop={largeDesktop}
      gap={gap}
      element={as}
      to={to}
      href={href}
      external={external}
      styles={getMediaQueryStyles({
        largeDesktop: style,
        desktop: desktop?.style,
        tablet: tablet?.style,
        largeMobile: largeMobile?.style,
        mediumMobile: mediumMobile?.style,
        smallMobile: smallMobile?.style,
      })}
    >
      {children}
    </StyledStack>
  );
});

Stack.displayName = "Stack";

export { Stack };

/**
 *
 * styles
 *
 */

const stackMediaQueries = (props: StackProps) =>
  DEVICES.map((viewport, index, devices) =>
    viewport in mediaQueries
      ? mediaQueries[viewport](css`
          ${isDefined(props[viewport]) && getViewportFlexStyles(viewport)};
          ${getChildrenMargin({ viewport, index, devices })}
        `)
      : viewport === "largeDesktop" &&
        css`
          ${getViewportFlexStyles(viewport)};
          ${getChildrenMargin({ viewport, index, devices })}
        `
  );

const stackOnHoverStyles = ({ onHover, theme }) => {
  const cssProps = { ...onHover, theme };
  return css`
    ${getStackCSS(cssProps)};
  `;
};

const StyledStack = styled(
  React.forwardRef<HTMLElement, any>(
    (
      {
        className,
        element: Element,
        children,
        dataTest,
        onClick,
        onMouseEnter,
        onMouseLeave,
        to,
        href,
        external,
      },
      ref
    ) => (
      <Element
        ref={ref}
        className={className}
        data-test={dataTest}
        onClick={onClick}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        to={to}
        href={href}
        external={external}
      >
        {children}
      </Element>
    )
  )
)`
  /* just apply all mediaQueries
   largeDesktop - default values are not mediaQuery and needs to be rendered differently */
  ${stackMediaQueries}
  ${({ styles }) => styles};
  ${({ cursor }) => cursor && `cursor: ${cursor}`};

  &:hover,
  &:active {
    ${
      /* @ts-ignore */
      stackOnHoverStyles
    }
    * {
      ${stackOnHoverStyles}
    }
  }
`;
