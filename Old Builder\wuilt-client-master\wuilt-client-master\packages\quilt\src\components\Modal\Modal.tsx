import React from "react";
import { Transition } from "react-transition-group";
import styled, { css, CSSProperties } from "styled-components";
import cuid from "cuid";
import { Fill, Slot } from "../SlotFill";
import { CloseIcon } from "../icons";
import { ButtonIcon } from "../ButtonIcon";

import { mediaQueries } from "../../utils";
import { useMediaQuery } from "../../hooks/useMediaQuery";
import { Box, BoxProps } from "../Box";

const SLOT_NAME = "modals";

const ModalWidth = {
  large: "70%",
  medium: "50%",
  small: "30%",
  auto: "auto",
};

const desktopModalTransitionStyles = {
  entering: { transform: "translateY(0)", opacity: 1 },
  entered: { transform: "translateY(0)", opacity: 1 },
  exiting: { transform: "translateY(-5rem)", opacity: 0 },
  exited: { transform: "translateY(-5rem)", opacity: 0 },
};

const mobileModalTransitionStyles = {
  entering: { transform: "translateY(0)", opacity: 1 },
  entered: { transform: "translateY(0)", opacity: 1 },
  exiting: { transform: "translateY(5rem)", opacity: 0 },
  exited: { transform: "translateY(5rem)", opacity: 0 },
};

function getModalTransition(isMobile: boolean, transitionState) {
  return isMobile
    ? { ...mobileModalTransitionStyles[transitionState] }
    : { ...desktopModalTransitionStyles[transitionState] };
}

const backdropTransitionStyles = {
  entering: { opacity: 1 },
  entered: { opacity: 1 },
  exiting: { opacity: 0 },
  exited: { opacity: 0 },
};

export type ModalWidthType = keyof typeof ModalWidth;

export interface ModalProps {
  children?: React.ReactNode;
  show: boolean;
  id?: string;
  onClose: any;
  modalWidth?: ModalWidthType;
  noTransition?: boolean;
  closeOnEscape?: boolean;
  position?: number;
  fullHeight?: boolean;
  background?: CSSProperties["background"];
}

const Backdrop = styled.div<{ duration: number; zIndex: number }>`
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(52, 64, 84, 0.7);
  backdrop-filter: blur(2px);
  z-index: ${({ zIndex }) => 999 + zIndex * 2};
  overflow-x: hidden;
  overflow-y: auto;
  outline: none;
  box-sizing: border-box;
  opacity: 0;
  ${({ duration }) => `  transition: all ${duration}ms ease-in-out`};
`;

const ModalWrapper = styled.div<{ zIndex: number }>`
  position: fixed;
  z-index: ${({ zIndex }) => 1000 + zIndex * 2};
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  top: 0;
  left: 0;
`;

const ModalBody = styled.div`
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
`;

const ModalContainer = styled.div<{
  modalWidth: ModalWidthType;
  duration: number;
  zIndex: number;
  fullHeight?: boolean;
  background: CSSProperties["background"];
}>`
  box-sizing: border-box;
  position: fixed;
  z-index: ${({ zIndex }) => 1001 + zIndex * 2};
  max-height: 95%;
  overflow: visible;
  background: ${({ theme, background }) =>
    background || `${theme.palette.white.normal}`};
  border-radius: 5px;
  width: ${({ modalWidth }) => ModalWidth[modalWidth]};
  ${({ duration }) => `  transition: all ${duration}ms ease-in-out`};
  transform: translateY(0);
  opacity: 1;

  ${ModalBody} {
    ${({ fullHeight }) => fullHeight && "height:75vh;"}
  }

  ${mediaQueries.desktop(css`
    min-width: ${ModalWidth.medium};
  `)}

  ${mediaQueries.tablet(css`
    width: ${ModalWidth.large};
  `)}

  ${mediaQueries.largeMobile(css`
    width: 100%;
    bottom: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  `)}
`;

const CloseContainer = styled.div`
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
  justify-content: flex-end;
  align-items: center;
  box-sizing: border-box;
  height: 52px;
  width: 100%;
  padding: 16px 14px;
`;

const ModalCloseButton = ({ onClick }) => {
  return (
    <ButtonIcon onClick={onClick} dataTest="button-close" onlyIcon transparent>
      <CloseIcon size="lg" />
    </ButtonIcon>
  );
};

const Modal: React.FC<ModalProps> & {
  Header: React.FC<HeaderProps>;
  Footer: React.FC<HeaderProps>;
  Body?: any;
} = (props) => {
  const {
    show,
    onClose,
    children,
    modalWidth = "large",
    noTransition,
    closeOnEscape = true,
    position = 0,
    fullHeight,
    id,
    background,
  } = props;
  const duration = noTransition ? 0 : 200;
  const idRef = React.useRef(id || cuid());
  const isMobile = useMediaQuery.isMobile();

  React.useEffect(() => {
    function handleKeyupEvent(event) {
      if (event.key === "Escape") {
        onClose();
      }
    }

    if (show) {
      document.body.style.overflow = "hidden";
      if (closeOnEscape) {
        document.addEventListener("keyup", handleKeyupEvent);
      }
    }

    // cleanUp
    return () => {
      document.body.style.overflow = "auto";
      document.removeEventListener("keyup", handleKeyupEvent);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [show]);

  return (
    <Fill name={SLOT_NAME}>
      <Transition
        key={idRef.current}
        in={show}
        timeout={duration}
        mountOnEnter
        unmountOnExit
      >
        {(transitionState) => (
          <>
            <Backdrop
              duration={duration}
              zIndex={position}
              style={backdropTransitionStyles[transitionState]}
              onClick={(e) => e.stopPropagation()}
            />
            <ModalWrapper zIndex={position}>
              <ModalContainer
                zIndex={position}
                modalWidth={modalWidth}
                duration={duration}
                style={getModalTransition(isMobile || false, transitionState)}
                fullHeight={fullHeight}
                onClick={(e) => e.stopPropagation()}
                background={background}
              >
                {onClose && (
                  <CloseContainer>
                    <ModalCloseButton onClick={onClose} />
                  </CloseContainer>
                )}
                {children}
              </ModalContainer>
            </ModalWrapper>
          </>
        )}
      </Transition>
    </Fill>
  );
};

export interface HeaderProps extends BoxProps {
  children: React.ReactNode;
  splitHeader?: boolean;
}
Modal.Header = ({
  children,
  splitHeader = true,
  ...restProps
}: HeaderProps) => {
  if (!children) {
    return null;
  }
  return (
    <Box
      p="16px 20px"
      minHeight="52px"
      display="flex"
      alignItems="center"
      borderBottom={splitHeader ? "1px solid #CBD5E1" : "none"}
      {...restProps}
    >
      {children}
    </Box>
  );
};

Modal.Body = ({ children, ...restProps }) => {
  return (
    <Box padding="20px" maxHeight="70VH" overflowY="auto" {...restProps}>
      {children}
    </Box>
  );
};

export interface FooterProps extends BoxProps {
  children: React.ReactNode;
}
Modal.Footer = ({ children, ...restProps }: FooterProps) => {
  if (!children) {
    return null;
  }
  return (
    <Box borderTop="1px solid #CBD5E1" padding="16px 20px" {...restProps}>
      {children}
    </Box>
  );
};

export interface ModalSlotProps {
	name?: string
	Wrapper?: any
}
const ModalSlot = ({ name = SLOT_NAME, Wrapper = undefined }: ModalSlotProps) => (
  <Slot name={name} Wrapper={Wrapper} />
);

Modal.displayName = "Modal";

export { Modal, ModalSlot };
