import React, { useEffect, useRef } from "react";
import styled, { css } from "styled-components";
import { unstyledLink } from "../../styles/links";
import { unstyledButton } from "../../styles/botton";
import { color } from "../../utils/colors";
import { spacing } from "../../styles/spacing";
// import {focusFirstFocusableNode} from '@shopify/javascript-utilities/focus';
// import {UnstyledLink} from '../../../UnstyledLink';
// import {handleMouseUpByBlurring} from '../../../../utilities/focus';

const StyledTabTitle = styled.span`
  padding: ${spacing("natural")} 0 calc(${spacing("natural")} - 3px) 0;
  margin: 0 ${spacing("condensed")};
  display: block;
  font-size: 14px;
  border-bottom: 4px solid transparent;
  /* min-width: 50px; */
`;

const StyledTabContainer = styled.li<{ selected: boolean }>`
  display: flex;

  margin: 0;
  padding: 0;
  /* flex: 1 1 auto; */
`;

const StyledTab = styled.button<{ selected: boolean }>`
  ${unstyledLink};
  ${unstyledButton};

  position: relative;
  justify-content: center;
  width: 100%;
  /* IE11 fix for overflowing flex items from parent container */
  min-width: 50px;
  margin-top: 1px;
  margin-bottom: -1px;
  /* padding: 0 ${spacing("condensed")}; */
  outline: none;
  text-align: center;
  white-space: nowrap;
  text-decoration: none;
  font-weight: 600;
  cursor: pointer;
  color: ${color("ink", "light")};

  ${({ selected }) =>
    selected
      ? css`
          color: ${color("product", "normal")};
          font-weight: 600;
          ${StyledTabTitle} {
            border-bottom: 4px solid ${color("product")};
          }
        `
      : ""};

  &:hover {
    text-decoration: none;

    ${StyledTabTitle} {
      /* @include text-emphasis-normal; */
      /* background-color: var(--p-surface-hovered, transparent); */
      border-bottom: 4px solid ${color("product")};
    }
  }
`;

export interface TabProps {
  id: string;
  focused?: boolean;
  siblingTabHasFocus?: boolean;
  selected?: boolean;
  panelID?: string;
  children?: React.ReactNode;
  measuring?: boolean;
  accessibilityLabel?: string;
  onClick?(id: string): void;
}

export function Tab({
  id,
  focused,
  siblingTabHasFocus,
  children,
  onClick,
  selected,
  panelID,
  measuring,
  accessibilityLabel,
}: TabProps) {
  const wasSelected = useRef(selected);
  const panelFocused = useRef(false);
  const node = useRef<HTMLLIElement | null>(null);

  // A tab can start selected when it is moved from the disclosure dropdown
  // into the main list, so we need to send focus from the tab to the panel
  // on mount and update
  useEffect(() => {
    if (measuring) {
      return;
    }

    // Because of timing issues with the render, we may still have the old,
    // in-disclosure version of the tab that has focus. Check for this
    // as a second indicator of focus
    const itemHadFocus =
      focused || (document.activeElement && document.activeElement.id === id);

    // If we just check for selected, the panel for the active tab will
    // be focused on page load, which we don’t want
    if (itemHadFocus && selected && panelID != null && !panelFocused.current) {
      // focusPanelID(panelID);
      panelFocused.current = true;
    }

    // if (selected && !wasSelected.current && panelID != null) {
    //   focusPanelID(panelID);
    // } else if (focused && node.current != null) {
    //   focusFirstFocusableNode(node.current);
    // }

    wasSelected.current = selected;
  }, [focused, id, measuring, panelID, selected]);

  const handleClick = onClick && onClick.bind(null, id);

  // const className = classNames(styles.Tab, selected && styles["Tab-selected"]);

  let tabIndex: 0 | -1;

  if (selected && !siblingTabHasFocus && !measuring) {
    tabIndex = 0;
  } else if (focused && !measuring) {
    tabIndex = 0;
  } else {
    tabIndex = -1;
  }

  // const tabTitleClassNames = classNames(
  //   styles.Title,
  //   newDesignLanguage && styles.newDesignLanguage
  // );

  const markup = (
    <StyledTab
      id={id}
      role="tab"
      type="button"
      tabIndex={tabIndex}
      selected={selected as boolean}
      onClick={handleClick}
      aria-selected={selected}
      aria-controls={panelID}
      aria-label={accessibilityLabel}
      // onMouseUp={handleMouseUpByBlurring}
    >
      {/* className={tabTitleClassNames} */}
      <StyledTabTitle>{children}</StyledTabTitle>
    </StyledTab>
  );

  return (
    <StyledTabContainer selected={selected as boolean} ref={node}>
      {markup}
    </StyledTabContainer>
  );
}

// function focusPanelID(panelID: string) {
//   const panel = document.getElementById(panelID);
//   if (panel) {
//     panel.focus({ preventScroll: true });
//   }
// }
