export const TOKENS = {
  // spinnerWidth: "spinnerWidth",
  // spinnerHeight: "spinnerHeight",
  // marginRightIcon: "marginRightIcon",
  // Size tokens
  // heightButton: "heightButton",
  // loadingWidth: "loadingWidth",
  // loadingHeight: "loadingHeight",
  // fontSizeButton: "fontSizeButton",
  // paddingButton: "paddingButton",
  // paddingButtonWithIcons: "paddingButtonWithIcons",
  // paddingButtonWithLeftIcon: "paddingButtonWithLeftIcon",
  // paddingButtonWithRightIcon: "paddingButtonWithRightIcon",
  // Type tokens
  background: "background",
  backgroundHover: "backgroundHover",
  backgroundActive: "backgroundActive",
  backgroundFocus: "backgroundFocus",
  colorText: "colorText",
  colorTextHover: "colorTextHover",
  colorTextActive: "colorTextActive",
  borderColorFocus: "borderColorFocus",
};

export const BUTTON_STATES = {
  DEFAULT: "default",
  HOVER: "hover",
  ACTIVE: "active",
  FOCUS: "focus",
};

export const TYPE_OPTIONS = {
  PRIMARY: "primary",
  SECONDARY: "secondary",
  SUCCESS: "success",
  CRITICAL: "critical",
  WARNING: "warning",
  FAILED: "failled",
  WHITE: "white",
  PRIMARY_SUBTLE: "primarySubtle",
  CRITICAL_SUBTLE: "criticalSubtle",
};

export const SIZE_OPTIONS = {
  SMALL: "small",
  NORMAL: "normal",
  LARGE: "large",
};
