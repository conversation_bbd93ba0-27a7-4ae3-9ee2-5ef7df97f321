import React from "react";
import styled from "styled-components";
import { ChevronSelectorHorizontalIcon } from "../icons";
import { Divider } from "../Divider";
import * as utils from "../../utils";

export interface ResizableHandleProps {
  top?: boolean;
  right?: boolean;
  bottom?: boolean;
  left?: boolean;
  topRight?: boolean;
  bottomRight?: boolean;
  bottomLeft?: boolean;
  topLeft?: boolean;
}

const ResizableHandle: React.FC<ResizableHandleProps> = ({
  top,
  right,
  bottom,
  left,
  topRight,
  bottomRight,
  bottomLeft,
  topLeft,
}) => {
  const rotate =
    top || bottom
      ? 90
      : topRight || bottomLeft
      ? -45
      : topLeft || bottomRight
      ? 45
      : right || left
      ? 0
      : 0;

  const verticalDivider = right || left;
  const horizontalDivider = top || bottom;

  return (
    <>
      <StyledResizableHandle>
        <ChevronSelectorHorizontalIcon rotate={rotate} viewBox="0 0 20 20" />
      </StyledResizableHandle>
      {verticalDivider && <StyledVerticalDivider vertical width="3px" />}
      {horizontalDivider && <StyledHorizontalDivider horizontal height="3px" />}
    </>
  );
};

export { ResizableHandle };

/**
 * Styles
 */

const StyledVerticalDivider = styled(Divider)`
  z-index: -1;
  position: absolute;
  transform: translateX(-50%);
  background-color: ${utils.color("product")};
  top: 0;
  left: 50%;
`;

const StyledHorizontalDivider = styled(Divider)`
  z-index: -1;
  position: absolute;
  background-color: ${utils.color("product")};
  left: 0;
  top: 50%;
  transform: translateY(-50%);
`;

const StyledResizableHandle = styled.span`
  display: inline-block;
  border-radius: 50%;
  background-color: white;
  outline: 1px solid ${utils.color("product")};
  padding: 2px 4px;
`;
