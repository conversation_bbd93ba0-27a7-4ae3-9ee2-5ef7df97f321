import React from "react";
import { Form, FormSubmit } from "../Form";
import { InputCard } from "./InputCard";

export default {
  title: "Components/Form/InputCard",
  component: InputCard,
};

export const Playground = () => {
  const [card, setCard] = React.useState({});

  return (
    <Form
      onSubmit={(data: {
        card: {
          number: string;
          cvc: string;
          expiry: { mm: string; yy: string };
        };
      }) => {
        setCard(data);
      }}
    >
      {({ formProps }) => (
        <form {...formProps}>
          {
            // eslint-disable-next-line no-console
            <InputCard onChange={(card) => console.log(card)} />
          }
          <br />
          <FormSubmit>submit</FormSubmit>
          <pre>{JSON.stringify(card, null, 2)}</pre>
        </form>
      )}
    </Form>
  );
};
