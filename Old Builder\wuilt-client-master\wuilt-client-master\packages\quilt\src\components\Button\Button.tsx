import React from "react";
import { useTheme } from "styled-components";
import { ButtonPrimitive } from "./primitive";
import getCommonProps from "./helpers/getCommonProps";
import getButtonStyles from "./helpers/getButtonStyles";
import getIconContainer from "./helpers/getIconContainer";
import getButtonIconForeground from "./helpers/getButtonIconForeground";
import { ButtonCommonProps } from "./primitive/ButtonPrimitive.types";
import { ColorsType } from "../../themes";

export interface ButtonProps extends ButtonCommonProps {
  readonly color?: ColorsType;
  readonly plain?: boolean;
  readonly outlined?: boolean;
  readonly transparent?: boolean;
}

const Button = React.forwardRef<any, ButtonProps>((props, ref) => {
  const {
    outlined,
    disabled,
    transparent,
    compact,
    plain,
    color = props.plain ? "secondary" : "primary",
    ...restProps
  } = props;

  const theme = useTheme();
  const propsWithTheme = { theme, ...props };
  const commonProps = getCommonProps(propsWithTheme);
  const buttonStyles = getButtonStyles({
    theme,
    outlined,
    transparent,
    plain,
    color,
  });
  const icons = getIconContainer({
    ...propsWithTheme,
    iconForeground: getButtonIconForeground(buttonStyles),
  } as any);

  return (
    <ButtonPrimitive
      ref={ref}
      disabled={disabled}
      {...buttonStyles}
      {...commonProps}
      {...restProps}
      {...icons}
    />
  );
});

Button.displayName = "Button";

export { Button };
