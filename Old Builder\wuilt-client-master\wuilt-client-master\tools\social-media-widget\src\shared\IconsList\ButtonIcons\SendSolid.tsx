import { TIconType } from "../types";

export function SendSolid({ size = 40, width, height, color }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.996 1.21467C17.6662 1.10449 17.3591 1.18417 17.2065 1.22851C17.0401 1.27683 16.8418 1.35425 16.6429 1.43191L2.51199 6.94641C2.28963 7.03316 2.07426 7.11717 1.90803 7.19937C1.7644 7.27038 1.45911 7.42845 1.28871 7.75646C1.10079 8.11818 1.10104 8.54881 1.28939 8.9103C1.46018 9.23811 1.76565 9.39582 1.90936 9.46667C2.07568 9.54867 2.29112 9.63242 2.51357 9.71889L6.44692 11.2485C6.74036 11.3626 6.88707 11.4197 7.03304 11.4232C7.16205 11.4263 7.29002 11.3994 7.40686 11.3446C7.53905 11.2826 7.65037 11.1713 7.87299 10.9487L11.9108 6.91083C12.2363 6.58539 12.7639 6.58539 13.0893 6.91083C13.4148 7.23626 13.4148 7.7639 13.0893 8.08934L9.0515 12.1272C8.82888 12.3498 8.71756 12.4611 8.65557 12.5933C8.60078 12.7101 8.57387 12.8381 8.57696 12.9671C8.58047 13.1131 8.63753 13.2598 8.75164 13.5532L10.2812 17.4865C10.3677 17.709 10.4515 17.9245 10.5335 18.0908C10.6043 18.2345 10.7621 18.54 11.0899 18.7108C11.4514 18.8991 11.882 18.8994 12.2437 18.7115C12.5717 18.541 12.7298 18.2358 12.8008 18.0921C12.883 17.9259 12.967 17.7106 13.0537 17.4882L18.5683 3.35726C18.6459 3.15834 18.7233 2.96007 18.7717 2.79371C18.816 2.64105 18.8957 2.334 18.7855 2.00419C18.661 1.63157 18.3686 1.33915 17.996 1.21467Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
