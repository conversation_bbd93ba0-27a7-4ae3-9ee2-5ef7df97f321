import styled from "styled-components";

/**
 * Provide a styled container for form sections.
 */
const FormSectionWrapper = styled.div``;
// margin-top: ${multiply(gridSize, 3)}px;

/**
 * Provide a styled container for form section title
 */
/* ${h600}; */
/* line-height: ${multiply(gridSize, 4)}px; */
/* margin-right: ${multiply(gridSize, 4)}px; */
const FormSectionTitle = styled.h3`
  margin-top: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

/**
 * Provide a styled container for form section content.
 */
/* margin-top: ${gridSize}px; */
const FormSectionDescription = styled.div``;

export default FormSectionWrapper;
export { FormSectionTitle, FormSectionDescription };
