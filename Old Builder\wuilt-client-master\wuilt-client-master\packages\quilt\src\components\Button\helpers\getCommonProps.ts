import { DefaultTheme } from "styled-components";
import getSizeToken from "./getSizeToken";
import getPadding from "./getPadding";
import { SIZE_OPTIONS } from "../consts";
import { theme } from "../../../themes";
import { ButtonCommonProps } from "../primitive/ButtonPrimitive.types";

type Color = keyof typeof theme.base.colors;
type CommonProps = {
  height: any;
  fontSize: any;
  width: any;
  fontWeight: any;
  contentWidth: any;
  contentAlign: any;
  padding: any;
};
export interface ButtonProps extends ButtonCommonProps {
  readonly color?: Color;
  readonly plain?: boolean;
  readonly outlined?: boolean;
  readonly transparent?: boolean;
}
type PropsWithTheme = ButtonProps & { theme: DefaultTheme };
const getCommonProps = ({
  width,
  size = SIZE_OPTIONS.NORMAL as any,
  theme,
  suffixIcon,
  prefixIcon,
  children,
  contentAlign,
  contentWidth,
  compact,
  padding,
}: PropsWithTheme): CommonProps => {
  const onlyIcon = Boolean(prefixIcon && !children);
  const hasCenteredContent = Boolean(
    (prefixIcon && !children) || (children && !(prefixIcon || suffixIcon))
  );
  return {
    ...getSizeToken(size),
    width,
    fontWeight: theme.base.fontWeight.semiBold,
    contentWidth: contentWidth || "100%",
    contentAlign:
      contentAlign ||
      (onlyIcon || hasCenteredContent ? "center" : "space-between"),
    padding: compact
      ? "0"
      : padding || getPadding(onlyIcon, suffixIcon, prefixIcon, size, theme),
  };
};

export default getCommonProps;
