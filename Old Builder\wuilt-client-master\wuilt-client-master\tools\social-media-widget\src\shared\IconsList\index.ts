import { AppsEnum, ButtonIconsEnum } from "../types";
import * as AppIcons from "./AppsIcons";
import * as ButtonIcons from "./ButtonIcons";

export const ButtonIcon = {
  [ButtonIconsEnum.MessageChatCircleSolid]: ButtonIcons.MessageChatCircleSolid,
  [ButtonIconsEnum.MessageChatSquareSolid]: ButtonIcons.MessageChatSquareSolid,
  [ButtonIconsEnum.MessageCircleSolid]: ButtonIcons.MessageCircleSolid,
  [ButtonIconsEnum.MessageDotsCircleSolid]: ButtonIcons.MessageDotsCircleSolid,
  [ButtonIconsEnum.MessageDotsSquareSolid]: ButtonIcons.MessageDotsSquareSolid,
  [ButtonIconsEnum.MessageSmileCircleSolid]:
    ButtonIcons.MessageSmileCircleSolid,
  [ButtonIconsEnum.MessageSmileSquareSolid]:
    ButtonIcons.MessageSmileSquareSolid,
  [ButtonIconsEnum.MessageSquareSolid]: ButtonIcons.MessageSquareSolid,
  [ButtonIconsEnum.MessageTextCircleSolid]: ButtonIcons.MessageSquareSolid,
  [ButtonIconsEnum.MessageTextSquareSolid]: ButtonIcons.MessageTextSquareSolid,
  [ButtonIconsEnum.SendSolid]: ButtonIcons.SendSolid,
  [ButtonIconsEnum.MessageChatCircle]: ButtonIcons.MessageChatCircle,
  [ButtonIconsEnum.MessageChatSquare]: ButtonIcons.MessageChatSquare,
  [ButtonIconsEnum.MessageCircle]: ButtonIcons.MessageCircle,
  [ButtonIconsEnum.MessageDotsCircle]: ButtonIcons.MessageDotsCircle,
  [ButtonIconsEnum.MessageDotsSquare]: ButtonIcons.MessageDotsSquare,
  [ButtonIconsEnum.MessageSmileCircle]: ButtonIcons.MessageSmileCircle,
  [ButtonIconsEnum.MessageSmileSquare]: ButtonIcons.MessageSmileSquare,
  [ButtonIconsEnum.MessageSquare]: ButtonIcons.MessageSquare,
  [ButtonIconsEnum.MessageTextCircle]: ButtonIcons.MessageTextCircle,
  [ButtonIconsEnum.MessageTextSquare]: ButtonIcons.MessageTextSquare,
  [ButtonIconsEnum.Send]: ButtonIcons.Send,
};

export const ButtonIconsList = Object.keys(ButtonIcon).map((key) => ({
  name: key as ButtonIconsEnum,
  Icon: ButtonIcon[key] as typeof ButtonIcons.Send,
}));

export const AppsIcon = {
  [AppsEnum.Whatsapp]: AppIcons.WhatsApp,
  [AppsEnum.Messenger]: AppIcons.Messenger,
  [AppsEnum.Facebook]: AppIcons.Facebook,
  [AppsEnum.Instagram]: AppIcons.Instagram,
  [AppsEnum.LinkedIn]: AppIcons.LinkedIN,
  [AppsEnum.Twitter]: AppIcons.Twitter,
  [AppsEnum.Email]: AppIcons.Email,
  [AppsEnum.Phone]: AppIcons.Phone,
  [AppsEnum.SMS]: AppIcons.Messaging,
  [AppsEnum.Map]: AppIcons.Map,
  [AppsEnum.Telegram]: AppIcons.Telegram,
  [AppsEnum.TikTok]: AppIcons.TikTok,
  [AppsEnum.SnapChat]: AppIcons.Snapchat,
  [AppsEnum.Discord]: AppIcons.Discord,
  [AppsEnum.Slack]: AppIcons.Slack,
  [AppsEnum.Skype]: AppIcons.Skype,
  [AppsEnum.Pinterest]: AppIcons.Pinterest,
  [AppsEnum.Custom]: AppIcons.Custom,
};

export const AppsDefaultColor = {
  [AppsEnum.Custom]: "#101828",
  [AppsEnum.Discord]: "#5865F2",
  [AppsEnum.Email]: "#B42318",
  [AppsEnum.Facebook]: "#1877F2",
  [AppsEnum.Instagram]: "#F00073",
  [AppsEnum.LinkedIn]: "#0077B5",
  [AppsEnum.Map]: "#FC5555",
  [AppsEnum.Messenger]: "#0068FF",
  [AppsEnum.Phone]: "#29CC6A",
  [AppsEnum.Pinterest]: "#BD081C",
  [AppsEnum.Skype]: "#0078D7",
  [AppsEnum.SMS]: "#0086C9",
  [AppsEnum.Slack]: "#FFFFFF",
  [AppsEnum.SnapChat]: "#FFFC00",
  [AppsEnum.Telegram]: "#2AABEE",
  [AppsEnum.TikTok]: "#000000",
  [AppsEnum.Twitter]: "#000000",
  [AppsEnum.Whatsapp]: "#20B038",
};

export const AppIconsList = Object.keys(AppsIcon).map((key) => ({
  name: key as AppsEnum,
  Icon: AppsIcon[key],
}));

export const avatarImages = [
  "https://d2pi0n2fm836iz.cloudfront.net/491017/11262023131908656345cca4b87.jpg",
  "https://d2pi0n2fm836iz.cloudfront.net/491017/1126202313205765634639990ff.jpg",
];

export const AppsPlaceholder = {
  [AppsEnum.Custom]: "Custom link",
  [AppsEnum.Discord]: "Invite Link",
  [AppsEnum.Email]: "Email",
  [AppsEnum.Facebook]: "Facebook Page ID",
  [AppsEnum.Instagram]: "Username",
  [AppsEnum.LinkedIn]: "Linkedin Profile Page",
  [AppsEnum.Map]: "Google Maps URL",
  [AppsEnum.Messenger]: "Facebook Page ID",
  [AppsEnum.Phone]: "Phone Number",
  [AppsEnum.Pinterest]: "Pinterest Profile",
  [AppsEnum.Skype]: "Skype account",
  [AppsEnum.SMS]: "Phone Number",
  [AppsEnum.Slack]: "Slack Channel Link",
  [AppsEnum.SnapChat]: "Username",
  [AppsEnum.Telegram]: "Username",
  [AppsEnum.TikTok]: "Tiktok Account",
  [AppsEnum.Twitter]: "X Account",
  [AppsEnum.Whatsapp]: "Phone Number",
};

export const AppsInfo = {
  [AppsEnum.Custom]: "https://getbutton.io",
  [AppsEnum.Discord]: "discord.gg/kwPeQEdW",
  [AppsEnum.Email]: "<EMAIL>",
  [AppsEnum.Facebook]: "***************",
  [AppsEnum.Instagram]: "user_name",
  [AppsEnum.LinkedIn]: "linkedin.com/in/profile",
  [AppsEnum.Map]: "maps.app.goo.gl/XXXX",
  [AppsEnum.Messenger]: "***************",
  [AppsEnum.Phone]: "+1(800) 123-45-67",
  [AppsEnum.Pinterest]: "pinterest.com/pinterest",
  [AppsEnum.Skype]: "skype_account",
  [AppsEnum.SMS]: "Only for a mobile! \n +1(800) 123-45-67",
  [AppsEnum.Slack]: "org.slack.com/archives/GMXXDG2M7",
  [AppsEnum.SnapChat]: "teamsnapchat",
  [AppsEnum.Telegram]: "user_name",
  [AppsEnum.TikTok]: "company",
  [AppsEnum.Twitter]: "user_name",
  [AppsEnum.Whatsapp]: "+1(800) 123-45-67",
};

export const AvatarImages = {
  male: "https://d2pi0n2fm836iz.cloudfront.net/491017/11262023131908656345cca4b87.jpg",
  female:
    "https://d2pi0n2fm836iz.cloudfront.net/491017/1126202313205765634639990ff.jpg",
};
