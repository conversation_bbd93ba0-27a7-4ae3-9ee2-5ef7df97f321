import React, { forwardRef } from "react";
import styled from "styled-components";
import { Link, LinkProps } from "react-router-dom";
import { Global } from "../../common/types";

export interface UnstyledLinkProps extends Partial<LinkProps>, Global {
  asComponent?: any;
  style?: React.CSSProperties;
  external?: boolean;
  href?: string;
  className?: string;
}

const UnstyledLink: React.FC<UnstyledLinkProps> = forwardRef((props, ref) => {
  const {
    children,
    asComponent = props.href ? "a" : Link,
    external,
    dataTest,
    ...restProps
  } = props;
  return (
    <StyledUnstyledLink
      ref={ref}
      as={asComponent}
      data-test={dataTest}
      target={external ? "_blank" : undefined}
      {...restProps}
    >
      {children}
    </StyledUnstyledLink>
  );
});

export { UnstyledLink };

/**
 *
 * Styles
 *
 */

const StyledUnstyledLink = styled.a`
  color: inherit;
  text-decoration: none;
  display: inline-block;

  &:visited {
    color: inherit;
  }
  cursor: pointer;
`;
