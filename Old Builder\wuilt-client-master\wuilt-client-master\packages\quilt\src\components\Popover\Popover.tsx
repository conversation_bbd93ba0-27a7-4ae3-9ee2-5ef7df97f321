import React, { MutableRefObject, forwardRef } from "react";
import { Fill, Slot } from "../SlotFill";
import PopoverContentWrapper from "./ContentWrapper";
import { useStateWithTimeout } from "../../hooks/useStateWithTimeout";
import { POSITIONS, ALIGNS } from "./consts";

const SLOT_NAME = "popovers";

export interface PopoverProps {
  readonly target: MutableRefObject<any>;
  readonly children?: React.ReactNode;
  preferredPosition?: "top" | "bottom" | "";
  preferredAlign?: "start" | "end" | "center";
  dataTest?;
  opened?: boolean;
  onClose?: () => void;
  width?: number;
  noFocus?: boolean;
  overlapped?: boolean;
  fixed?: boolean;
}

const Popover = forwardRef<any, PopoverProps>(
  (
    {
      children,
      target: container,
      preferredPosition = POSITIONS.BOTTOM,
      preferredAlign = ALIGNS.START,
      dataTest,
      opened,
      onClose,
      noFocus,
      width,
      overlapped,
      fixed,
    },
    ref
  ) => {
    const transitionLength = 0;
    const [shown, setShown, setShownWithTimeout, clearShownTimeout] =
      useStateWithTimeout<boolean>(false, transitionLength);
    const [render, setRender, setRenderWithTimeout, clearRenderTimeout] =
      useStateWithTimeout<boolean>(false, transitionLength);

    React.useEffect(() => {
      if (typeof opened !== "undefined") {
        if (opened) {
          setRender(true);
          clearRenderTimeout();
          setShownWithTimeout(true);
        } else {
          setShown(false);
          clearShownTimeout();
          setRenderWithTimeout(false);
        }
      }
    }, [
      opened,
      clearRenderTimeout,
      clearShownTimeout,
      setRender,
      setShown,
      setShownWithTimeout,
      setRenderWithTimeout,
    ]);
    return render ? (
      <Fill name={SLOT_NAME}>
        <PopoverContentWrapper
          ref={ref}
          shown={shown}
          width={width}
          containerRef={container}
          preferredPosition={preferredPosition}
          preferredAlign={preferredAlign}
          onClose={onClose}
          dataTest={dataTest}
          overlapped={overlapped}
          fixed={fixed}
          noFocus={noFocus}
        >
          {children}
        </PopoverContentWrapper>
      </Fill>
    ) : null;
  }
);
Popover.displayName = "Popover";
export interface PopoverSlotProps {
	name?: string
	Wrapper?: any
}
const PopoverSlot = ({ name = SLOT_NAME, Wrapper = undefined }: PopoverSlotProps) => (
  <Slot name={name} Wrapper={Wrapper} />
);

export { Popover, PopoverSlot };
