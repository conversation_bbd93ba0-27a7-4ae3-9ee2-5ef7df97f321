import React from "react";

export const ModalContext = React.createContext({
  setHasModalSection: () => {},
  removeHasModalSection: () => {},
  callContextFunctions: () => {},
  hasModalSection: false,
  isMobileFullPage: false,
  isInsideModal: false,
  closable: false,
});

ModalContext.displayName = "ModalContext";

export const withModalContext = (Component) => (props) =>
  (
    <ModalContext.Consumer>
      {(contextProps) => <Component {...props} {...contextProps} />}
    </ModalContext.Consumer>
  );
