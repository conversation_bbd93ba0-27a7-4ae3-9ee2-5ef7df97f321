import React from "react";
import {
  Box,
  DragH<PERSON>le,
  Heading,
  InputField,
  Stack,
  VerticalSort,
  GearIcon,
  TrashDeleteBinIcon,
} from "@wuilt/quilt";
import { useWidget } from "../../../../context/widget-provider";
import {
  AppsIcon,
  AppsInfo,
  AppsPlaceholder,
} from "../../../../shared/IconsList";
import { Popover } from "../../ui";
import AppSetting from "../AppSetting";
import {
  AppsEnum,
  TAppSettings,
  TWhatsAppSettings,
  TWidgetBackground,
} from "../../../../shared/types";
import AddAgentButton from "../AddAgentButton";
import SingleAgent from "../SingleAgent";
import AppInfo from "./AppInfo";
import { useIntl } from "react-intl";

function SortApps() {
  const { settings, update } = useWidget();
  const { apps } = settings;
  const { locale } = useIntl();

  const handleSortApps = (value: TAppSettings[]) => {
    update({ apps: value });
  };

  const removeApp = (appName: AppsEnum) => {
    const index = settings.apps.findIndex((item) => item.name === appName);

    if (index !== -1) {
      apps.splice(index, 1);
    }
    update({ apps: apps });
  };

  const handleChangeSettings = (
    appName: AppsEnum,
    objKey: string,
    newValue: string | TWidgetBackground | TWhatsAppSettings
  ) => {
    const index = settings.apps.findIndex((item) => item.name === appName);
    const updatedAppsValue = [...settings.apps];
    updatedAppsValue[index] = {
      ...updatedAppsValue[index],
      [objKey]: newValue,
    };

    update({ apps: updatedAppsValue });
  };

  const updateAgents = (
    apps: TAppSettings[],
    agentIndex: number,
    key: string,
    value: string
  ) => {
    const whatsApp = apps.find((app) => app.name === AppsEnum.Whatsapp);
    if (
      whatsApp &&
      whatsApp.whatsAppSettings &&
      whatsApp.whatsAppSettings.agents
    ) {
      const whatsAppAgents = whatsApp.whatsAppSettings.agents;

      if (agentIndex >= 0 && agentIndex < whatsAppAgents.length) {
        whatsAppAgents[agentIndex] = {
          ...whatsAppAgents[agentIndex],
          [key]: value,
        };

        update({ apps: apps });
      }
    }
  };

  return (
    <Stack width="100%" flex="1.3 1">
      <VerticalSort
        useHandleOnly
        value={settings.apps || []}
        onChange={handleSortApps}
        uniqueFieldName="name"
      >
        {({ item: app }) => {
          const AppIcon = AppsIcon[app.name];
          return (
            <Box
              mb="10px"
              className="dnd-item"
              border="1px solid #EAECF0"
              borderRadius="10px"
              pl="0px"
              pr="12px"
              style={{
                background: "#F9FAFB",
                boxShadow: "0px 1px 2px 0px #1018280D",
              }}
            >
              <Stack
                align="center"
                height="100%"
                position="relative"
                pl="28px"
                py="10px"
              >
                <Stack
                  position="absolute"
                  top="0"
                  left="0"
                  borderRadius="10px 0px 0px 10px"
                  justify="center"
                  height="100%"
                  style={{ background: "#EAECF0" }}
                >
                  <DragHandle id={app.name} />
                </Stack>
                <Stack
                  tablet={{ direction: "column" }}
                  direction="row"
                  justify="between"
                  width="100%"
                >
                  <Stack
                    width="180px"
                    tablet={{ width: "100%" }}
                    direction="row"
                    align="center"
                    justify="between"
                  >
                    <Stack direction="row" align="center">
                      <Box
                        width="32px"
                        height="32px"
                        color="white"
                        borderRadius="100px"
                        style={{ background: app?.background?.color }}
                      >
                        <AppIcon
                          size={32}
                          messengerThunderColor={app?.background?.color}
                        />
                      </Box>
                      <Heading fontWeight="medium">{app.name}</Heading>
                    </Stack>
                    {app.name === AppsEnum.Whatsapp ? (
                      <Box display="none" tablet={{ display: "block" }}>
                        <AddAgentButton apps={settings.apps} update={update} />
                      </Box>
                    ) : null}
                  </Stack>
                  <Stack width="100%" direction="row" align="center">
                    <Stack
                      borderRadius="8px"
                      boxShadow="0px 1px 2px 0px #1018280D"
                      width="100%"
                    >
                      <InputField
                        borderRadius="8px"
                        height="40px"
                        type="text"
                        value={
                          app.name === AppsEnum.Whatsapp
                            ? app.whatsAppSettings?.agents[0].phone
                            : app?.value
                        }
                        suffix={<AppInfo info={AppsInfo[app.name]} />}
                        placeholder={AppsPlaceholder[app.name]}
                        onBlur={(e: any) => {
                          if (app.name === AppsEnum.Whatsapp) {
                            const firstAgentIndex = 0;
                            updateAgents(
                              apps,
                              firstAgentIndex,
                              "phone",
                              e.target.value
                            );
                          } else {
                            handleChangeSettings(
                              app.name,
                              "value",
                              e.target.value
                            );
                          }
                        }}
                      />
                    </Stack>
                    {app.name === AppsEnum.Whatsapp ? (
                      <Box tablet={{ display: "none" }}>
                        <AddAgentButton apps={settings.apps} update={update} />
                      </Box>
                    ) : null}
                    <Popover
                      translateX={locale.substring(0, 2) === "ar" ? 30 : 80}
                      closeButton
                      button={
                        <Stack
                          boxShadow="0px 1px 2px 0px #1018280D"
                          justify="center"
                          align="center"
                          border="1px solid #bac7d5"
                          borderRadius="8px"
                          bg="white"
                          height="40px"
                          width="40px"
                          cursor="pointer"
                        >
                          <GearIcon
                            viewBox="0 0 20 20"
                            size="md"
                            customColor="#667085"
                          />
                        </Stack>
                      }
                      content={
                        <AppSetting
                          updateAgents={updateAgents}
                          app={app}
                          handleChangeSettings={handleChangeSettings}
                        />
                      }
                    />
                    <Box>
                      <Stack
                        boxShadow="0px 1px 2px 0px #1018280D"
                        justify="center"
                        align="center"
                        border="1px solid #FDA29B"
                        borderRadius="8px"
                        bg="white"
                        height="40px"
                        width="40px"
                        cursor="pointer"
                        onClick={() => {
                          removeApp(app.name);
                        }}
                      >
                        <TrashDeleteBinIcon
                          viewBox="0 0 19 19"
                          size="md"
                          customColor="#D92D20"
                        />
                      </Stack>
                    </Box>
                  </Stack>
                </Stack>
                {app.name === AppsEnum.Whatsapp &&
                  app.whatsAppSettings?.agents
                    .slice(1)
                    .map((agent, index) => (
                      <SingleAgent
                        locale={locale}
                        updateAgents={updateAgents}
                        apps={apps}
                        agentIndex={index + 1}
                        update={update}
                        key={index}
                        agent={agent}
                      />
                    ))}
              </Stack>
            </Box>
          );
        }}
      </VerticalSort>
    </Stack>
  );
}

export default SortApps;
