import { UniqueIdentifier } from "@dnd-kit/core";
import React from "react";
import styled, { css } from "styled-components";
import { BoxProps } from "../Box";
import { SortableContext } from "./SortableContext";
import { SortableItem, SortableItemChildrenArgs } from "./SortableItem";

interface GridSortProps<Type> {
  value: Type[];
  onChange: (value: Type[], oldIndex: number, newIndex: number) => void;
  children: (args: SortableItemChildrenArgs<Type>) => React.ReactNode;
  useHandleOnly?: boolean;
  uniqueFieldName?: string;
  limitToContainerEdges?: boolean;
  className?: string;
  boxProps?: BoxProps;
  distance?: number;
  disableItem?: (id: UniqueIdentifier) => boolean;
  extraItem?: React.ReactNode;
  noOfColumns?: number;
  bigFirstTile?: boolean;
}

function GridSort<Type>(props: GridSortProps<Type>) {
  const {
    children,
    noOfColumns = 5,
    bigFirstTile = false,
    value = [],
    uniqueFieldName = "id",
    useHandleOnly = false,
    extraItem = null,
    disableItem = () => false,
  } = props;
  return (
    <StyledGridSort noOfColumns={noOfColumns} bigFirstTile={bigFirstTile}>
      <SortableContext
        limitToContainerEdges={false}
        grid
        distance={10}
        {...props}
      >
        {value.map((item, index) => (
          <SortableItem
            key={item[uniqueFieldName]}
            id={item[uniqueFieldName]}
            item={item}
            index={index}
            children={children}
            useHandleOnly={useHandleOnly}
            disabled={disableItem(item[uniqueFieldName])}
          />
        ))}
        {extraItem}
      </SortableContext>
    </StyledGridSort>
  );
}

export { GridSort };

/**
 *
 * Styles
 *
 */

const StyledGridSort = styled.div<{
  noOfColumns: number;
  bigFirstTile: boolean;
}>`
  display: grid;
  grid-gap: 0.8rem;
  grid-template-columns: ${({ noOfColumns }) => `repeat(${noOfColumns}, 1fr)`};
  ${({ bigFirstTile }) =>
    bigFirstTile &&
    css`
  & > :first-child {
			grid-column: 1 / span 2;
			grid-row: 1 / span 2;`}
`;
