import React from "react";
import { ComponentStory, ComponentMeta } from "@storybook/react";
import { GroupSort } from "../GroupSort";
import { Heading } from "../../Heading";
import { Stack } from "../../Stack";
import { Box } from "../../Box";
import { DragHandle } from "../DragHandle";
import { Checkbox } from "../../Checkbox";
import { Badge } from "../../Badge";

// https://storybook.js.org/docs/react/essentials/controls
export default {
  title: "Components/Sorting/Group",
  component: GroupSort,
  argTypes: {
    value: { control: false },
    uniqueFieldName: { control: false },
    className: { control: false },
    boxProps: { control: false },
  },
} as ComponentMeta<typeof GroupSort>;

const ITEMS = [
  { title: "item 1", id: "1" },
  { title: "item 2", id: "2" },
  { title: "item 3", id: "3" },
  { title: "item 4", id: "4" },
  { title: "item 5", id: "5" },
];

const Template: ComponentStory<typeof GroupSort> = ({
  limitToContainerEdges,
  useHandleOnly,
  ...restProps
}) => {
  const [items, setItems] = React.useState(ITEMS);
  const [selectedIds, setSelectedIds] = React.useState([]);

  const handleChange = (sortedItems) => {
    setItems(sortedItems);
  };

  const handleSelect = (id: string) => {
    if (selectedIds.includes(id)) {
      setSelectedIds((prev) => prev.filter((productId) => productId !== id));
    } else {
      setSelectedIds((prev) => [...prev, id]);
    }
  };

  return (
    <GroupSort
      {...restProps}
      limitToContainerEdges={limitToContainerEdges}
      // useHandleOnly={useHandleOnly}
      value={items}
      onChange={handleChange}
      selectedIds={selectedIds}
    >
      {({ item, isDragging }) => (
        <Box
          mb="10px"
          className="dnd-item"
          border="1px solid"
          borderColor="disabled"
          p="20px"
          bg="warning"
          onClick={() => handleSelect(item?.id)}
        >
          <Stack direction="row" align="center">
            <DragHandle id={item?.id} />
            <Checkbox
              value={selectedIds.includes(item?.id)}
              onChange={() => handleSelect(item?.id)}
            />
            <Heading>{item.title}</Heading>
            {isDragging && selectedIds.length > 1 && (
              <Badge noIcon type="secondary" label={selectedIds.length} />
            )}
          </Stack>
        </Box>
      )}
    </GroupSort>
  );
};

export const ListSorting = Template.bind({});
ListSorting.args = {
  useHandleOnly: false,
  limitToContainerEdges: true,
};
