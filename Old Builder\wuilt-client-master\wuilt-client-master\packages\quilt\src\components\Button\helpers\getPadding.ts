import { rtlSpacing } from "../../../utils/rtl";

const paddingOptions = {
  withoutText: "0",
  default: {
    small: `0 28px`,
    normal: `0 28px`,
    large: "0 28px",
  },
  withIcons: {
    small: `0 22px`,
    normal: `0 22px`,
    large: `0 22px`,
  },
  withLeftIcon: {
    small: `0 14px 0 14px`,
    normal: `0 26px 0 22px`,
    large: `0 26px 0 22px`,
  },
  withRightIcon: {
    small: `0 14px 0 14px`,
    normal: `0 22px 0 26px`,
    large: `0 22px 0 26px`,
  },
};

const getSpacing = (onlyIcon, iconRight, iconLeft, size, theme) => {
  const wrappedRtl = (value) => rtlSpacing(value)({ theme });
  if (onlyIcon) return wrappedRtl(paddingOptions.withoutText);

  if (iconLeft && iconRight) return wrappedRtl(paddingOptions.withIcons[size]);
  if (iconLeft && !iconRight) {
    return wrappedRtl(paddingOptions.withLeftIcon[size]);
  }
  if (!iconLeft && iconRight) {
    return wrappedRtl(paddingOptions.withRightIcon[size]);
  }
  return wrappedRtl(paddingOptions.default[size]);
};

export default getSpacing;
