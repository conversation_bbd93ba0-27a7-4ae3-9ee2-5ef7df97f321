import { useEffect, useState } from "react";
import { css } from "styled-components";
import { minimatch } from "minimatch";
import {
  AppsEnum,
  TAppSettings,
  TWhatsAppAgent,
  TWidgetSettings,
  WidgetPagesEnum,
  WidgetPositionEnum,
} from "../../shared/types";

const convertHexToRgba = (color: string, opacity: number) => {
  const removeHash = color.replace("#", "");
  const hex = removeHash.length === 3 ? removeHash + removeHash : removeHash;
  const red = parseInt(hex.substring(0, 2), 16);
  const green = parseInt(hex.substring(2, 4), 16);
  const blue = parseInt(hex.substring(4, 6), 16);
  return `rgba(${red}, ${green}, ${blue}, ${opacity})`;
};

export const BackgroundGradient =
  "linear-gradient(135deg, rgba(0, 0, 0, 0) 0%, rgba(255, 255, 255, 0.4) 100%)";

function shouldNotRender(settings: TWidgetSettings, href: string) {
  if (
    // Check if no apps selected
    !settings.apps.length ||
    // Check on selected devices
    (!settings.appearance.display.showOnMobile && window.innerWidth < 768) ||
    (!settings.appearance.display.showOnDesktop && window.innerWidth > 768) ||
    // Check on selected pages
    (settings.appearance.display.pages.type === WidgetPagesEnum.SelectedPages &&
      !settings.appearance.display.pages.displayOn.some((urlMatch) => {
        return minimatch(href, urlMatch);
      })) ||
    // Check on hidden pages
    (settings.appearance.display.pages.type === WidgetPagesEnum.AllPages &&
      settings.appearance.display.pages.hideOn.some((urlMatch) => {
        return minimatch(href, urlMatch);
      }))
  ) {
    return true;
  }
  return false;
}

export const useShouldRender = (settings: TWidgetSettings) => {
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    require("url-change-event");
    const handleUrlChange = (event?: any) => {
      const href = event?.newURL?.href || window.location.href;
      setShouldRender(!shouldNotRender(settings, href));
    };

    window.addEventListener("urlchangeevent", handleUrlChange);
    handleUrlChange();

    return () => {
      window.removeEventListener("urlchangeevent", handleUrlChange);
    };
  }, [settings]);

  return shouldRender;
};

export const WidgetAppStyles = ({
  settings,
}: {
  settings: TWidgetSettings;
}) => {
  const MARGIN = "16px";
  const isLeftSide =
    settings.appearance.display.position === WidgetPositionEnum.Left;
  const backgroundColor = settings.appearance.style.background.color;
  const isGradient = settings.appearance.style.background.gradient;
  const size = settings.appearance.style.size;
  const withText = settings.appearance.content.withText;
  const borderColor = settings.appearance.style.border?.color;
  const borderThickness = settings.appearance.style.border?.thickness;
  return css`
    cursor: pointer;
    display: grid;
    place-items: center;
    flex-shrink: 0;
    width: ${withText ? `calc(${size} * 3)` : size};
    height: ${size};
    border-radius: ${settings.appearance.style.radius};
    background: ${isGradient && `${BackgroundGradient},`} ${backgroundColor};
    border: ${borderColor ? `${borderColor} solid ${borderThickness}` : "none"};
    box-shadow: 0px 4px 6px -2px ${convertHexToRgba(settings.appearance.style.shadow?.color!, settings.appearance.style.shadow?.opacity!)};
    margin-top: ${MARGIN};
    margin-left: ${isLeftSide ? "0" : MARGIN};
    margin-right: ${!isLeftSide ? "0" : MARGIN};
    text-decoration: none;
  `;
};

export const CloseIcon = ({ size }: { size: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 20 17"
    fill="currentColor"
  >
    <path
      fillRule="evenodd"
      d="M11.414 10l4.293-4.293c.391-.391.391-1.023 0-1.414-.39-.391-1.023-.391-1.414 0L10 8.586 5.707 4.293c-.39-.391-1.023-.391-1.414 0-.39.391-.39 1.023 0 1.414L8.586 10l-4.293 4.293c-.39.391-.39 1.023 0 1.414.195.195.451.293.707.293.256 0 .512-.098.707-.293L10 11.414l4.293 4.293c.195.195.451.293.707.293.256 0 .512-.098.707-.293.391-.391.391-1.023 0-1.414L11.414 10z"
    />
  </svg>
);

export const getWhatsappAgentRedirect = (agent: TWhatsAppAgent | undefined) => {
  return `https://wa.me/${agent?.phone}?text=${agent?.message}`;
};

export const getAppRedirect = (app: TAppSettings) => {
  let url = "";

  switch (app.name) {
    case AppsEnum.Whatsapp:
      url = getWhatsappAgentRedirect(app?.whatsAppSettings?.agents?.[0]);
      break;

    case AppsEnum.Instagram:
      url = `https://www.instagram.com/${app.value}`;
      break;

    case AppsEnum.Messenger:
      url = `https://www.messenger.com/t/${app.value}`;
      break;

    case AppsEnum.Facebook:
      url = `https://www.facebook.com/${app.value}`;
      break;

    case AppsEnum.Telegram:
      url = `https://t.me/${app.value}`;
      break;

    case AppsEnum.LinkedIn:
      url = `https://www.linkedin.com/in/${app.value}`;
      break;

    case AppsEnum.SnapChat:
      url = `https://www.snapchat.com/add/${app.value}`;
      break;

    case AppsEnum.TikTok:
      url = `https://www.tiktok.com/${
        app.value.startsWith("@") ? app.value : `@${app.value}`
      }`;
      break;

    case AppsEnum.Custom:
      url = app.value;
      break;

    case AppsEnum.Discord:
      url = `https://discord.gg/${app.value}`;
      break;

    case AppsEnum.Email:
      url = `mailto:${app.value}`;
      break;

    case AppsEnum.SMS:
      url = `sms:${app.value}`;
      break;

    case AppsEnum.Phone:
      url = `tel:${app.value}`;
      break;

    case AppsEnum.Map:
      url = app.value;
      break;

    case AppsEnum.Slack:
      url = `https://${app.value}.slack.com/`;
      break;

    case AppsEnum.Pinterest:
      url = `https://www.pinterest.com/${app.value}`;
      break;

    case AppsEnum.Skype:
      url = `skype:${app.value}?chat`;
      break;

    case AppsEnum.Twitter:
      url = `https://twitter.com/${app.value}`;
      break;

    default:
      url = app.value;
      break;
  }

  if (app.value.startsWith("http")) {
    url = app.value;
  }

  return url;
};
