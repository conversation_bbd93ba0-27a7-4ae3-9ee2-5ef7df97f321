import { DomainIcon, Select } from "@wuilt/quilt";
import { SUPPORTED_LOCALES, useLocale, Locale } from "./local-provider";
import styled from "styled-components";
import { components } from "react-select";

const OPTIONS = Object.values(SUPPORTED_LOCALES).map((locale) => ({
  label: locale.display,
  value: locale.code,
}));

const StyledSelectWrapper = styled.div`
  input {
    right: -100px;
  }
`;
const Control: React.FC<any> = ({ children, ...props }) => {
  return (
    <components.Control {...props}>
      <div style={{ marginInlineStart: 9, marginInlineEnd: -9 }}>
        <DomainIcon size="xl" />
      </div>
      {children}
    </components.Control>
  );
};

export const LanguageSwitcher = () => {
  const { locale, setLocale } = useLocale();

  const handleChange = (locale: Locale) => {
    setLocale(locale);
  };
  return (
    <StyledSelectWrapper>
      <Select
        defaultValue={OPTIONS.find((option) => option.value === locale.code)}
        options={OPTIONS}
        onChange={(e: any) => handleChange(SUPPORTED_LOCALES[e.value])}
        components={{ Control }}
        isSearchable={false}
        isClearable={false}
        hideSelectedOptions
      />
    </StyledSelectWrapper>
  );
};
