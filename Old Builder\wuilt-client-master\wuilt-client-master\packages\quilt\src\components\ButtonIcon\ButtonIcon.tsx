import React from "react";
import styled, { css } from "styled-components";
import { Button, ButtonProps } from "../Button";

const sizes = {
  small: 6,
  medium: 8,
  large: 10,
};

export interface ButtonIconProps extends Omit<ButtonProps, "size"> {
  size?: keyof typeof sizes;
  onlyIcon?: boolean;
  rounded?: boolean;
  stopOpacity?: boolean;
  className?: string;
  borderRadius?: string;
}

const ButtonIcon: React.FC<ButtonIconProps> = React.forwardRef(
  (
    {
      children,
      color,
      squared = true,
      size = "large",
      onlyIcon,
      rounded,
      stopOpacity,
      borderRadius,
      ...restProps
    },
    ref
  ) => {
    return (
      <StyledButton
        ref={ref}
        color={color}
        squared={squared}
        rounded={rounded || false}
        iconsize={sizes[size]}
        onlyIcon={onlyIcon || false}
        stopOpacity={stopOpacity || false}
        borderRadius={borderRadius || ""}
        {...restProps}
      >
        <span>{children}</span>
      </StyledButton>
    );
  }
);

ButtonIcon.displayName = "ButtonIcon";
export { ButtonIcon };

const OnlyIconStyle = css`
  border: none;
  background: none;
  background-image: none;
  box-shadow: none;
  padding: 0;
  height: auto;
  width: auto;
  &:hover,
  &:active,
  &:focus {
    background: none;
    border: none;
    background-image: none;
    box-shadow: none;
  }
`;
const TransparentStyle = css`
  box-shadow: none;
  background: transparent;
  &:hover,
  &:active,
  &:focus {
    background: none;
    border: none;
    background-image: none;
    box-shadow: none;
  }
`;

const WhiteButtonStyle = css`
  background: #ffffff;
  border: 1px solid #eaecf0;
  /* box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08),
    0px 4px 6px -2px rgba(16, 24, 40, 0.03); */
`;

const ClearStyles = css`
  background: none;
  border: none;
  background-image: none;
  box-shadow: none;

  &:hover,
  &:active,
  &:focus {
    background: none;
    border: none;
    background-image: none;
    box-shadow: none;
  }
`;

const RoundedStyles = css`
  flex-shrink: 0;
  border: none;
  border-radius: 50%;
  height: auto;
  width: auto;
`;

const BorderRadiusStyle = css<{ borderRadius: string }>`
  border-radius: ${({ borderRadius }) => borderRadius};
`;

const DefaultStyles = css`
  ${ClearStyles}
  color: ${({ theme }) => theme.palette.ink.normal} !important;
  background: #f7f7f7;
`;

const StyledButton = styled(Button)<{
  iconsize: number;
  onlyIcon: boolean;
  rounded: boolean;
  stopOpacity: boolean;
  borderRadius: string;
}>`
  height: fit-content;
  ${({ color }) => !color && DefaultStyles};
  box-shadow: ${({ theme }) => theme.base.boxShadow.xxs};
  padding: ${({ iconsize }) => `${iconsize - 2}px ${iconsize}px`};
  ${({ color }) => color === "white" && WhiteButtonStyle};

  & span {
    ${({ stopOpacity }) =>
      !stopOpacity &&
      css`
        opacity: 0.5;
      `}
  }

  &:active {
    transform: scale(0.95);
  }

  &:hover {
    span {
      opacity: 1;
    }
  }

  &:hover,
  &:active,
  &:focus {
    box-shadow: ${({ theme }) => theme.base.boxShadow.xxs};
  }

  ${({ rounded }) => rounded && RoundedStyles}
  ${({ transparent }) => transparent && TransparentStyle}
  ${({ onlyIcon }) => onlyIcon && OnlyIconStyle}
  ${({ borderRadius }) => borderRadius && BorderRadiusStyle}
`;
