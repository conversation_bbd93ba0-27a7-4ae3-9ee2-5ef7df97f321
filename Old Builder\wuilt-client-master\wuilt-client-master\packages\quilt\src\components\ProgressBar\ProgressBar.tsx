import React from "react";
import styled from "styled-components";
import { Size } from "../../common/types";

export interface Props {
  count: number;
  completed: number;
  text?: string;
  size?: Size;
}

const sizeHight = (props) => {
  const sizeToHight = {
    small: "xxs",
    normal: "xs",
    large: "sm",
  };
  return props.theme.base.space[sizeToHight[props.size || "normal"]];
};

const ProgressContainer = styled.div<{ size?: Size }>`
  height: ${sizeHight};
  background-color: #e8e9ea;
  border-radius: 50px;
`;

const Bar = styled.div<{ count: number; completed }>`
  height: 100%;
  width: ${(props) => `${(props.completed / props.count) * 100}%`};
  background-color: ${(props) => `${props.theme.palette.product.normal}`};
  border-radius: inherit;
`;

const ProgressHeading = styled.p`
  font-size: ${(props) => `${props.theme.base.fontSize.md}`};
  font-family: ${(props) => `${props.theme.base.fontFamily}`};
  font-weight: ${(props) => props.theme.base.fontWeight.semiBold};
  color: ${(props) => props.theme.palette.ink.light};
  margin: 0 0 6px 0;

  > span {
    font-size: 12px;
  }
`;

const ProgressBar: React.FC<Props> = (props) => {
  const { text, size } = props;
  const count = props.count || 100;
  const completed = props.completed || 0;
  return (
    <>
      <ProgressHeading>
        {completed}/<span>{count}</span> {text}
      </ProgressHeading>
      <ProgressContainer size={size}>
        <Bar count={count} completed={completed} />
      </ProgressContainer>
    </>
  );
};

ProgressBar.displayName = "ProgressBar";
export { ProgressBar };
