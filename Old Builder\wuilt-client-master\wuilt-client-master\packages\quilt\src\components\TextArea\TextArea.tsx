import React, { CSSProperties } from "react";
import styled from "styled-components";
import { GeneralInputWrapper } from "../InputField/InputField";
import { Global } from "../../common/types";

export interface TextAreaProps
  extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>,'value'>,
    Global {
  isDisabled?: boolean;
  isError?: boolean;
  value?: string | null;
  resize?: CSSProperties["resize"];
}

const TextArea = React.forwardRef<HTMLTextAreaElement, TextAreaProps>(
  (props, ref) => {
    const { children, className, isDisabled, isError, dataTest, ...restProps } =
      props;
    return (
      <GeneralInputWrapper
        as={StyledTextArea}
        className={className}
        {...restProps}
        ref={ref}
        isDisabled={isDisabled || undefined}
        isError={isError}
        data-test={dataTest}
      >
        {children}
      </GeneralInputWrapper>
    );
  }
);
TextArea.displayName = "TextArea";
export { TextArea };

/**
 *
 *
 * Styles
 *
 *
 */

const StyledTextArea = styled.textarea<{
  disabled;
  resize: CSSProperties["resize"];
}>`
  box-sizing: border-box;
  line-height: 24px;
  outline: none;
  color: inherit;
  font-family: inherit;
  width: 100%;
  color: ${({ theme }) => theme.palette.ink.normal};
  padding: 8px 12px;
  min-height: 80px;

  resize: ${({ resize }) => resize && resize};

  &::placeholder {
    color: ${({ theme }) => theme.palette.ink.light};
    opacity: 0.5;
  }
  &::-webkit-input-placeholder {
    color: ${({ theme }) => theme.palette.ink.light};
    opacity: 0.5;
  }
  &:-moz-placeholder {
    color: ${({ theme }) => theme.palette.ink.light};
    opacity: 0.5;
  }
  &:-ms-input-placeholder {
    color: ${({ theme }) => theme.palette.ink.light};
    opacity: 0.5;
  }
`;
