import React from "react";
import styled from "styled-components";
import { UnstyledLink, UnstyledLinkProps } from "../UnstyledLink";
import { left, right } from "../../utils";
import { Global } from "../../common/types";
import { base } from "../../themes/original";

const ALIGNMENT = {
  start: left,
  center: "center",
  end: right,
};

const palette = base.colors;

type VerticalAlignType = "top" | "middle" | "bottom";

export interface RowCell {
  content: string | JSX.Element;
  colSpan?: number;
  rowSpan?: number;
  linkTo?: string | "UN_LINK";
  linkProps?: UnstyledLinkProps;
  dataTest?: string;
  width?: string;
  align?: keyof typeof ALIGNMENT;
}
export interface Row {
  cells: RowCell[];
  bgColor?: keyof typeof palette;
  linkTo?: string;
  onClick?: () => void;
  linkProps?: UnstyledLinkProps;
  dataTest?: string;
}
export interface HeaderCell {
  content: React.ReactNode;
  colSpan?: number;
  rowSpan?: number;
  width?: string;
  align?: keyof typeof ALIGNMENT;
  verticalAlign?: VerticalAlignType;
}

export interface Header {
  cells: HeaderCell[];
  bgColor?: keyof typeof palette;
  customBgColor?: string;
}

export interface DataTableProps extends Global {
  rows: Row[];
  header: Header;
}

const DataTable: React.FC<DataTableProps> = ({
  rows = [],
  header = { cells: [] },
  dataTest,
}) => {
  return (
    <StyledTable data-test={dataTest}>
      <StyledTableHeader
        bgColor={header.bgColor}
        customBgColor={header?.customBgColor}
      >
        <tr>
          {header.cells.map(({ content, ...cellProps }, index) =>
            content ? (
              <StyledTableHeaderCell {...cellProps} key={`header-${index}`}>
                {content}
              </StyledTableHeaderCell>
            ) : (
              <th key={`header-${index}`} />
            )
          )}
        </tr>
      </StyledTableHeader>
      <StyledTableBody>
        {rows.map((row, index) => (
          <StyledTableRow
            data-test={row.dataTest}
            key={`row-${index}`}
            bgColor={row.bgColor}
            onClick={row?.onClick}
          >
            {row.cells.map((cell, index) => (
              <StyledTableCell
                key={`cell-${index}`}
                data-test={cell.dataTest}
                colSpan={cell.colSpan}
                rowSpan={cell.rowSpan}
                width={cell.width}
                align={cell.align}
                asComponent={
                  !cell.linkTo && !row.linkTo
                    ? undefined
                    : cell.linkTo === "UN_LINK"
                    ? undefined
                    : UnstyledLink
                }
                to={cell.linkTo || row.linkTo}
                linkProps={cell?.linkProps || row?.linkProps}
                onClick={row?.onClick}
              >
                {cell.content}
              </StyledTableCell>
            ))}
          </StyledTableRow>
        ))}
      </StyledTableBody>
    </StyledTable>
  );
};

export { DataTable };

const StyledTable = styled.table`
  background: #fff;
  border-spacing: 0;
  border-collapse: collapse;
  width: 100%;
  border-radius: 6px;
  font-size: 14px;
`;

const StyledTableHeader = styled.thead<{
  bgColor?: keyof typeof palette;
  customBgColor?: string;
}>`
  border-bottom: 1px solid #dfe3e8;
  background-color: ${({ bgColor, customBgColor }) =>
    `${(bgColor ? palette[bgColor] : customBgColor) || "white"}`};
`;

const StyledTableHeaderCell = styled.th<{
  align?;
  verticalAlign?: VerticalAlignType;
}>`
  font-weight: ${({ theme }) => theme.base.fontWeight.semiBold};
  color: ${({ theme }) => theme.palette.ink.light};
  padding: 15px 12px;
  text-align: ${({ align }) => align || left};
  vertical-align: ${({ verticalAlign }) => verticalAlign};
`;

const StyledTableBody = styled.tbody``;

const StyledTableRow = styled.tr<{ bgColor?: string; onClick?: () => void }>`
  border-top: 1px solid #dfe3e8;
  height: 100%;
  background-color: ${({ bgColor }) => `${palette[bgColor || "white"]}`};
  &:hover {
    filter: contrast(0.9);
  }
  cursor: ${({ onClick }) => (onClick ? "pointer" : "default")};
`;

const StyledTableCell = styled(
  ({ asComponent: Component = "td", linkProps, to, ...rest }) => {
    const linkTo =
      typeof linkProps?.to === "object"
        ? { ...linkProps?.to, pathname: linkProps?.to?.pathname || to }
        : undefined;

    return Component === "td" ? (
      <Component {...rest} />
    ) : (
      <StyledTableLinkCell>
        <Component {...rest} {...linkProps} to={linkTo || to} />
      </StyledTableLinkCell>
    );
  }
)`
  text-align: ${({ align }) => align || left};
  color: ${({ theme }) => theme.palette.ink.light};
  padding: 12px 12px;
`;

const StyledTableLinkCell = styled.td`
  a {
    width: 100%;
    height: 100%;
    display: inline-block;
    box-sizing: border-box;
  }
`;
