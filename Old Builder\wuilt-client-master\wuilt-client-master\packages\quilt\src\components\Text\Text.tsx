import React from "react";
import styled, { css } from "styled-components";
import { layout, LayoutProps } from "styled-system";
import {
  Color,
  ColorProps,
  Typography,
  TypographyProps,
  TextAlignmentCss,
  TextAlignmentProps,
  Space,
  SpaceProps,
  TextAlignmentFn,
} from "../../themes/property-overriding";
import { Global } from "../../common/types";

const fontSizes = {
  xsmall: "xxxs",
  small: "xs",
  medium: "sm",
  large: "lg",
  xlarge: "xl",
} as const;
export interface TextStyleProps
  extends Global,
    TextAlignmentProps,
    ColorProps,
    SpaceProps,
    Omit<TypographyProps, "fontSize">,
    LayoutProps {
  fontSize?: keyof typeof fontSizes | TypographyProps["fontSize"];
  style?: React.CSSProperties;
  wordBreak?: React.CSSProperties["wordBreak"];
  whiteSpace?: React.CSSProperties["whiteSpace"];
  lineHeight?: React.CSSProperties["lineHeight"];
  letterSpacing?: React.CSSProperties["letterSpacing"];
}

export interface TextProps extends TextStyleProps {
  className?: string;
  onHover?: TextStyleProps;
  children: React.ReactNode;
}

export const Text: React.FC<TextProps> = ({
  fontSize = "small",
  color = "info",
  fontWeight = "normal",
  align = "left",
  ...restProps
}) => (
  <StyledText
    fontSize={fontSizes[fontSize] || fontSize}
    color={color}
    fontWeight={fontWeight}
    align={align}
    {...restProps}
  />
);

/**
 *
 * styles *
 *
 */

const onHoverStyles = ({ onHover, theme }) => {
  const cssProps = { ...onHover, theme };
  return css`
    ${Color(cssProps)};
    ${TextAlignmentFn(cssProps)}
    ${Typography(cssProps)}
		${Space(cssProps)}
		${layout(cssProps)}
  `;
};

const StyledText = styled(({ children, className, style, dataTest }) => (
  <p className={className} style={style} data-test={dataTest}>
    {children}
  </p>
))`
  padding: 0;
  margin: 0;
  ${Color};
  ${TextAlignmentCss}
  ${Typography};
  ${Space}
  ${layout}
	&:hover, &:active {
    ${onHoverStyles}
  }
  ${({ wordBreak }) => wordBreak && `word-break:${wordBreak}`}
  ${({ lineHeight }) => lineHeight && `line-height:${lineHeight}`}
  ${({ whiteSpace }) => whiteSpace && `white-space:${whiteSpace}`}
  ${({ letterSpacing }) => letterSpacing && `letter-spacing:${letterSpacing}`}
`;
