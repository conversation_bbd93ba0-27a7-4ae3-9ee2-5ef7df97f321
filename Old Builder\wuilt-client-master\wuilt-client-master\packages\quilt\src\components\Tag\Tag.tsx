import React from "react";
import styled from "styled-components";
import { CloseIcon } from "../icons";
import { Event } from "../../common/types";
import { Stack } from "../Stack/Stack";

export interface Props {
  text: string;
  onRemove?: Event<any>;
}

const StyledTag = styled.div`
  border-radius: 14px;
  background-color: #dfe3e8;
  color: #212b36;
  padding: 4px 10px;
  width: fit-content;
`;

const StyledClose = styled.div`
  cursor: pointer;
  align-items: center;
  display: flex;
`;

const Tag: React.FC<Props> = ({ text, onRemove }) => {
  return (
    <StyledTag>
      <Stack direction="row" spacing="tight" align="center">
        <span>{text}</span>
        {onRemove && (
          <StyledClose onClick={onRemove}>
            <CloseIcon size="sm" />
          </StyledClose>
        )}
      </Stack>
    </StyledTag>
  );
};

export { Tag };
