// import { Box, BoxProps } from "@wuilt/quilt";
// import * as React from "react";
import styled from "styled-components";

// export const Container: React.FC<BoxProps> = (props) => (
//   <Box maxWidth="1250px" margin="0 auto" {...props}>
//     {props.children}
//   </Box>
// );
// styles
export const Container = styled.div`
  margin: 0 auto;
  padding: 32px 10px;
  width: 100%;
  max-width: 1200px;
`;
