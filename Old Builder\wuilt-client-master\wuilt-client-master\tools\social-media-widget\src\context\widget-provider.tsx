import { createContext, useContext, useMemo, useState } from "react";
import {
  AnimationEnum,
  AppsEnum,
  ButtonIconsEnum,
  TWidgetSettings,
  WidgetOrientationEnum,
  WidgetPagesEnum,
  WidgetPositionEnum,
} from "../shared/types";
import { AppsDefaultColor, AvatarImages } from "../shared/IconsList";

const getLocale = () => {
  const query = new URLSearchParams(window.location.search);
  const localeQuery = query.get("locale") || "en-US";
  const locale = localeQuery.substring(0, 2);
  return locale;
};

const initialValue: TWidgetSettings = {
  apps: [
    {
      name: AppsEnum.Whatsapp,
      value: "",
      onHoverText: AppsEnum.Whatsapp,
      background: {
        color: AppsDefaultColor[AppsEnum.Whatsapp],
        gradient: false,
      },
      whatsAppSettings: {
        agents: [
          {
            name: "Agent 1",
            position: "",
            message: "",
            image: AvatarImages.male,
            phone: "",
          },
        ],
        form: {
          title: "Start a Conversation",
          subtitle: "Click one of our members below to chat",
        },
      },
    },
  ],
  appearance: {
    content: {
      closeText: "Close",
      openText: "Open",
      withText: false,
      icon: ButtonIconsEnum.MessageChatCircleSolid,
    },
    display: {
      showOnMobile: true,
      showOnDesktop: true,
      position:
        getLocale() === "ar"
          ? WidgetPositionEnum.Left
          : WidgetPositionEnum.Right,
      orientation: WidgetOrientationEnum.Vertical,
      shift: { horizontal: "10px", vertical: "10px" },
      pages: {
        type: WidgetPagesEnum.AllPages,
        displayOn: [],
        hideOn: [],
      },
    },
    style: {
      size: "50px",
      radius: "60px",
      animation: AnimationEnum.None,
      shadow: {
        color: "#101828",
        opacity: 0.7,
      },
      background: {
        color: "#2e90fa",
        gradient: false,
      },
    },
  },
};

export type TWidgetContext = {
  settings: TWidgetSettings;
  update: (settings: Partial<TWidgetSettings>) => void;
};

const Context = createContext<TWidgetContext>({
  settings: initialValue,
  update: () => {},
});

export const WidgetProvider = ({ children }) => {
  const [state, setState] = useState<TWidgetSettings>(initialValue);

  const value = useMemo<TWidgetContext>(() => {
    return {
      settings: state,
      update(settings: Partial<TWidgetSettings>) {
        setState((prev) => ({
          appearance: {
            content: {
              ...prev.appearance.content,
              ...settings.appearance?.content,
            },
            display: {
              ...prev.appearance.display,
              ...settings.appearance?.display,
            },
            style: {
              ...prev.appearance.style,
              ...settings.appearance?.style,
            },
          },
          apps: [...(settings?.apps || prev.apps)],
        }));
      },
    };
  }, [state]);

  return (
    <Context.Provider value={value}>
      {typeof children === "function" ? children(state) : children}
    </Context.Provider>
  );
};

export const useWidget = () => useContext(Context);
