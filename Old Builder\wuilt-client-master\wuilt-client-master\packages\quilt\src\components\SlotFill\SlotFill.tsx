// TODO: revert iframe portals
// import Fill from "./fill";
// import Slot from "./slot";

// export { Fill, Slot };

import React from "react";
import ReactDOM from "react-dom";

export interface FillProps {
  children: any;
  name: string;
}

export const Fill: React.FC<FillProps> = ({ children, name }) => {
  if (typeof document === "undefined") return children;
  const domNode = document.getElementById(name);
  if (domNode == null) return children;
  return ReactDOM.createPortal(children, domNode);
};

export interface SlotProps {
  name: string;
  Wrapper?: any;
}

export const Slot: React.FC<SlotProps> = ({ name }) => {
  return <div id={name} />;
};
