import { CircleButtonIcon, Stack, Text } from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import {
  TWidgetAppearance,
  TWidgetSettings,
} from "../../../../../shared/types";
import ButtonWithTextIcon from "./ButtonWithTextIcon";
interface StyleTabsProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function StyleTabs({ appearance, update }: StyleTabsProps) {
  const {
    content: { withText },
  } = appearance;
  return (
    <Stack mt="16px" direction="row" width="100%">
      <Stack
        cursor="pointer"
        direction="row"
        borderRadius="8px"
        height="46px"
        align="center"
        justify="center"
        boxShadow="0px 1px 2px 0px #1018280D"
        width="104px"
        spacing="condensed"
        border={withText ? "1px solid  #D0D5DD" : "1px solid #0E9384"}
        style={{ backgroundColor: withText ? "" : "#F6FEFC" }}
        onClick={() => {
          update({
            appearance: {
              ...appearance,
              content: { ...appearance.content, withText: false },
            },
          });
        }}
      >
        <CircleButtonIcon className="style-btn-icon" color="transparent" />
        <Text
          color={withText ? "black" : "primary"}
          fontSize="sm"
          fontWeight="semiBold"
        >
          <FormattedMessage defaultMessage="Circle" id="vH8sCb" />
        </Text>
      </Stack>
      <Stack
        onClick={() => {
          update({
            appearance: {
              ...appearance,
              content: { ...appearance.content, withText: true },
            },
          });
        }}
        cursor="pointer"
        direction="row"
        borderRadius="8px"
        align="center"
        justify="center"
        height="46px"
        width="198px"
        spacing="condensed"
        boxShadow="0px 1px 2px 0px #1018280D"
        border={withText ? "1px solid #0E9384" : "1px solid #D0D5DD"}
        style={{ backgroundColor: withText ? "#F6FEFC" : "" }}
      >
        <ButtonWithTextIcon />
        <Text
          color={withText ? "primary" : "black"}
          fontSize="sm"
          fontWeight="semiBold"
        >
          <FormattedMessage defaultMessage="Button + Text" id="tPgKAw" />
        </Text>
      </Stack>
    </Stack>
  );
}

export default StyleTabs;
