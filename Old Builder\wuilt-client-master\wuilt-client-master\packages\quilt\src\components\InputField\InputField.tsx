/* eslint-disable react/no-unused-prop-types */
import React, { CSSProperties } from "react";
import styled, { css } from "styled-components";
import { DebounceInput } from "react-debounce-input";
import { Event, Global } from "../../common/types";
import { ArrowDownFilledIcon, ArrowUpFilledIcon } from "../icons";
import { color, mediaQueries } from "../../utils";

type Type =
  | "text"
  | "number"
  | "email"
  | "password"
  | "passportid"
  | "tel"
  | "time";
type InputModeType =
  | "text"
  | "email"
  | "tel"
  | "none"
  | "url"
  | "numeric"
  | "decimal"
  | "search";
type InputEvent = Event<React.SyntheticEvent<HTMLInputElement>>;
type KeyboardEvent = Event<React.KeyboardEvent<HTMLInputElement>>;
type ClipboardEvent = Event<React.ClipboardEvent<HTMLInputElement>>;

export interface InputFieldProps extends Global {
  // readonly size?: Common.InputSize;
  readonly type?: Type;
  readonly name?: string;
  readonly title?: string;
  readonly label?: string; // Common.Translation;
  readonly inlineLabel?: boolean;
  readonly value?: string | number | null; // | (() => string | number);
  readonly placeholder?: string; // Common.Translation;
  readonly prefix?: React.ReactNode;
  readonly prefixBorder?: boolean;
  readonly suffix?: React.ReactNode;
  readonly suffixBorder?: boolean;
  readonly help?: React.ReactNode;
  readonly error?: React.ReactNode;
  readonly tags?: React.ReactNode;
  readonly isDisabled?: boolean;
  readonly isError?: boolean;
  readonly backgroundColor?: string;
  readonly maxValue?: number;
  readonly minValue?: number;
  readonly maxLength?: number;
  readonly minLength?: number;
  readonly isRequired?: boolean;
  readonly tabIndex?: number;
  readonly noValidate?: boolean;
  readonly autoFocus?: boolean;
  readonly readOnly?: boolean;
  readonly autoComplete?: string;
  readonly id?: string;
  readonly inputMode?: InputModeType;
  readonly pattern?: string;
  readonly height?: string;
  readonly width?: string;
  readonly minWidth?: string;
  readonly noFraction?: boolean;
  readonly loading?: boolean;
  readonly hideArrows?: boolean;
  readonly debounce?: number;
  readonly margin?: CSSProperties["margin"];
  readonly borderRadius?: CSSProperties["borderRadius"];
  readonly onChange?: Event<React.ChangeEvent<HTMLInputElement>>;
  readonly onFocus?: InputEvent;
  readonly onBlur?: InputEvent;
  readonly onSelect?: InputEvent;
  readonly onMouseUp?: InputEvent;
  readonly onMouseDown?: InputEvent;
  readonly onKeyDown?: KeyboardEvent;
  readonly onKeyUp?: KeyboardEvent;
  readonly onPaste?: ClipboardEvent;
  readonly handleArrowUpClick?: (value: React.ReactText) => void;
  readonly handleArrowDownClick?: (value: React.ReactText) => void;
}

const Field = styled(
  ({
    component: Component,
    className,
    children,
    spaceAfter,
    theme,
    width,
    ...props
  }) => {
    return (
      <Component className={className} {...props}>
        {children}
      </Component>
    );
  }
)`
  display: block;
  flex: 1 1 100%;
  width: 100%;
`;

export const GeneralInputWrapper = styled.div<{
  isDisabled?: boolean;
  backgroundColor?: string;
  isError?: boolean;
  width?: string;
  height?: string;
  margin?: CSSProperties["margin"];
  borderRadius?: CSSProperties["borderRadius"];
}>`
  position: relative;
  display: flex;
  align-items: center;
  background-color: #fff;
  color: ${({ theme }) => theme.palette.ink.normal};
  border-radius: ${({ borderRadius }) => borderRadius || "4px"};
  margin-top: ${({ margin }) => margin || "4px 0 0 0"};
  border: 1px solid ${({ theme }) => theme.palette.ink.lighter};
  cursor: text;
  padding: 0 12px 0 12px;
  font-family: inherit;
  font-size: 14px;
  line-height: 34px;
  ${({ height }) => height && `height: ${height}`};
  ${({ width }) => width && `width: ${width}`};

  &:focus-within {
    border: 2px solid ${({ theme }) => theme.palette.product.normal}90;
  }

  ${({ backgroundColor }) =>
    backgroundColor &&
    css`
      background-color: ${backgroundColor};
    `}

  ${({ isDisabled }) =>
    isDisabled &&
    css`
      background-color: ${({ theme }) => theme.palette.cloud.dark};
    `}

		${({ isError }) =>
    isError &&
    css`
      border-color: ${({ theme }) => theme.palette.red.normal};
    `}


		${mediaQueries.largeMobile(css`
    font-size: 16px;
  `)}
`;

const StyledPrefix = styled.span<{
  isIconExist: boolean;
  prefixBorder?: boolean;
}>`
  ${({ isIconExist }) =>
    isIconExist &&
    css`
      ${({ theme }) => (theme.rtl ? `margin-left: 10px` : `margin-right: 10px`)}
    `};
  ${({ isIconExist, prefixBorder }) =>
    isIconExist &&
    prefixBorder &&
    css`
      ${({ theme }) =>
        theme.rtl
          ? `border-left: 1px solid #c4cdd5; padding-left:12px; `
          : `border-right: 1px solid #c4cdd5; padding-right:12px;`}
    `};
`;

const StyledSuffix = styled.span<{
  isIconExist: boolean;
  isNumberField: boolean;
  suffixBorder?: boolean;
}>`
  ${({ isNumberField }) =>
    isNumberField &&
    css`
      ${({ theme }) => (theme.rtl ? `margin-left: 25px` : `margin-right: 25px`)}
    `};

  ${({ suffixBorder, isIconExist }) =>
    suffixBorder &&
    isIconExist &&
    css`
      ${({ theme }) =>
        theme.rtl
          ? `border-right: 1px solid #c4cdd5; padding-right:12px;`
          : `border-left: 1px solid #c4cdd5; padding-left:12px;`}
    `};

  ${({ suffixBorder, isIconExist, isNumberField }) =>
    suffixBorder &&
    isIconExist &&
    isNumberField &&
    css`
      ${({ theme }) =>
        theme.rtl
          ? `border-right: 1px solid #c4cdd5; padding-right:12px; margin-left: 0px;`
          : `border-left: 1px solid #c4cdd5; padding-left:12px; margin-right: 0px;`}
    `};
`;

export const StyledInput = styled.input<{
  minWidth?: string;
  hideArrows?: boolean;
}>`
  padding: 0;
  line-height: 34px;
  border: none;
  outline: none;
  color: inherit;
  font-family: inherit;
  background-color: transparent;
  width: ${({ hideArrows }) => (hideArrows ? "100%" : "calc(100% - 10px)")};
  min-width: ${({ minWidth }) => minWidth || "100px"};

  &::placeholder {
    color: ${({ theme }) => theme.palette.ink.light};
    opacity: 0.5;
  }
  &::-webkit-input-placeholder {
    color: ${({ theme }) => theme.palette.ink.light};
    opacity: 0.5;
  }
  &:-moz-placeholder {
    color: ${({ theme }) => theme.palette.ink.light};
    opacity: 0.5;
  }
  &:-ms-input-placeholder {
    color: ${({ theme }) => theme.palette.ink.light};
    opacity: 0.5;
  }

  // remove default arrows from input field of type number
  //chrome
  &::-webkit-inner-spin-button,
  ::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  //firefox
  -moz-appearance: textfield;

  font-size: 14px;
  ${mediaQueries.largeMobile(css`
    font-size: 16px;
  `)}
`;

const StyledArrowsWrapper = styled.span`
  display: inline-block;
  position: absolute;
  width: 22px;
  height: 35px;
  top: 0;

  ${({ theme }) => (theme.rtl ? "left:0" : "right:0")};

  ${({ theme }) =>
    theme.rtl
      ? "border-right: solid 1px #c4cdd5;"
      : "border-left: solid 1px #c4cdd5;"};
`;

const StyledArrow = styled.button`
  border: none;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background-color: transparent;
  width: 22px;
  height: 17px;

  &:nth-child(1) {
    border-bottom: solid 1px #c4cdd5;
  }

  &:focus,
  &:active {
    outline: none;
  }

  &:active {
    transform: scale(0.9);
  }

  svg {
    width: 6px;
    height: 3px;
  }
`;

const StyledLoading = styled.span`
  &::after {
    display: inline-block;
    animation: ellipsis 1.25s infinite;
    content: ".";
    width: 1em;
    font-size: 20px;
    transform: translate(0px, -4px);
    text-align: left;
    color: ${color("ink", "light")};
  }
  @keyframes ellipsis {
    0% {
      content: ".";
    }
    20% {
      content: "..";
    }
    40% {
      content: "...";
    }
    60% {
      content: "....";
    }
  }
`;

const InputField = React.forwardRef<HTMLInputElement, InputFieldProps>(
  (props, ref) => {
    const {
      type,
      value,
      prefix,
      prefixBorder,
      suffix,
      suffixBorder,
      placeholder,
      name,
      title,
      isRequired,
      isDisabled,
      isError,
      minLength,
      maxLength,
      minValue,
      maxValue,
      autoComplete,
      readOnly,
      backgroundColor,
      inputMode,
      pattern,
      tabIndex,
      noValidate,
      autoFocus,
      width,
      height,
      noFraction,
      loading,
      minWidth,
      dataTest,
      hideArrows,
      debounce = 0,
      borderRadius,
      margin,
      onChange = () => {},
      onFocus,
      onBlur,
      onKeyUp,
      onKeyDown,
      onSelect,
      onMouseUp,
      onMouseDown,
      onPaste,
    } = props;

    const handleChange = (input: any) => {
      if (!isDisabled) {
        const numberValue = getNumberValue(
          input,
          minValue,
          maxValue,
          noFraction
        );
        if (onChange) {
          return onChange(numberValue as any);
        }
      }
    };

    return (
      <Field component="div">
        <GeneralInputWrapper
          isDisabled={isDisabled}
          backgroundColor={backgroundColor}
          isError={isError}
          width={width}
          height={height}
          title={title}
          margin={margin}
          borderRadius={borderRadius}
        >
          {prefix && (
            <StyledPrefix prefixBorder={prefixBorder} isIconExist={!!prefix}>
              {prefix}
            </StyledPrefix>
          )}
          <DebounceInput
            element={StyledInput}
            debounceTimeout={debounce || 0}
            data-test={dataTest}
            inputRef={ref}
            type={type}
            value={value === null ? undefined : value}
            name={name}
            placeholder={placeholder}
            required={isRequired}
            // data-test={dataTest}
            // data-state={getFieldDataState(!!error)}
            disabled={isDisabled || undefined}
            formNoValidate={noValidate}
            // min={minValue}
            // max={maxValue}
            minLength={minLength}
            maxLength={maxLength}
            // size={size}
            // error={error}
            tabIndex={tabIndex}
            pattern={pattern}
            // inlineLabel={inlineLabel}
            readOnly={readOnly}
            autoComplete={autoComplete}
            autoFocus={autoFocus}
            // id={forID}
            inputMode={inputMode}
            // dataAttrs={dataAttrs}
            minWidth={minWidth}
            onChange={type === "number" ? handleChange : onChange}
            onFocus={onFocus}
            onBlur={onBlur}
            onKeyUp={onKeyUp}
            onKeyDown={onKeyDown}
            onPaste={onPaste}
            onSelect={onSelect}
            onMouseUp={onMouseUp}
            onMouseDown={onMouseDown}
            onWheel={(e) => e.currentTarget.blur()}
            hideArrows={hideArrows}
          />
          {type === "number" && !hideArrows && (
            <StyledArrowsWrapper>
              <StyledArrow
                data-test="button-increment-number"
                type="button"
                tabIndex={-1}
                onClick={() => handleChange(+(value as number) + 1)}
              >
                <ArrowUpFilledIcon />
              </StyledArrow>
              <StyledArrow
                data-test="button-decrement-number"
                type="button"
                tabIndex={-1}
                onClick={() => handleChange(+(value as number) - 1)}
              >
                <ArrowDownFilledIcon />
              </StyledArrow>
            </StyledArrowsWrapper>
          )}

          {loading && <StyledLoading />}

          {suffix && (
            <StyledSuffix
              isIconExist={!!suffix}
              isNumberField={type === "number"}
              suffixBorder={suffixBorder}
            >
              {suffix}
            </StyledSuffix>
          )}
        </GeneralInputWrapper>
      </Field>
    );
  }
);
InputField.displayName = "InputField";
export { InputField };

/**
 *
 *
 * Functions
 *
 *
 */

function discardDigits(value: string) {
  return value?.match(/^-?\d*/g)?.[0];
}

function isEvent(event: any) {
  return Boolean(event && event.target);
}

function getMaxOrMinValue(value: any, minValue: number, maxValue: number) {
  return Math.abs(value - maxValue) < Math.abs(value - minValue)
    ? maxValue
    : minValue;
}

function getNumberValue(
  value: any,
  minValue?: number,
  maxValue?: number,
  noFraction?: boolean
) {
  if (value == undefined) {
    return "";
  }

  const pureValue = isEvent(value) ? value.target.value : value;

  let stringValue = pureValue?.toString();

  if (stringValue?.trim?.() === "") {
    return "";
  }

  if (noFraction) {
    stringValue = discardDigits(stringValue);
  }

  const numberValue = Number(stringValue);

  if (maxValue != undefined && minValue != undefined) {
    return numberValue <= maxValue && numberValue >= minValue
      ? numberValue
      : getMaxOrMinValue(numberValue, minValue, maxValue);
  }

  if (maxValue != undefined) {
    return numberValue <= maxValue ? numberValue : maxValue;
  }

  if (minValue != undefined) {
    return numberValue >= minValue ? numberValue : minValue;
  }

  return numberValue;
}
