import { Box, Heading, Stack } from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import AppColorSetting from "../../ChooseMessaging/AppColorSetting";
import { TWidgetAppearance, TWidgetSettings } from "../../../../shared/types";
import BorderStyle from "./BorderStyle";
import ShadowStyle from "./ShadowStyle";
import GradientAndTransparent from "./GradientAndTransparent";

interface AppColorsProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function AppColors({ appearance, update }: AppColorsProps) {
  const {
    style: { background },
  } = appearance;
  return (
    <Stack
      boxShadow="0px 1px 2px 0px #1018280D"
      spacing="none"
      width="100%"
      border="1px solid #EAECF0"
      borderRadius="10px"
      padding="16px"
      flex="1 1"
    >
      <Heading color="black" fontSize="sm" fontWeight="semiBold">
        <FormattedMessage defaultMessage="Colors" id="U+dGE5" />
      </Heading>
      <Box mt="16px">
        <AppColorSetting
          defaultColor={false}
          color={background?.color}
          handleChangeColor={(color) => {
            update({
              appearance: {
                ...appearance,
                style: {
                  ...appearance.style,
                  background: { ...background, color: color },
                },
              },
            });
          }}
        />
      </Box>
      <BorderStyle appearance={appearance} update={update} />
      <ShadowStyle appearance={appearance} update={update} />
      <GradientAndTransparent appearance={appearance} update={update} />
    </Stack>
  );
}

export default AppColors;
