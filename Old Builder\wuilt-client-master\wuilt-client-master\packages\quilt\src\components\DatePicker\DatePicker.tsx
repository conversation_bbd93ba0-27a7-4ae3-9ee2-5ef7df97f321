import React from "react";
import Picker, { ReactDatePickerProps } from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import styled, { css } from "styled-components";
import { CalendarOutlineIcon } from "../icons";
import * as utils from "../../utils";

export interface DatePickerProps
  extends Omit<ReactDatePickerProps, "selectsRange"> {
  showBorder?: boolean;
  selectsRange?: boolean;
}

const DatePicker = ({ showBorder, inline, ...restProps }: DatePickerProps) => {
  return (
    <DatePickerContainer
      inline={inline || false}
      showBorder={showBorder || false}
    >
      <Picker
        inline={inline}
        icon={(restProps as any)?.icon || <CalendarOutlineIcon />}
        {...restProps}
      />
    </DatePickerContainer>
  );
};

export { DatePicker };

const DatePickerContainer = styled.div<{
  showBorder: boolean;
  inline: boolean;
}>`
  ${({ inline }) =>
    !inline &&
    css`
      display: flex;
      align-items: center;
      gap: 10px;
      border-radius: 6px;
      background: ${utils.color("white")};
      padding: 0 12px;
    `};

  ${({ showBorder, inline }) =>
    showBorder &&
    !inline &&
    css`
      border: 1px solid ${({ theme }) => theme.palette.ink.lighter};
    `}

  &:focus-within {
    ${({ showBorder, inline }) =>
      showBorder &&
      !inline &&
      css`
        border: 1px solid ${utils.color("product")};
      `}
  }

  .react-datepicker__input-container {
    display: flex;
  }

  .react-datepicker__input-container input {
    padding: 0;
    ${({ inline }) =>
      !inline &&
      css`
        border: none;
        border-radius: 6px;
        width: 100%;
        background: ${utils.color("white")};
      `};
  }

  .react-datepicker__input-container input::placeholder {
    color: ${({ theme }) => theme.palette.ink.lighter};
  }

  .react-datepicker__calendar-icon {
    position: relative;
  }

  .react-datepicker__navigation-icon::before {
    top: 18px;
    border-width: 2px 2px 0 0;
    height: 7px;
    width: 7px;
  }
  .react-datepicker__month-container + div {
    border-left: 2px solid ${utils.color("cloud", "light")};
  }
  .react-datepicker__day--in-range,
  .react-datepicker__day--in-selecting-range {
    background-color: ${utils.color("cloud", "dark")} !important;
    color: ${utils.color("ink")} !important;
    position: relative;
    border-radius: 0 !important;
  }

  .react-datepicker__day--in-range::after,
  .react-datepicker__day--in-selecting-range::after {
    content: "";
    position: absolute;
    right: ${utils.rtl("-6px", "0")};
    left: ${utils.rtl("0", "-6px")};
    bottom: 0;
    background-color: ${utils.color("cloud", "dark")} !important;
    height: 100%;
    width: 6px;
  }
  .react-datepicker__day--in-range:first-child::after,
  .react-datepicker__day--in-selecting-range:first-child::after,
  .react-datepicker__day--selected::after {
    display: none;
  }
  .react-datepicker__day--range-start,
  .react-datepicker__day--selecting-range-start {
    border-radius: 4px !important;
  }
  .react-datepicker__day--range-end {
    background: ${utils.color("product")} !important;
    position: relative;
    color: ${utils.color("white")} !important;
    border-radius: 4px !important;
  }
  .react-datepicker__day--keyboard-selected {
    background: ${utils.color("product")} !important;
    position: relative;
    color: ${utils.color("white")} !important;
  }
  .react-datepicker__day--today {
    font-weight: 400;
    color: ${utils.color("product")};
  }

  .react-datepicker__day--outside-month {
    color: ${utils.color("ink", "lightHover")} !important;
  }
  .react-datepicker__day--selected,
  .react-datepicker__day--range-start {
    background: ${utils.color("product")} !important;
    color: ${utils.color("white")} !important;
  }

  .react-datepicker__header {
    background-color: ${utils.color("white")};
    border-bottom: none;
  }

  .react-datepicker__input-container input:focus-visible,
  .react-datepicker__navigation {
    outline: none;
  }

  .react-datepicker__current-month {
    color: ${utils.color("ink")} !important;
    padding-top: 10px;
    padding-bottom: 10px;
    font-weight: 500;
  }

  .react-datepicker {
    border: 2px solid ${utils.color("cloud", "light")};
    background: ${utils.color("white")};
    border-radius: 0;
  }

  .react-datepicker__day-name {
    color: ${utils.color("ink", "light")} !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
  }

  .react-datepicker__input-time-container {
    border-left: none !important;
  }
  .react-datepicker-time__caption {
    font-size: 14px !important;
    font-weight: 600 !important;
    color: ${utils.color("ink", "light")} !important;
    margin: 0 5px !important;
  }

  .react-datepicker__input-time-container {
    margin: 5px 15px 10px !important;
    text-align: ${utils.left} !important;
    float: none !important;
    width: auto !important;
  }
  .react-datepicker__input-time-container
    .react-datepicker-time__input-container
    .react-datepicker-time__input
    input {
    width: auto;
    height: 35.6px;
    padding: 0 7px;
  }
  .react-datepicker__input-time-container
    .react-datepicker-time__input-container
    .react-datepicker-time__input
    input:focus-within {
    outline: 1px solid ${utils.color("product")} !important;
  }
`;
