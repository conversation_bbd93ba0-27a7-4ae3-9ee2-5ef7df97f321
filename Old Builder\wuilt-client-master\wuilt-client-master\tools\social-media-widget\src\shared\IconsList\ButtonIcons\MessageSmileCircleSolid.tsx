import { TIconType } from "../types";

export function MessageSmileCircleSolid({
  size = 20,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.4168 1.66675C6.04453 1.66675 2.50012 5.21116 2.50012 9.58342C2.50012 10.4667 2.64511 11.3179 2.91325 12.1134C2.96041 12.2534 2.98817 12.3362 3.00621 12.3972L3.01212 12.4178L3.00899 12.4243C2.98757 12.4685 2.95574 12.5278 2.89619 12.638L1.51963 15.1859C1.43431 15.3438 1.34529 15.5085 1.28407 15.6517C1.22321 15.794 1.12464 16.0551 1.17314 16.3647C1.23 16.7276 1.44357 17.0471 1.75714 17.2384C2.02464 17.4015 2.30363 17.4103 2.45842 17.4085C2.61414 17.4067 2.80033 17.3874 2.9788 17.3689L7.27461 16.9248C7.34341 16.9177 7.37938 16.9141 7.40573 16.912L7.40912 16.9118L7.41942 16.9154C7.4518 16.9271 7.49571 16.9439 7.5715 16.9732C8.45562 17.3138 9.41543 17.5001 10.4168 17.5001C14.789 17.5001 18.3335 13.9557 18.3335 9.58342C18.3335 5.21116 14.789 1.66675 10.4168 1.66675ZM11.6606 7.50008C11.6606 6.80973 12.2203 6.25008 12.9106 6.25008C13.601 6.25008 14.1606 6.80973 14.1606 7.50008C14.1606 8.19044 13.601 8.75008 12.9106 8.75008C12.2203 8.75008 11.6606 8.19044 11.6606 7.50008ZM6.57732 11.0001C6.94435 10.7249 7.46455 10.7981 7.74137 11.1633C7.79274 11.2289 7.84926 11.2905 7.90684 11.3506C8.02959 11.4787 8.21404 11.6525 8.45288 11.8262C8.93376 12.1759 9.59859 12.5001 10.4107 12.5001C11.2227 12.5001 11.8875 12.1759 12.3684 11.8262C12.6073 11.6525 12.7917 11.4787 12.9145 11.3506C12.972 11.2906 13.0286 11.2289 13.0799 11.1633C13.3568 10.7981 13.877 10.7249 14.244 11.0001C14.6122 11.2763 14.6868 11.7986 14.4107 12.1668C14.4128 12.1641 14.4078 12.1703 14.3993 12.181C14.379 12.2066 14.3384 12.2577 14.3245 12.2741C14.2756 12.3324 14.2065 12.4112 14.1178 12.5038C13.941 12.6882 13.6828 12.9311 13.3487 13.1741C12.6838 13.6577 11.6819 14.1668 10.4107 14.1668C9.13938 14.1668 8.13754 13.6577 7.47259 13.1741C7.13851 12.9311 6.88026 12.6882 6.70353 12.5038C6.61482 12.4112 6.54566 12.3324 6.49679 12.2741C6.47232 12.245 6.45286 12.2209 6.43849 12.2028C6.15063 11.8309 6.18933 11.2911 6.57732 11.0001ZM7.91065 6.25008C7.22029 6.25008 6.66065 6.80973 6.66065 7.50008C6.66065 8.19044 7.22029 8.75008 7.91065 8.75008C8.601 8.75008 9.16064 8.19044 9.16064 7.50008C9.16064 6.80973 8.601 6.25008 7.91065 6.25008Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
