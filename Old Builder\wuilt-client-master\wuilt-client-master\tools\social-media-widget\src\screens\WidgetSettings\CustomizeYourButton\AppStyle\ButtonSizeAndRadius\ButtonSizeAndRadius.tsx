import React from "react";
import { Box, InputRange, Stack } from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import {
  TWidgetAppearance,
  TWidgetSettings,
  TWidgetStyle,
} from "../../../../../shared/types";
import CustomText from "../../../ui/CustomText";
interface ButtonSizeAndRadiusProps {
  appearance: TWidgetAppearance;
  buttonStyle: TWidgetStyle;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function ButtonSizeAndRadius({
  appearance,
  buttonStyle,
  update,
}: ButtonSizeAndRadiusProps) {
  return (
    <Stack largeMobile={{ direction: "column" }} mt="16px" direction="row">
      <Box width="100%">
        <CustomText>
          <FormattedMessage defaultMessage="Button size" id="ubd9HZ" />
        </CustomText>
        <Stack mt="6px" spacing="condensed" align="center" direction="row">
          <InputRange
            value={buttonStyle.size.replace("px", "")}
            min="40"
            max="80"
            onChange={(value) => {
              update({
                appearance: {
                  ...appearance,
                  style: { ...buttonStyle, size: `${value}px` },
                },
              });
            }}
          />
          <Box>{buttonStyle.size}</Box>
        </Stack>
      </Box>
      <Box width="100%">
        <CustomText>
          <FormattedMessage defaultMessage="Corner radius" id="L5SBmk" />
        </CustomText>
        <Stack mt="6px" spacing="condensed" align="center" direction="row">
          <InputRange
            value={buttonStyle.radius.replace("px", "")}
            min="0"
            max="60"
            onChange={(value) => {
              update({
                appearance: {
                  ...appearance,
                  style: { ...buttonStyle, radius: `${value}px` },
                },
              });
            }}
          />

          <Box>{buttonStyle.radius}</Box>
        </Stack>
      </Box>
    </Stack>
  );
}

export default ButtonSizeAndRadius;
