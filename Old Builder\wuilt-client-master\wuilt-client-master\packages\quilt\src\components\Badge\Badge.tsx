import React from "react";
import styled, { css } from "styled-components";
import { ButtonIcon } from "../ButtonIcon";
import { CloseIcon } from "../icons";
import { Global } from "../../common/types";
import BadgeTypes from "./BadgeTypes";

type Type = keyof typeof BadgeTypes;

export interface Props extends Global {
  id?: string;
  className?: string;
  prefixIcon?: React.ReactNode;
  suffixIcon?: React.ReactNode;
  type?: Type;
  label: React.ReactNode;
  noIcon?: boolean;
  onClose?: (e: any) => void;
  closeIcon?: React.ReactNode;
  style?: React.CSSProperties;
  title?: string;
  fitContent?: boolean;
}

const Badge = (props: Props) => {
  const {
    id,
    label,
    type = "primary",
    prefixIcon = null,
    suffixIcon = null,
    noIcon = false,
    onClose,
    closeIcon = <CloseIcon />,
    dataTest,
    className,
    style,
    title,
    fitContent,
  } = props;
  return (
    <StyledBadge
      className={className}
      style={style}
      id={id}
      mode={type}
      data-test={dataTest}
      title={title}
      fitContent={fitContent || false}
    >
      {prefixIcon}
      {prefixIcon && <span>&nbsp;&nbsp;</span>}
      {!prefixIcon && !noIcon && <StyledIcon />}
      {label}
      {suffixIcon}
      {!!onClose && (
        <ButtonIcon onlyIcon transparent onClick={onClose}>
          {closeIcon}
        </ButtonIcon>
      )}
    </StyledBadge>
  );
};

Badge.displayName = "Badge";
export { Badge };

/**
 *
 * Styles
 *
 */

const StyledIcon = styled.span`
  display: inline-block;
  content: " ";
  width: 8px;
  height: 8px;
  border-radius: 50%;
  outline: none;

  ${({ theme }) => (theme.rtl ? "margin-left: 5px;" : "margin-right: 5px;")}
`;

const StyledBadge = styled.span<{ mode: Type; fitContent: boolean }>`
  padding: 4px 6px;
  border-radius: 100px;
  border: solid 2px #ffffff;
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
  border: 2px solid #fff;
  background-color: transparent;
  outline: none;
  width: fit-content;
  min-width: ${({ fitContent }) => fitContent && "fit-content"};
  max-height: 30px;

  display: flex;
  justify-content: center;
  align-items: center;

  ${({ mode }) =>
    mode &&
    css`
		 background-color: ${BadgeTypes[mode].background};
		 color: ${BadgeTypes[mode].colorText};

		 ${StyledIcon} {

		 border: 2px solid ${BadgeTypes[mode].colorText};

			 ${
         mode === "success" &&
         css`
           background-color: ${BadgeTypes[mode].colorText};
         `
       }
	 `}

  &:active,
 &:focus {
    outline: none;
  }
`;
