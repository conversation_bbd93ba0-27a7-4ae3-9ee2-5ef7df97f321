import React from "react";
import { Box, BoxProps } from "../Box";

export type CardProps = BoxProps;
export interface CardHeaderProps extends BoxProps {
  children: any;
}
export interface CardBodyProps extends BoxProps {
  children: any;
}
export type CardDividerProps = BoxProps;
export interface CardFooterProps extends BoxProps {
  children: any;
}

const Card: React.FC<CardProps> & {
  Header: React.FC<CardHeaderProps>;
  Body: React.FC<CardBodyProps>;
  Footer: React.FC<CardFooterProps>;
  Divider: React.FC<CardDividerProps>;
} = ({ children, ...props }) => {
  return (
    <Box
      bgColor="white"
      boxShadow="fixed"
      borderRadius="4px"
      width="100%"
      position="relative"
      {...props}
    >
      {children}
    </Box>
  );
};

Card.Header = ({ children, ...props }: CardHeaderProps) => (
  <Box position="relative" padding="20px 20px 0" {...props}>
    {children}
  </Box>
);

Card.Body = ({ children, ...props }: CardBodyProps) => (
  <Box position="relative" padding="20px" {...props}>
    {children}
  </Box>
);

Card.Divider = (props: CardDividerProps) => (
  <Box width="100%" height="1px" bg="disabled" {...props} />
);

Card.Footer = ({ children, ...props }: CardFooterProps) => (
  <>
    <Box width="100%" height="1px" bg="disabled" />
    <Box position="relative" padding="20px" {...props}>
      {children}
    </Box>
  </>
);

export { Card };
