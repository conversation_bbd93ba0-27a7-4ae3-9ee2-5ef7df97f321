import {
  PointerActivationConstraint,
  use<PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>or,
  Mouse<PERSON><PERSON>or,
  TouchSensor,
  KeyboardSensor,
  DragEndEvent,
  DndContext,
  closestCenter,
} from "@dnd-kit/core";
import {
  restrictToVerticalAxis,
  restrictToHorizontalAxis,
  restrictToFirstScrollableAncestor,
} from "@dnd-kit/modifiers";
import {
  horizontalListSortingStrategy,
  verticalListSortingStrategy,
  arrayMove,
  SortableContext as DndSortableContext,
  rectSortingStrategy,
} from "@dnd-kit/sortable";
import React from "react";

export interface SortableContextProps<Type> {
  value: Type[];
  onChange: (value: Type[], oldIndex: number, newIndex: number) => void;
  children: React.ReactNode;
  uniqueFieldName?: string;
  limitToContainerEdges?: boolean;
  distance?: number;
  horizontal?: boolean;
  grid?: boolean;
}

export function SortableContext<Type>({
  limitToContainerEdges = true,
  value,
  uniqueFieldName = "id",
  onChange,
  children,
  distance = 5,
  horizontal = false,
  grid = false,
  ...restDndContextProps
}: SortableContextProps<Type>) {
  const activationConstraint: PointerActivationConstraint = { distance };
  const sensors = [
    useSensor(PointerSensor, { activationConstraint }),
    useSensor(MouseSensor, { activationConstraint }),
    useSensor(TouchSensor),
    useSensor(KeyboardSensor),
  ];

  const strategy = horizontal
    ? horizontalListSortingStrategy
    : grid
    ? rectSortingStrategy
    : verticalListSortingStrategy;

  const restrictToAxis = horizontal
    ? restrictToHorizontalAxis
    : restrictToVerticalAxis;

  const handleDrag = ({ active, over }: DragEndEvent) => {
    document.body.style.cursor = "";
    if (!active.data.current || !over?.data.current || active.id === over.id)
      return;
    const oldIndex = active.data.current.sortable.index;
    const newIndex = over.data.current.sortable.index;
    const tempItems = arrayMove(value, oldIndex, newIndex);
    onChange?.(tempItems, oldIndex, newIndex);
  };
  const onDragStart = () => {
    document.body.style.cursor = "grabbing";
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDrag}
      modifiers={
        limitToContainerEdges
          ? [restrictToAxis, restrictToFirstScrollableAncestor]
          : []
      }
      onDragStart={onDragStart}
      {...restDndContextProps}
    >
      <DndSortableContext
        items={value.map((item) => item[uniqueFieldName])}
        strategy={strategy}
      >
        {children}
      </DndSortableContext>
    </DndContext>
  );
}
