'use client'

import {
  Box,
  VStack,
  Text,
  Input,
  Textarea,
  Select,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  FormControl,
  FormLabel,
  Switch,
  Button,
  Divider,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  useColorModeValue
} from '@chakra-ui/react'
import { Element, useEditorStore } from '../../../lib/stores/editorStore'

interface ElementPropertiesProps {
  element: Element
}

export function ElementProperties({ element }: ElementPropertiesProps) {
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const { updateElement, deleteElement } = useEditorStore()

  const handlePropChange = (key: string, value: any) => {
    updateElement(element.id, {
      props: {
        ...element.props,
        [key]: value
      }
    })
  }

  const handleStyleChange = (key: string, value: any) => {
    updateElement(element.id, {
      style: {
        ...element.style,
        [key]: value
      }
    })
  }

  const renderElementSpecificProps = () => {
    switch (element.type) {
      case 'text':
        return (
          <VStack spacing={3} align="stretch">
            <FormControl>
              <FormLabel fontSize="sm">Content</FormLabel>
              <Textarea
                value={element.props.content || ''}
                onChange={(e) => handlePropChange('content', e.target.value)}
                size="sm"
                rows={3}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel fontSize="sm">Font Size</FormLabel>
              <Input
                value={element.props.fontSize || '16px'}
                onChange={(e) => handlePropChange('fontSize', e.target.value)}
                size="sm"
                placeholder="16px"
              />
            </FormControl>
            
            <FormControl>
              <FormLabel fontSize="sm">Font Weight</FormLabel>
              <Select
                value={element.props.fontWeight || 'normal'}
                onChange={(e) => handlePropChange('fontWeight', e.target.value)}
                size="sm"
              >
                <option value="normal">Normal</option>
                <option value="bold">Bold</option>
                <option value="lighter">Light</option>
                <option value="100">100</option>
                <option value="200">200</option>
                <option value="300">300</option>
                <option value="400">400</option>
                <option value="500">500</option>
                <option value="600">600</option>
                <option value="700">700</option>
                <option value="800">800</option>
                <option value="900">900</option>
              </Select>
            </FormControl>
            
            <FormControl>
              <FormLabel fontSize="sm">Text Align</FormLabel>
              <Select
                value={element.props.textAlign || 'left'}
                onChange={(e) => handlePropChange('textAlign', e.target.value)}
                size="sm"
              >
                <option value="left">Left</option>
                <option value="center">Center</option>
                <option value="right">Right</option>
                <option value="justify">Justify</option>
              </Select>
            </FormControl>
          </VStack>
        )
      
      case 'image':
        return (
          <VStack spacing={3} align="stretch">
            <FormControl>
              <FormLabel fontSize="sm">Image URL</FormLabel>
              <Input
                value={element.props.src || ''}
                onChange={(e) => handlePropChange('src', e.target.value)}
                size="sm"
                placeholder="https://example.com/image.jpg"
              />
            </FormControl>
            
            <FormControl>
              <FormLabel fontSize="sm">Alt Text</FormLabel>
              <Input
                value={element.props.alt || ''}
                onChange={(e) => handlePropChange('alt', e.target.value)}
                size="sm"
                placeholder="Image description"
              />
            </FormControl>
            
            <FormControl>
              <FormLabel fontSize="sm">Width</FormLabel>
              <Input
                value={element.props.width || '100%'}
                onChange={(e) => handlePropChange('width', e.target.value)}
                size="sm"
                placeholder="100%"
              />
            </FormControl>
            
            <FormControl>
              <FormLabel fontSize="sm">Height</FormLabel>
              <Input
                value={element.props.height || 'auto'}
                onChange={(e) => handlePropChange('height', e.target.value)}
                size="sm"
                placeholder="auto"
              />
            </FormControl>
          </VStack>
        )
      
      case 'button':
        return (
          <VStack spacing={3} align="stretch">
            <FormControl>
              <FormLabel fontSize="sm">Button Text</FormLabel>
              <Input
                value={element.props.text || ''}
                onChange={(e) => handlePropChange('text', e.target.value)}
                size="sm"
                placeholder="Button text"
              />
            </FormControl>
            
            <FormControl>
              <FormLabel fontSize="sm">Color Scheme</FormLabel>
              <Select
                value={element.props.colorScheme || 'blue'}
                onChange={(e) => handlePropChange('colorScheme', e.target.value)}
                size="sm"
              >
                <option value="blue">Blue</option>
                <option value="green">Green</option>
                <option value="red">Red</option>
                <option value="orange">Orange</option>
                <option value="purple">Purple</option>
                <option value="teal">Teal</option>
                <option value="gray">Gray</option>
              </Select>
            </FormControl>
            
            <FormControl>
              <FormLabel fontSize="sm">Size</FormLabel>
              <Select
                value={element.props.size || 'md'}
                onChange={(e) => handlePropChange('size', e.target.value)}
                size="sm"
              >
                <option value="xs">Extra Small</option>
                <option value="sm">Small</option>
                <option value="md">Medium</option>
                <option value="lg">Large</option>
              </Select>
            </FormControl>
            
            <FormControl>
              <FormLabel fontSize="sm">Link URL</FormLabel>
              <Input
                value={element.props.href || ''}
                onChange={(e) => handlePropChange('href', e.target.value)}
                size="sm"
                placeholder="https://example.com"
              />
            </FormControl>
          </VStack>
        )
      
      default:
        return (
          <Text fontSize="sm" color="gray.500">
            No specific properties for {element.type} elements
          </Text>
        )
    }
  }

  return (
    <Box p={4}>
      <VStack spacing={4} align="stretch">
        {/* Element Header */}
        <Box>
          <Text fontSize="sm" fontWeight="semibold" color="gray.700" mb={1}>
            {element.type.charAt(0).toUpperCase() + element.type.slice(1)} Element
          </Text>
          <Text fontSize="xs" color="gray.500">
            ID: {element.id}
          </Text>
        </Box>

        <Divider />

        {/* Element Properties */}
        <Accordion allowToggle defaultIndex={[0]}>
          <AccordionItem border="none">
            <AccordionButton px={0}>
              <Box flex="1" textAlign="left">
                <Text fontSize="sm" fontWeight="medium">Content</Text>
              </Box>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel px={0} pb={4}>
              {renderElementSpecificProps()}
            </AccordionPanel>
          </AccordionItem>

          <AccordionItem border="none">
            <AccordionButton px={0}>
              <Box flex="1" textAlign="left">
                <Text fontSize="sm" fontWeight="medium">Spacing</Text>
              </Box>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel px={0} pb={4}>
              <VStack spacing={3} align="stretch">
                <FormControl>
                  <FormLabel fontSize="sm">Margin</FormLabel>
                  <Input
                    value={element.style.margin || '0'}
                    onChange={(e) => handleStyleChange('margin', e.target.value)}
                    size="sm"
                    placeholder="0px"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Padding</FormLabel>
                  <Input
                    value={element.style.padding || '0'}
                    onChange={(e) => handleStyleChange('padding', e.target.value)}
                    size="sm"
                    placeholder="0px"
                  />
                </FormControl>
              </VStack>
            </AccordionPanel>
          </AccordionItem>

          <AccordionItem border="none">
            <AccordionButton px={0}>
              <Box flex="1" textAlign="left">
                <Text fontSize="sm" fontWeight="medium">Appearance</Text>
              </Box>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel px={0} pb={4}>
              <VStack spacing={3} align="stretch">
                <FormControl>
                  <FormLabel fontSize="sm">Background Color</FormLabel>
                  <Input
                    type="color"
                    value={element.style.backgroundColor || '#ffffff'}
                    onChange={(e) => handleStyleChange('backgroundColor', e.target.value)}
                    size="sm"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Text Color</FormLabel>
                  <Input
                    type="color"
                    value={element.style.color || '#000000'}
                    onChange={(e) => handleStyleChange('color', e.target.value)}
                    size="sm"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Border Radius</FormLabel>
                  <Input
                    value={element.style.borderRadius || '0'}
                    onChange={(e) => handleStyleChange('borderRadius', e.target.value)}
                    size="sm"
                    placeholder="0px"
                  />
                </FormControl>
              </VStack>
            </AccordionPanel>
          </AccordionItem>
        </Accordion>

        <Divider />

        {/* Actions */}
        <Button
          colorScheme="red"
          variant="outline"
          size="sm"
          onClick={() => deleteElement(element.id)}
        >
          Delete Element
        </Button>
      </VStack>
    </Box>
  )
}
