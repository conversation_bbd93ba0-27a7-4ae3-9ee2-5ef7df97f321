import React from "react";
import { MemoryRouter } from "react-router-dom";
import { UnstyledLink } from "./UnstyledLink";

export default {
  title: "Components/UnstyledLink",
  component: UnstyledLink,
  decorators: [
    (Story) => (
      <MemoryRouter>
        <Story />
      </MemoryRouter>
    ),
  ],
};

export const Playground = () => {
  return (
    <UnstyledLink asComponent="a" href="https://wuilt.com">
      Link Text
    </UnstyledLink>
  );
};
