import { TIconType } from "../types";

export function Discord({ size = 40, width, height, color }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M29.7032 10.8237C27.9186 9.97958 26.0048 9.35768 24.0039 9.00149C23.9674 8.99462 23.931 9.0118 23.9123 9.04616C23.6661 9.4974 23.3935 10.0861 23.2026 10.5488C21.0505 10.2167 18.9094 10.2167 16.8014 10.5488C16.6104 10.0758 16.3279 9.4974 16.0807 9.04616C16.0619 9.01295 16.0255 8.99577 15.9891 9.00149C13.9892 9.35654 12.0755 9.97844 10.2898 10.8237C10.2743 10.8306 10.261 10.842 10.2523 10.8569C6.62229 16.4471 5.62789 21.9 6.11571 27.2852C6.11792 27.3116 6.13226 27.3368 6.15213 27.3528C8.54708 29.1658 10.867 30.2664 13.1438 30.996C13.1803 31.0075 13.2189 30.9937 13.2421 30.9628C13.7806 30.2046 14.2607 29.4052 14.6724 28.5645C14.6967 28.5153 14.6735 28.4569 14.6238 28.4374C13.8623 28.1396 13.1372 27.7765 12.4397 27.3642C12.3845 27.331 12.3801 27.2497 12.4309 27.2107C12.5776 27.0974 12.7245 26.9794 12.8646 26.8603C12.89 26.8385 12.9253 26.8339 12.9551 26.8477C17.5375 29.0043 22.4985 29.0043 27.0268 26.8477C27.0566 26.8328 27.0919 26.8374 27.1184 26.8591C27.2586 26.9783 27.4054 27.0974 27.5533 27.2107C27.604 27.2497 27.6007 27.331 27.5455 27.3642C26.848 27.7845 26.1229 28.1396 25.3603 28.4362C25.3106 28.4557 25.2885 28.5153 25.3128 28.5645C25.7333 29.404 26.2134 30.2034 26.742 30.9616C26.7641 30.9937 26.8038 31.0075 26.8403 30.996C29.1282 30.2664 31.4481 29.1658 33.843 27.3528C33.864 27.3368 33.8772 27.3127 33.8794 27.2864C34.4633 21.0604 32.9016 15.6523 29.7396 10.858C29.7319 10.842 29.7186 10.8306 29.7032 10.8237ZM15.3567 24.0062C13.9771 24.0062 12.8403 22.7005 12.8403 21.0971C12.8403 19.4937 13.955 18.188 15.3567 18.188C16.7694 18.188 17.8951 19.5051 17.873 21.0971C17.873 22.7005 16.7583 24.0062 15.3567 24.0062ZM24.6605 24.0062C23.281 24.0062 22.1442 22.7005 22.1442 21.0971C22.1442 19.4937 23.2589 18.188 24.6605 18.188C26.0732 18.188 27.199 19.5051 27.1769 21.0971C27.1769 22.7005 26.0732 24.0062 24.6605 24.0062Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
