import React from "react";
import styled from "styled-components";

import * as Icons from "../icons";

const List = styled.div`
  display: flex;
  padding: 30px;
  flex-flow: row wrap;
  justify-content: space-between;
  border-radius: 4px;
`;

const Container = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  align-content: center;
  width: 100%;
  min-height: 80px;
  background-color: white;
  margin-bottom: 30px;
  border-radius: 5px;
  border: 1px solid ${(props) => props.theme.palette.cloud.normal};
  padding-right: 30px;
`;

const IconImport = styled.pre`
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier,
    monospace;
  font-size: 12px;
  line-height: 20px;
  color: ${(props) => props.theme.palette.ink.light};
  background-color: ${(props) => props.theme.palette.cloud.light};
  border: 1px solid ${(props) => props.theme.palette.cloud.normal};
  padding: 4px 8px;
`;

const IconList = ({ customColor, size }: any) => {
  return (
    <List>
      {Object.keys(Icons).map((icon) => {
        const Icon = styled(Icons[icon])`
          margin: 0 30px;
          flex-shrink: 0;
        `;
        const iconName = `${icon}`;
        return (
          <Container key={icon}>
            <Icon size={size} customColor={customColor} />
            <IconImport>
              {`import { ${iconName} } from "@wuilt/quilt"`}
            </IconImport>
          </Container>
        );
      })}
    </List>
  );
};

export default IconList;
