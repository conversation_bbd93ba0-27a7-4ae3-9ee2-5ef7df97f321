import React from "react";
import { select, text, color, boolean } from "@storybook/addon-knobs";
import { useTheme } from "styled-components";
import * as Icons from "../../icons";
import { ButtonPrimitive } from "../primitive";

const getIcons = (name, defaultIcon) =>
  select(name, [null, ...Object.keys(Icons)], defaultIcon, "Compoent");
const getIcon = (source) => Icons[source];

export default {
  title: "Components/Buttons/ButtonPrimitive",
  component: ButtonPrimitive,
};

// const kinds = ["empty", "full"];
// const colors = [
//   "orange-gradient",
//   "product-gradient",
//   "product",
//   "red",
//   "orange",
//   "blue",
//   "cloud",
//   "ink",
// ];
// const sizes = ["small", "medium", "large"];
export const ButtonPrimitivePlayground = () => {
  const theme: any = useTheme();
  const IconBefor = getIcon(getIcons("prefix", "Settings"));
  const IconAfter = getIcon(getIcons("suffix", "Edit"));
  return (
    <ButtonPrimitive
      background={color("background", theme.palette.product.normal, "Compoent")}
      backgroundHover={color(
        "backgroundHover",
        theme.palette.product.normalHover,
        "Compoent"
      )}
      foreground={color("foreground", theme.palette.white.normal, "Compoent")}
      height="50px"
      backgroundActive="#ccc"
      squared
      fullWidth={boolean("fullWidth", false, "Compoent")}
      icons={{
        leftMargin: "10px",
        rightMargin: "10px",
      }}
      prefixIcon={IconBefor && <IconBefor />}
      suffixIcon={IconAfter && <IconAfter />}
      disabled
      // kind={select("kind", kinds, "full") as any}
      // size={select("size", sizes, "medium") as any}
      // prefix={IconBefor}
      // suffix={IconAfter}
      // onClick={action("clicked")}
      // asComponent={select("asComponent", ["a", "button"], "button") as any}
      // href="https://wuilt.com"
    >
      {text("children", "test", "Compoent")}
    </ButtonPrimitive>
  );
};
