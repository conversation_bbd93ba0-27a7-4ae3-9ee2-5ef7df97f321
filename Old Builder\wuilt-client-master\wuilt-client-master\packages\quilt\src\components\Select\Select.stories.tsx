import { object, text } from "@storybook/addon-knobs";
import React from "react";
import { Select, TimezoneSelect } from "./index";
import CountrySelect from "./CountrySelect";

export default {
  title: "Components/Form/Select",
  component: Select,
};

export const Playground = () => {
  return (
    <Select
      id={text("id", "Availability", "Component")}
      // label={text("label", "Availability", "Component")}
      options={object(
        "options",
        [
          { value: "0", label: "Available" },
          { value: "1", label: "Not Available", dataTest: "not-avail" },
          { value: "2", label: "Disabled", disabled: true },
        ],
        "Component"
      )}
      value={object(
        "selectedItem",
        { value: "1", label: "Not Available" },
        "Component"
      )}
    />
  );
};

export const CountrySelectExample = () => {
  return <CountrySelect placeholder="Country" />;
};

export const TimezoneSelectExample = () => {
  return <TimezoneSelect />;
};
