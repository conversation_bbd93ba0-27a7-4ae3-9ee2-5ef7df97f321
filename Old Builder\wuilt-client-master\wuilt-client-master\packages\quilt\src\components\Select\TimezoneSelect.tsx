import Select from "react-select";
import React from "react";
import _sortBy from "lodash/sortBy";
import { createSelect } from "./createSelect";
import type { SelectProps, OptionType } from "./types";
import { timezones } from "./data/timezones";

const getOffset = (timeZone: string) => {
  const date = new Date();
  const utcDate = new Date(date.toLocaleString("en-US", { timeZone: "UTC" }));
  const tzDate = new Date(date.toLocaleString("en-US", { timeZone }));
  return (tzDate.getTime() - utcDate.getTime()) / 6e4 / 60;
};

const getLabel = (timezone: string, hours: any) => {
  const negative = hours < 0;
  hours = Math.abs(Math.round(hours));
  hours = negative ? `-${hours}` : `+${hours}`;
  return `(GMT${hours}:00) ${timezone}`;
};

const getTimezonesOptions = () => {
  const offsetTmz: any[] = [];

  timezones.forEach((timezone) => {
    const hours = getOffset(timezone);
    const label = getLabel(timezone, hours);
    offsetTmz.push({
      label,
      value: timezone,
      hours,
    });
  });

  return _sortBy(offsetTmz, [(el) => -el.hours]);
};

const TIMEZONES_OPTIONS = getTimezonesOptions();

const SelectTimezone: React.FC<SelectProps<OptionType>> = (props: any) => {
  const value = TIMEZONES_OPTIONS.find((o) => o.value === props.value);
  return <Select options={TIMEZONES_OPTIONS} {...props} value={value} />;
};

export default createSelect(SelectTimezone);
