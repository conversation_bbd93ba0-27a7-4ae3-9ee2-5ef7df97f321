import React from "react";
import { Heading } from "../Heading";
import { ScrollableList } from "./ScrollableList";
import { Box } from "../Box";

export default {
  title: "Components/ScrollableList",
  component: ScrollableList,
};

const ITEMS = [
  { title: "item 1", id: 1 },
  { title: "item 2", id: 2 },
  { title: "item 3", id: 3 },
  { title: "item 4", id: 4 },
  { title: "item 5", id: 5 },
  { title: "item 6", id: 6 },
  { title: "item 7", id: 7 },
  { title: "item 8", id: 8 },
  { title: "item 9", id: 9 },
  { title: "item 10", id: 10 },
  { title: "item 11", id: 11 },
  { title: "item 12", id: 12 },
  { title: "item 13", id: 13 },
  { title: "item 14", id: 14 },
  { title: "item 15", id: 15 },
  { title: "item 16", id: 16 },
  { title: "item 17", id: 17 },
  { title: "item 18", id: 18 },
  { title: "item 19", id: 19 },
  { title: "item 20", id: 20 },
  { title: "item 21", id: 21 },
  { title: "item 22", id: 22 },
  { title: "item 23", id: 23 },
  { title: "item 24", id: 24 },
];

export const playground = () => {
  const onFetchMore = () => {
    // eslint-disable-next-line no-console
    console.log("object");
  };

  return (
    <ScrollableList
      items={ITEMS}
      itemsTotalCount={ITEMS.length + 1}
      placeholder="Search"
      isLoading
      onFetchMore={onFetchMore}
    >
      {(item) => (
        <Box key={item.id} margin="20px">
          <Heading>{item.title}</Heading>
        </Box>
      )}
    </ScrollableList>
  );
};
