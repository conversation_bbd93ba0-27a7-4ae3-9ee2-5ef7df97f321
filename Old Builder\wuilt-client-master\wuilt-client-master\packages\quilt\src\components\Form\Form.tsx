// @ts-nocheck
import React from "react";
import {
  createForm,
  FieldConfig,
  FieldState,
  FieldSubscription,
  FormState,
  Unsubscribe,
  ValidationErrors,
} from "final-form";
import createDecorator from "final-form-focus";

import { OnSubmitHandler } from "./types";

type DefaultValue<FieldValue> = (value?: FieldValue) => FieldValue;

type RegisterField = <FieldValue>(
  name: string,
  defaultValue: FieldValue | DefaultValue<FieldValue>,
  subscriber: (state: FieldState<FieldValue>) => void,
  subscription: FieldSubscription,
  config: FieldConfig<FieldValue>
) => Unsubscribe;

export const FormContext = React.createContext<RegisterField>(() => {
  return () => {};
});
export const FormStateContext = React.createContext({
  isDisabled: false,
  isSubmitting: false,
  isDirty: false,
  initialValues: {},
});

interface FormChildrenProps<FormData> {
  ref: React.RefObject<HTMLFormElement>;
  onSubmit: (
    event?: React.FormEvent<HTMLFormElement> | React.SyntheticEvent<HTMLElement>
  ) => Promise<FormData>;
  onKeyDown: (event: React.KeyboardEvent<HTMLElement>) => void;
}

export interface FormProps<FormData> {
  /* Children rendered inside the Form component. Function will be passed props from the form. */
  children: (args: {
    formProps: FormChildrenProps<FormData>;
    isDisabled: boolean;
    dirty: boolean;
    submitting: boolean;
    getValues: () => FormData;
    setFieldValue: (name: string, value: any) => void;
    reset: (initialValues?: FormData) => void;
    initialValues: Partial<FormData>;
    submitSucceeded: boolean;
  }) => React.ReactNode;
  /* Called when the form is submitted without field validation errors */
  onSubmit: OnSubmitHandler<FormData>;
  /* When set the form and all fields will be disabled */
  isDisabled?: boolean;
  /* Set the initial values of the form */
  initialValues?: Partial<FormData>;
  /* unsubscripe for dirty checking */
  keepDirty?: boolean;
  /* Reset the form after submitting */
  resetAfterSubmit?: boolean;
  /* Validation function */
  validate?: (values: FormData) => ValidationErrors | Promise<ValidationErrors>;
}

// eslint-disable-next-line @typescript-eslint/ban-types
function Form<FormData extends Record<string, any> = {}>(
  props: FormProps<FormData>
) {
  const formRef = React.useRef<HTMLFormElement | null>(null);
  const onSubmitRef = React.useRef(props.onSubmit);
  onSubmitRef.current = props.onSubmit;

  const form = React.useState(() => {
    const finalForm = createForm<FormData>({
      onSubmit: (...args) => onSubmitRef.current(...args),
      destroyOnUnregister: true,
      initialValues: props.initialValues || ({} as FormData),
      validate: props.validate,
      mutators: {
        setDefaultValue: (
          // eslint-disable-next-line @typescript-eslint/ban-types
          [name, defaultValue]: [string, {} | DefaultValue<any>],
          state,
          tools
        ) => {
          if (defaultValue !== undefined) {
            let {
              formState: { initialValues },
            } = state;

            const value =
              name && typeof defaultValue === "function"
                ? defaultValue(initialValues[name])
                : defaultValue;

            tools.changeValue(state, name, () => value);
            initialValues = tools.setIn(initialValues, name, value);
            state.formState.initialValues = initialValues;
          }
        },
      },
    });

    createDecorator<FormData>(() =>
      formRef.current
        ? Array.from(formRef.current.querySelectorAll("input"))
        : []
    )(finalForm);

    return finalForm;
  })[0];

  const [state, setState] =
    React.useState<FormState<FormData, Partial<FormData>>>();
  // console.log(state);

  React.useEffect(() => {
    const unsubscribe = form.subscribe(
      (formState) => {
        setState(formState);
      },
      {
        submitting: true,
        // eslint-disable-next-line no-unneeded-ternary
        dirty: props.keepDirty ? false : true,
        // dirtySinceLastSubmit: props.keepDirty ? false : true,
        submitSucceeded: true,
        // submitSucceeded: props.keepDirty ? false : true,
        // values: true,
        // initialValues: true,
        // dirtyFields: true,
        // dirtyFieldsSinceLastSubmit: true,
      }
    );
    return unsubscribe;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form]);

  const registerField = React.useCallback<RegisterField>(
    (name, defaultValue, subscriber, subscription, config) => {
      form.pauseValidation();

      const unsubscribe = form.registerField(
        name,
        subscriber,
        subscription,
        config
      );

      form.mutators.setDefaultValue(name, defaultValue);

      form.resumeValidation();

      return unsubscribe;
    },
    [form]
  );

  React.useEffect(() => {
    if (state?.submitSucceeded && props.resetAfterSubmit) {
      form.reset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state?.submitSucceeded]);

  const handleSubmit = (
    e?: React.FormEvent<HTMLFormElement> | React.SyntheticEvent<HTMLElement>
  ): Promise<FormData> => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    return form.submit();
  };

  const handleReset = (initialValues?: FormData) => {
    form.reset(initialValues || (form.getState().initialValues as FormData));
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLElement>) => {
    if (e.key === "Enter" && (e.ctrlKey || e.metaKey) && formRef.current) {
      const submitButton: HTMLElement | null = formRef.current.querySelector(
        "button:not([type]), button[type='submit'], input[type='submit']"
      );
      if (submitButton) submitButton.click();
      e.preventDefault();
    }
  };

  const { isDisabled = false, children } = props;
  const isDirty = props.keepDirty ? true : state?.dirty;
  // : (state?.submitSucceeded ? state?.dirtySinceLastSubmit : state?.dirty) ||
  // false;

  return (
    <FormContext.Provider value={registerField}>
      <FormStateContext.Provider
        value={{
          isDisabled,
          isSubmitting: state?.submitting,
          isDirty,
          initialValues: form.getState().initialValues,
        }}
      >
        {children({
          formProps: {
            onSubmit: handleSubmit,
            ref: formRef,
            onKeyDown: handleKeyDown,
          },
          dirty: isDirty,
          reset: handleReset,
          submitting: state?.submitting,
          isDisabled: isDisabled || state?.submitting,
          getValues: () => form.getState().values,
          setFieldValue: form.change,
          initialValues: form.getState().initialValues,
          submitSucceeded: state?.submitSucceeded,
        })}
      </FormStateContext.Provider>
    </FormContext.Provider>
  );
}

export { Form };
