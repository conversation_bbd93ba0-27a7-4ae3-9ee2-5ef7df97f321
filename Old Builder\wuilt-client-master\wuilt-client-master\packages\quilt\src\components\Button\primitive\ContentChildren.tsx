import React from "react";
import styled, { css } from "styled-components";

const StyledContentChildren = styled.div<{ contentWidth; hasIcon; isLoading }>`
  display: inline-block;
  width: ${({ contentWidth }) => contentWidth};
  text-align: ${({ hasIcon, theme }) =>
    hasIcon && (theme.rtl ? "right" : "left")};

  ${({ isLoading }) =>
    isLoading &&
    css`
      color: transparent;
    `}
`;

const ContentChildren = ({ children, hasIcon, contentWidth, isLoading }) => (
  <StyledContentChildren
    hasIcon={hasIcon}
    contentWidth={contentWidth}
    isLoading={isLoading}
  >
    {children}
  </StyledContentChildren>
);

export default ContentChildren;
