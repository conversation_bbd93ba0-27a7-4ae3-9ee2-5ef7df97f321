import React from "react";

type UseClickOutside = (
  ref: { current: HTMLElement | null | undefined },
  handler: (ev: React.SyntheticEvent<HTMLLinkElement>) => void,
  closeOnParentWindowClick?: boolean,
  isOnSelf?: boolean
) => void;

export const useClickOutside: UseClickOutside = (
  ref,
  handler,
  closeOnParentWindowClick = true,
  isOnSelf = false
) => {
  React.useEffect(() => {
    const ownerWindow = ref.current?.ownerDocument.defaultView;
    const handleClose = (event) => {
      if (ref.current && !ref.current.contains(event.target)) {
        if (handler) {
          handler(event);
        }
      }
    };
    if (ownerWindow && !isOnSelf) {
      ownerWindow.addEventListener("mousedown", handleClose);
      ownerWindow.addEventListener("touchstart", handleClose);
    } else {
      window.addEventListener("mousedown", handleClose);
      window.addEventListener("touchstart", handleClose);
    }
    if (window.parent !== ownerWindow && closeOnParentWindowClick) {
      window.parent.addEventListener("mousedown", handleClose);
      window.parent.addEventListener("touchstart", handleClose);
    }
    return () => {
      if (ownerWindow && !isOnSelf) {
        ownerWindow.removeEventListener("mousedown", handleClose);
        ownerWindow.removeEventListener("touchstart", handleClose);
      } else {
        window.removeEventListener("mousedown", handleClose);
        window.removeEventListener("touchstart", handleClose);
      }
      if (window.parent !== ownerWindow && closeOnParentWindowClick) {
        window.parent.removeEventListener("mousedown", handleClose);
        window.parent.removeEventListener("touchstart", handleClose);
      }
    };
  }, [closeOnParentWindowClick, handler, isOnSelf, ref]);
};

export default useClickOutside;
