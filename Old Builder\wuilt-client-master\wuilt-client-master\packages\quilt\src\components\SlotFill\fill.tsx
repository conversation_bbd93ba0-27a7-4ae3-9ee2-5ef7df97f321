import ReactDOM from "react-dom";
import { useSlotFillContext } from "./SlotFillContext";

interface FillProps {
  name: string;
  children?: React.ReactNode;
}
const Fill = ({ name, children }: FillProps) => {
  const { getSlot } = useSlotFillContext();
  const slot = getSlot(name);
  const ref = slot.ref.current;
  const Wrapper = slot.Wrapper;
  return ref
    ? ReactDOM.createPortal(
        Wrapper ? <Wrapper>{children}</Wrapper> : children,
        ref
      )
    : null;
};

export default Fill;
