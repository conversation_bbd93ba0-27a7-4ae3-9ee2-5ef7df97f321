import { TIconType } from "../types";

export function MessageSquareSolid({
  size = 40,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.4655 1.66675H13.5343C14.2051 1.66674 14.7588 1.66673 15.2098 1.70358C15.6782 1.74185 16.1088 1.82398 16.5132 2.03006C17.1404 2.34964 17.6504 2.85957 17.9699 3.48678C18.176 3.89123 18.2582 4.32181 18.2964 4.79023C18.3333 5.24122 18.3333 5.79485 18.3333 6.46565V11.0345C18.3333 11.7053 18.3333 12.2589 18.2964 12.7099C18.2582 13.1784 18.176 13.6089 17.9699 14.0134C17.6504 14.6406 17.1404 15.1505 16.5132 15.4701C16.1088 15.6762 15.6782 15.7583 15.2098 15.7966C14.7588 15.8334 14.2052 15.8334 13.5344 15.8334H11.403C10.8496 15.8334 10.6885 15.8373 10.5418 15.8674C10.3947 15.8976 10.2523 15.9476 10.1186 16.0159C9.9853 16.084 9.857 16.1816 9.42484 16.5273L7.4139 18.1361C7.26084 18.2586 7.10481 18.3835 6.96764 18.4741C6.83645 18.5609 6.58209 18.7161 6.25127 18.7165C5.87059 18.7169 5.51047 18.5438 5.27299 18.2463C5.06661 17.9878 5.02894 17.6921 5.01471 17.5355C4.99984 17.3718 4.99988 17.1719 4.99991 16.9759L4.99992 15.8266C4.67341 15.8158 4.39341 15.7885 4.13719 15.7198C2.98688 15.4116 2.08839 14.5131 1.78016 13.3628C1.66592 12.9364 1.66618 12.4442 1.66654 11.7814C1.66656 11.7437 1.66658 11.7055 1.66658 11.6667L1.66658 6.46567C1.66657 5.79486 1.66656 5.24122 1.70341 4.79023C1.74168 4.32181 1.82382 3.89123 2.0299 3.48678C2.34947 2.85957 2.85941 2.34964 3.48662 2.03006C3.89106 1.82398 4.32165 1.74185 4.79006 1.70358C5.24106 1.66673 5.79469 1.66674 6.4655 1.66675Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
