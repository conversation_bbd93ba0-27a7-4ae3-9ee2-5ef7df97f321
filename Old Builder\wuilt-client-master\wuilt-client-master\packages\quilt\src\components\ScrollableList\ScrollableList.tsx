import React, { useEffect, useRef, useState } from "react";
import { SearchIcon } from "../icons";
import { InputField } from "../InputField";
import { Box, BoxProps } from "../Box";

export interface ScrollableListProps<Type = any>
  extends Omit<BoxProps, "children"> {
  items: Type[];
  placeholder?: string;
  itemsTotalCount?: number;
  className?: string;
  isLoading?: boolean;
  hideSearchBar?: boolean;
  threshold?: number;
  searchField?: string;
  searchDataTest?: string;
  children: (items: Type) => React.ReactNode;
  onFetchMore?: () => void;
  onFilter?: (searchQuery: any) => void;
}

function ScrollableList<Type = any>({
  children,
  items,
  placeholder,
  itemsTotalCount,
  isLoading,
  hideSearchBar,
  className,
  onFetchMore,
  onFilter,
  height = "300px",
  searchField = "title",
  searchDataTest,
  threshold = 0.75,
  ...boxStyle
}: ScrollableListProps<Type>) {
  const [filteredItems, setFilteredItems] = useState<Type[]>(items);
  const previousItemsLength = useRef(0);

  useEffect(() => {
    setFilteredItems(items);
  }, [items]);

  const handleFilter = (value: string) => {
    if (onFilter) {
      onFilter(value);
    } else {
      const tempItems = items.filter((elem: any) =>
        elem?.[searchField]?.toLowerCase()?.includes(value)
      );
      setFilteredItems(tempItems);
    }
  };

  const handleScroll = (event: any) => {
    const containerHeight = event.target?.clientHeight;
    const allItemsHeight = event.target?.scrollHeight;
    const hiddenItemsHeight = allItemsHeight - containerHeight;
    const scrolledHeight = event.target?.scrollTop;

    if (
      onFetchMore &&
      itemsTotalCount &&
      scrolledHeight / hiddenItemsHeight > threshold &&
      filteredItems.length < itemsTotalCount &&
      previousItemsLength.current !== filteredItems.length
    ) {
      previousItemsLength.current = filteredItems.length;
      onFetchMore();
    }
  };

  return (
    <Box width="100%">
      {!hideSearchBar && (
        <InputField
          type="text"
          debounce={500}
          onChange={({ target: { value } }) =>
            handleFilter(value?.toLocaleLowerCase())
          }
          prefix={<SearchIcon color="secondary" />}
          placeholder={placeholder}
          loading={isLoading}
          dataTest={searchDataTest}
        />
      )}

      <Box
        m="5px 0"
        boxShadow="xxs"
        overflow="auto"
        borderRadius="4px"
        onScroll={handleScroll}
        className={className}
        height={height}
        {...boxStyle}
      >
        {filteredItems?.map?.(children)}
      </Box>
    </Box>
  );
}

ScrollableList.displayName = "ScrollableList";
export { ScrollableList };
