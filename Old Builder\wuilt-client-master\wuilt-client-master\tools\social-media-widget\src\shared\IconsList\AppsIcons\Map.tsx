import { TIconType } from "../types";

export function Map({ size = 40, width, height, color }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11 17.5C11 12.5294 15.0294 8.5 20 8.5C24.9706 8.5 29 12.5294 29 17.5C29 20.0262 27.8532 22.2402 26.2923 24.267C24.988 25.9606 23.3183 27.6158 21.6506 29.269L21.6495 29.2702C21.3345 29.5825 21.0195 29.8947 20.7071 30.2071C20.3166 30.5976 19.6834 30.5976 19.2929 30.2071C18.9803 29.8945 18.6644 29.5813 18.3492 29.2688C16.6815 27.6156 15.012 25.9606 13.7077 24.267C12.1468 22.2402 11 20.0262 11 17.5ZM23 17.5C23 19.1569 21.6569 20.5 20 20.5C18.3431 20.5 17 19.1569 17 17.5C17 15.8431 18.3431 14.5 20 14.5C21.6569 14.5 23 15.8431 23 17.5Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
