import styled from "styled-components";

const FormHeaderWrapper = styled.div``;

// line-height: ${multiply(gridSize, 4)}px;
// margin-right: ${multiply(gridSize, 4)}px;
const FormHeaderTitle = styled.h2`
  margin-top: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

/**
 * Provide a styled container for form header title.
 */
const FormHeaderDescription = styled.div``;
// margin-top: ${gridSize}px;

/**
 * Provide a styled container for form header content.
 */
const FormHeaderContent = styled.div`
  min-width: 100%;
`;
// margin-top: ${gridSize}px;

export default FormHeaderWrapper;
export { FormHeaderTitle, FormHeaderDescription, FormHeaderContent };
