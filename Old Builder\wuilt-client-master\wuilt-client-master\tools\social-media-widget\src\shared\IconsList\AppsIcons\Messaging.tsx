import { TIconType } from "../types";

export function Messaging({ size = 40, width, height, color }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20 9.5C14.4772 9.5 10 13.9772 10 19.5C10 20.8283 10.2595 22.0985 10.7316 23.2608C10.7742 23.3658 10.7987 23.4264 10.8155 23.4711L10.8206 23.4849L10.82 23.4897C10.815 23.5266 10.8067 23.5769 10.7907 23.673L10.1929 27.2596C10.1661 27.4202 10.1361 27.5999 10.1243 27.756C10.1115 27.9261 10.107 28.1969 10.2297 28.483C10.3814 28.8367 10.6633 29.1186 11.017 29.2703C11.3031 29.393 11.5739 29.3885 11.744 29.3757C11.9001 29.3639 12.0799 29.3339 12.2405 29.3071L15.827 28.7093C15.9231 28.6933 15.9734 28.685 16.0103 28.68L16.015 28.6794L16.0289 28.6845C16.0736 28.7013 16.1342 28.7258 16.2392 28.7684C17.4015 29.2405 18.6717 29.5 20 29.5C25.5228 29.5 30 25.0228 30 19.5C30 13.9772 25.5228 9.5 20 9.5ZM14 19.5C14 18.6716 14.6716 18 15.5 18C16.3284 18 17 18.6716 17 19.5C17 20.3284 16.3284 21 15.5 21C14.6716 21 14 20.3284 14 19.5ZM18.5 19.5C18.5 18.6716 19.1716 18 20 18C20.8284 18 21.5 18.6716 21.5 19.5C21.5 20.3284 20.8284 21 20 21C19.1716 21 18.5 20.3284 18.5 19.5ZM24.5 18C23.6716 18 23 18.6716 23 19.5C23 20.3284 23.6716 21 24.5 21C25.3284 21 26 20.3284 26 19.5C26 18.6716 25.3284 18 24.5 18Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
