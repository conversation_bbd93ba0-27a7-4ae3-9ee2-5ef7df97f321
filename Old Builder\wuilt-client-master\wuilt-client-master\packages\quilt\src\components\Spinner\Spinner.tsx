import React from "react";
import styled, { keyframes, css } from "styled-components";

enum TYPEOPTIONS {
  BUTTON_LOADER = "button",
  SEARCH_LOADER = "search",
  BOX_LOADER = "box",
  PAGE_LOADER = "page",
  INLINE_LOADER = "inline",
}

type SpinnerType = keyof typeof TYPEOPTIONS;

const getHeight = ({ type }) => {
  const tokens = {
    [TYPEOPTIONS.BUTTON_LOADER]: "100%",
    [TYPEOPTIONS.SEARCH_LOADER]: "40px",
    [TYPEOPTIONS.BOX_LOADER]: "80px",
    [TYPEOPTIONS.PAGE_LOADER]: "120px",
    [TYPEOPTIONS.INLINE_LOADER]: "19px",
  };
  const height = tokens[type];
  const prop = type === TYPEOPTIONS.INLINE_LOADER ? "min-height" : "height";
  const style = `${prop}: ${height}`;
  return css`
    ${style}
  `;
};

const getAlign = ({ type }) => {
  const tokens = {
    [TYPEOPTIONS.BUTTON_LOADER]: "center",
    [TYPEOPTIONS.SEARCH_LOADER]: "start",
    [TYPEOPTIONS.BOX_LOADER]: "center",
    [TYPEOPTIONS.PAGE_LOADER]: "center",
    [TYPEOPTIONS.INLINE_LOADER]: "center",
  };

  return tokens[type];
};

const SpinnerAnimation = keyframes`
  100% { transform: rotate(360deg); }
`;

export const StyledLoading = styled(({ children, className, dataTest }) => (
  <div className={className} data-test={dataTest}>
    {children}
  </div>
))`
  ${getHeight};

  display: flex;
  flex-direction: column;
  justify-content: ${getAlign};
  align-items: center;
  overflow: hidden;
  box-sizing: border-box;
`;

function getSize({ size }) {
  return size === "large"
    ? 80
    : size === "medium"
    ? 60
    : size === "small"
    ? 40
    : 20;
}
export const StyledSpinner = styled.svg<{ size }>`
  width: ${getSize}px;
  height: ${getSize}px;
  animation: ${SpinnerAnimation} 0.75s linear infinite;
`;

const StyledSpinnerCircle = styled.circle<{ color?: string }>`
  fill: transparent;
  stroke: ${({ theme, color }) =>
    color || theme?.palette?.product?.normal || "#00A991"};
  stroke-width: 3px;
  stroke-linecap: round;
  stroke-dasharray: 128px;
  stroke-dashoffset: 64px;
`;

export interface SpinnerProps {
  dataTest?: string;
  size?: "large" | "medium" | "small" | "xsmall";
  type?: SpinnerType;
  color?: string;
}

const Spinner: React.FC<SpinnerProps> = (props) => {
  const {
    dataTest = "loading-spinner",
    size = "small",
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    type = "BOX_LOADER",
    color,
  } = props;

  return (
    <StyledLoading dataTest={dataTest}>
      <StyledSpinner size={size} viewBox="0 0 40 40">
        <StyledSpinnerCircle color={color} cx="50%" cy="50%" r="18" />
      </StyledSpinner>
    </StyledLoading>
  );
};

Spinner.displayName = "Spinner";

export { Spinner };
