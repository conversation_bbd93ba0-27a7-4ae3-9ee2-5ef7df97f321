import React from "react";
import { ComponentStory, ComponentMeta } from "@storybook/react";
import { VerticalSort } from "../VerticalSort";
import { Heading } from "../../Heading";
import { Stack } from "../../Stack";
import { Box } from "../../Box";
import { DragHandle } from "../DragHandle";

// https://storybook.js.org/docs/react/essentials/controls
export default {
  title: "Components/Sorting/Vertical",
  component: VerticalSort,
  argTypes: {
    value: { control: false },
    uniqueFieldName: { control: false },
    className: { control: false },
    boxProps: { control: false },
  },
} as ComponentMeta<typeof VerticalSort>;

const ITEMS = [
  { title: "item 1", id: "1" },
  { title: "item 2", id: "2" },
  { title: "item 3", id: "3" },
  { title: "item 4", id: "4" },
  { title: "item 5", id: "5" },
];

const Template: ComponentStory<typeof VerticalSort> = ({
  limitToContainerEdges,
  useHandleOnly,
  ...restProps
}) => {
  const [items, setItems] = React.useState(ITEMS);

  const handleChange = (sortedItems) => {
    setItems(sortedItems);
  };

  return (
    <VerticalSort
      {...restProps}
      limitToContainerEdges={limitToContainerEdges}
      useHandleOnly={useHandleOnly}
      value={items}
      onChange={handleChange}
    >
      {({ item }) => (
        <Box
          mb="10px"
          className="dnd-item"
          border="1px solid"
          borderColor="disabled"
          p="20px"
          bg="warning"
        >
          <Stack direction="row">
            <DragHandle id={item?.id} />
            <Heading>{item.title}</Heading>
          </Stack>
        </Box>
      )}
    </VerticalSort>
  );
};

export const ListSorting = Template.bind({});
ListSorting.args = {
  useHandleOnly: false,
  limitToContainerEdges: true,
};
