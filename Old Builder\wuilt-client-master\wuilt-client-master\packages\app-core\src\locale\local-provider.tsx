import React, { useMemo } from "react";
import { updateUserStorage } from "./user-storage";

export type Locale = {
  dir: "rtl" | "ltr";
  code: string;
  name: string;
  locale: string;
  display: string;
  beamerPanel: string;
};

export type IntlContextType = {
  setLocale: (value: Locale) => void;
  activeLocale: string;
  locale: Locale;
};

export type Locales = { [key: string]: Locale };

export const SUPPORTED_LOCALES: Locales = {
  ar: {
    dir: "rtl",
    code: "ar",
    locale: "ar-EG",
    name: "arabic",
    display: "العربية",
    beamerPanel: "left",
  },
  en: {
    dir: "ltr",
    code: "en",
    locale: "en-US",
    name: "english",
    display: "English",
    beamerPanel: "right",
  },
  fr: {
    dir: "ltr",
    code: "fr",
    locale: "fr-FR",
    name: "french",
    display: "Franca<PERSON>",
    beamerPanel: "right",
  },
  tr: {
    dir: "ltr",
    code: "tr",
    locale: "tr-TR",
    name: "turkish",
    display: "Türkçe",
    beamerPanel: "right",
  },
} as const;

const LocaleContext = React.createContext<IntlContextType>({
  setLocale: () => {
    //
  },
  activeLocale: "en-US",
  locale: SUPPORTED_LOCALES.en,
});

export function useLocale() {
  return React.useContext(LocaleContext);
}

export const LocaleProvider: React.FC<{ children: any }> = ({ children }) => {
  const [state, setState] = React.useState<Locale>(SUPPORTED_LOCALES["en"]);

  React.useEffect(() => {
    let savedLocale: Locale;
    try {
      const savedLang = JSON.parse(localStorage.getItem("locale") || "");
      if (savedLang) {
        savedLocale = SUPPORTED_LOCALES[savedLang.substring(0, 2)];
        setState(savedLocale);
      } else {
        savedLocale = SUPPORTED_LOCALES.en;
        setState(savedLocale);
      }
    } catch (error) {
      savedLocale = SUPPORTED_LOCALES.en;
      setState(savedLocale);
    }
    updateUserStorage(savedLocale);
  }, []);

  const setLocale = (newLocale: Locale) => {
    updateUserStorage(newLocale);
    setState(newLocale);
  };

  const contextValue = useMemo(
    () => ({
      setLocale,
      activeLocale: state?.locale,
      locale: state,
    }),
    [state]
  );

  return (
    <LocaleContext.Provider value={contextValue}>
      {children}
    </LocaleContext.Provider>
  );
};
