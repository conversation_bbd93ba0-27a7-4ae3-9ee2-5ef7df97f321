// @ts-nocheck
import React from "react";
import { action } from "@storybook/addon-actions";
import { Form, Field, HelperMessage, FormFooter, FormSubmit } from ".";
import { InputField } from "../InputField";
import { Checkbox } from "../Checkbox";
import { Select } from "../Select";
import { Stack } from "../Stack";
import { FieldCondition } from "./FieldCondition";
import { FieldValue } from "./FieldValue";

export default {
  title: "Components/Form/Form",
};

const OPTIONS = [
  {
    label: "COD",
    value: "cod",
  },
  {
    label: "VISA",
    value: "visa",
  },
];

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

async function handleSubmit(data) {
  action("submit")(data);
  await sleep(2000);
  const errors = {
    email: "Your email is required, please try again",
  };
  if (!data.email.trim()) return errors;
}
export const Playground = () => {
  return (
    <Form<{ username: string; email: string }> onSubmit={handleSubmit}>
      {({ formProps }) => {
        return (
          <form {...formProps}>
            <Field name="username" label="User Name" defaultValue="Ahmed Tarek">
              {({ fieldProps }) => (
                <>
                  <InputField {...fieldProps} maxLength={15} />
                  <HelperMessage>Max num of charecters is 15</HelperMessage>
                </>
              )}
            </Field>
            <Field
              name="email"
              isRequired
              label="E-Mail"
              validate={validateEmail}
            >
              {({ fieldProps, error }) => (
                <>
                  <InputField {...fieldProps} />
                  {!error && (
                    <HelperMessage>This field is required</HelperMessage>
                  )}
                </>
              )}
            </Field>

            <FieldValue name="email">
              {({ value }) => (
                <Field
                  name="confirm"
                  label="Confirm Email"
                  defaultValue={value}
                >
                  {({ fieldProps }) => <InputField {...fieldProps} />}
                </Field>
              )}
            </FieldValue>

            <Stack direction="row" align="end">
              <Field
                name="select"
                defaultValue={OPTIONS[0]}
                label="select your payment methods"
              >
                {({ fieldProps }) => (
                  <Select {...fieldProps} options={OPTIONS} />
                )}
              </Field>
              <FieldCondition when="select" equal={OPTIONS[1]}>
                <Field name="payment" margin="0">
                  {({ fieldProps }) => (
                    <InputField
                      width="300px"
                      {...fieldProps}
                      placeholder="Visa Number"
                    />
                  )}
                </Field>
              </FieldCondition>
            </Stack>

            <br />
            <Field name="isSubscribed" defaultValue>
              {({ fieldProps }) => (
                <Checkbox {...fieldProps} label="Subscribe to our news" />
              )}
            </Field>
            <FormFooter align="end">
              <FormSubmit>Submit</FormSubmit>
            </FormFooter>
          </form>
        );
      }}
    </Form>
  );
};

const validateEmail = (value: string | undefined) => {
  if (
    !/(^$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.(?:[a-zA-Z]{2,6})$)/i.test(value)
  ) {
    return "Not correct email";
  }
};
