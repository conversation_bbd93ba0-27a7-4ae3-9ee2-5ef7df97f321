import { TIconType } from "../types";

export function MessageChatCircleSolid({
  size = 20,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.05281 13.7499C1.05281 10.7775 3.40993 8.33325 6.35982 8.33325C9.30971 8.33325 11.6668 10.7775 11.6668 13.7499C11.6668 16.7223 9.30971 19.1666 6.35982 19.1666C5.75345 19.1666 5.16931 19.0625 4.62472 18.8702C4.53472 18.8385 4.47888 18.8188 4.43793 18.8053C4.43051 18.8028 4.42442 18.8008 4.41956 18.7993L4.41503 18.7998C4.38644 18.803 4.34749 18.8082 4.27527 18.8181L1.7796 19.1589C1.50956 19.1958 1.23854 19.0983 1.05389 18.8978C0.869244 18.6974 0.794297 18.4193 0.853205 18.1531L1.36266 15.8517C1.38036 15.7718 1.38983 15.7286 1.39592 15.6969C1.39626 15.6951 1.39687 15.6919 1.39687 15.6919C1.39535 15.6866 1.39343 15.6801 1.39104 15.6721C1.37732 15.6267 1.35713 15.5647 1.32433 15.4643C1.14792 14.9243 1.05281 14.3475 1.05281 13.7499Z"
        fill={color || "currentColor"}
      />
      <path
        d="M4.28978 6.97708C4.93776 6.77551 5.62846 6.66659 6.3484 6.66659C10.2763 6.66659 13.3336 9.90881 13.3336 13.7499C13.3336 14.4279 13.2383 15.0873 13.0598 15.7135C13.5115 15.6324 13.9496 15.5112 14.37 15.3537C14.4293 15.3315 14.4885 15.3088 14.5484 15.2883C14.5967 15.2939 14.6449 15.3015 14.6931 15.3085L17.3705 15.701C17.4969 15.7196 17.6403 15.7406 17.7654 15.7478C17.9038 15.7556 18.119 15.7548 18.347 15.6567C18.6317 15.5343 18.8608 15.3104 18.9898 15.0285C19.0931 14.8028 19.0989 14.5877 19.0942 14.4492C19.09 14.3239 19.0723 14.1801 19.0566 14.0533L18.7183 11.305C18.7095 11.2336 18.7049 11.1961 18.7023 11.1685C18.7169 11.1083 18.7444 11.0501 18.7664 10.9924C19.0821 10.1648 19.2546 9.26813 19.2546 8.33325C19.2546 4.18597 15.8719 0.833252 11.7107 0.833252C8.01421 0.833252 4.93208 3.47895 4.28978 6.97708Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
