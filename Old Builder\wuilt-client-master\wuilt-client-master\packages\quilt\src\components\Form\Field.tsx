// @ts-nocheck
import React from "react";
import { FieldState, FormState } from "final-form";
import { uid } from "react-uid";

import { FormContext, FormStateContext } from "./Form";
import FieldWrapper from "./styled/Field";
import { Label } from "../Label";
import { ErrorMessage } from "./Messages";
import { FieldId } from "./FieldIdContext";
import { Global } from "../../common/types";
import { BoxProps } from "../Box";

function isEvent(event: any): event is React.FormEvent<SupportedElements> {
  return Boolean(event && event.target);
}

function isFunction<T>(x: T | ((value?: T) => T)): x is (value?: T) => T {
  return typeof x === "function";
}

type SupportedElements =
  | HTMLInputElement
  | HTMLTextAreaElement
  | HTMLSelectElement;

export interface FieldProps<
  FieldValue,
  EventValue =
    | React.FormEvent<SupportedElements>
    | React.ChangeEvent<SupportedElements>
    | FieldValue
> {
  id: string;
  isRequired: boolean;
  isDisabled?: boolean;
  isInvalid: boolean;
  dataTest?: string;
  // This can be either an event or value as `onChange` might not be applied
  // directly to a DOM element. For example, it might be a react-select
  onChange: (value: EventValue) => void;
  onBlur: () => void;
  onFocus: () => void;
  value: FieldValue;
  name: string;
  "aria-invalid": "true" | "false";
  "aria-labelledby": string;
}

export interface Meta {
  dirty: boolean;
  dirtySinceLastSubmit: boolean;
  submitFailed: boolean;
  submitting: boolean;
  touched: boolean;
  valid: boolean;
  error?: string;
  submitError?: boolean;
  modified?: boolean;
  submitSucceeded?: boolean;
}

interface Props<FieldValue, EventValue> extends Global {
  /* Children to render in the field. Called with props for the field component and other information about the field. */
  children: (args: {
    fieldProps: FieldProps<FieldValue, EventValue>;
    error?: string;
    setError?: (error?: string) => void;
    valid: boolean;
    meta: Meta;
  }) => React.ReactNode;
  /* The default value of the field. If a function is provided it is called with the current default value of the field. */
  defaultValue?:
    | FieldValue
    | ((currentDefaultValue?: FieldValue) => FieldValue);
  /* Passed to the ID attribute of the field. Randomly generated if not specified */
  id?: string;
  /* Whether the field is required for submission */
  isRequired?: boolean;
  /* Whether the field is disabled. If the parent Form component is disabled, then the field will always be disabled */
  isDisabled?: boolean;
  /* Label displayed above the field */
  label?: React.ReactNode;
  /* The name of the field */
  name: string;
  /* The margin field */
  margin?: string;
  /* Filed Wrapper box styles */
  boxStyles?: BoxProps;
  /* whether this field is error or not */
  isError?: boolean;
  /* whether to show error message or not */
  hideErrorMessage?: boolean;
  /* whether to reintialize the field value after submit or keep its last updated value */
  resetAfterSubmit?: boolean;
  /* Given what onChange was called with and the current field value return the next field value */
  transform?: (event: EventValue, current: FieldValue) => FieldValue;
  /* validates the current value of field */
  validate?: (
    value: FieldValue | undefined,
    formState: object,
    fieldState: Meta
  ) => React.ReactNode | Promise<React.ReactNode>;
}

interface State<
  FieldValue,
  SupportedElements,
  EventValue = React.FormEvent<SupportedElements> | FieldValue
> {
  fieldProps: {
    onChange: (value: EventValue) => void;
    onBlur: () => void;
    onFocus: () => void;
    value: FieldValue;
  };
  error?: string;
  valid: boolean;
  meta: Meta;
}

function usePreviousRef<T>(current: T): React.MutableRefObject<T> {
  const ref = React.useRef(current);

  // will be updated on the next render
  React.useEffect(() => {
    ref.current = current;
  });

  // return the existing current (pre render)
  return ref;
}

function isShallowEqual<FieldValue>(
  previousValue: FieldValue,
  currentValue: FieldValue
) {
  if (previousValue === currentValue) {
    return true;
  }

  // not checking functions
  if (
    typeof previousValue === "function" &&
    typeof currentValue === "function"
  ) {
    return true;
  }

  if (Array.isArray(previousValue) && Array.isArray(currentValue)) {
    return JSON.stringify(previousValue) === JSON.stringify(currentValue);
  }

  if (typeof previousValue === "object" && typeof currentValue === "object") {
    return JSON.stringify(previousValue) === JSON.stringify(currentValue);
  }

  return false;
}

function Field<
  FieldValue = string,
  EventValue = React.FormEvent<SupportedElements> | FieldValue
>(props: Props<FieldValue, EventValue>) {
  const registerField = React.useContext(FormContext);
  const formStateDisable = React.useContext(FormStateContext).isDisabled;
  const fromStateSubmitting = React.useContext(FormStateContext).isSubmitting;
  const isDisabled =
    props.isDisabled || formStateDisable || fromStateSubmitting;

  const defaultValue = isFunction(props.defaultValue)
    ? props.defaultValue()
    : props.defaultValue;

  const [state, setState] = React.useState<
    State<FieldValue, SupportedElements, EventValue>
  >({
    fieldProps: {
      onChange: () => {},
      onBlur: () => {},
      onFocus: () => {},
      value: defaultValue,
    },
    error: undefined,
    valid: false,
    meta: {
      dirty: false,
      dirtySinceLastSubmit: false,
      touched: false,
      valid: false,
      submitting: false,
      submitFailed: false,
      error: undefined,
      submitError: undefined,
      modified: false,
      submitSucceeded: false,
    },
  });

  const latestPropsRef = usePreviousRef(props);
  const latestStateRef = usePreviousRef(state);

  /**
   * HACK: defaultValue can potentially be an array or object which cannot be
   * passed directly into a `useEffect` dependency array, since it will trigger
   * the hook every time.
   */
  const hasDefaultValueChanged = isShallowEqual(
    latestPropsRef.current.defaultValue,
    props.defaultValue
  );

  React.useEffect(() => {
    function fieldStateToMeta(
      value: Partial<FieldState<FieldValue>> = {}
    ): Meta {
      return {
        dirty: value.dirty || false,
        dirtySinceLastSubmit: value.dirtySinceLastSubmit || false,
        touched: value.touched || false,
        valid: value.valid || false,
        submitting: value.submitting || false,
        submitFailed: value.submitFailed || false,
        error: value.error,
        submitError: value.submitError,
        modified: value.modified,
        submitSucceeded: value.submitSucceeded,
      };
    }

    const unregister = registerField<FieldValue>(
      latestPropsRef.current.name,
      latestPropsRef.current.defaultValue,
      (fieldState) => {
        /** Do not update dirtySinceLastSubmit until submission has finished. */
        const modifiedDirtySinceLastSubmit = fieldState.submitting
          ? latestStateRef.current.meta.dirtySinceLastSubmit
          : fieldState.dirtySinceLastSubmit;

        /** Do not update submitFailed until submission has finished. */
        const modifiedSubmitFailed = fieldState.submitting
          ? latestStateRef.current.meta.submitFailed
          : fieldState.submitFailed;

        /** Do not use submitError if the value has changed. */
        const modifiedSubmitError = modifiedDirtySinceLastSubmit // && latestPropsRef.current.validate
          ? undefined
          : fieldState.submitError;
        const modifiedError =
          modifiedSubmitError ||
          ((fieldState.touched || fieldState.dirty) && fieldState.error);

        /**
         * If there has been a submit error, then use logic in modifiedError to determine validity,
         * so we can determine when there is a submit error which we do not want to display
         * because the value has been changed.
         */
        const modifiedValid = modifiedSubmitFailed
          ? modifiedError === undefined
          : fieldState.valid;

        function getTransform(
          eventOrValue: EventValue,
          currentValue: FieldValue
        ): FieldValue | undefined {
          if (latestPropsRef.current.transform) {
            return latestPropsRef.current.transform(eventOrValue, currentValue);
          }

          if (isEvent(eventOrValue)) {
            const { target } = eventOrValue;

            // @ts-ignore
            if (target?.type === "checkbox") {
              if ((target as unknown as HTMLInputElement).checked) {
                // @ts-ignore boolean is FieldValue
                return target.value || true;
              }
              // @ts-ignore
              return target.value ? undefined : false;
            }
            if (target) {
              // @ts-ignore
              return target.value;
            }
          } else {
            // @ts-ignore
            return eventOrValue;
          }
        }
        setState({
          fieldProps: {
            onChange: (e) => {
              fieldState.change(getTransform(e, fieldState.value));
            },
            onBlur: fieldState.blur,
            onFocus: fieldState.focus,
            value: fieldState.value,
          },
          error: modifiedError,
          /**
           * The following parameters are optionally typed in final-form to indicate that not all parameters need
           * to be subscribed to. We cast them as booleans (using || false), since this is what they are semantically.
           */
          valid: modifiedValid || false,
          meta: fieldStateToMeta(fieldState),
        });
      },
      {
        dirty: true,
        dirtySinceLastSubmit: true,
        touched: true,
        valid: true,
        submitting: true,
        submitFailed: true,
        value: true,
        error: true,
        submitError: true,
        modified: true,
        initial: true,
        submitSucceeded: true,
      },
      {
        getValidator: () =>
          function validate(
            value: FieldValue,
            formState: object,
            fieldState?: FieldState<FieldValue>
          ) {
            const supplied = latestPropsRef.current.validate || localValidate;

            if (supplied && fieldState) {
              return supplied(value, formState, fieldStateToMeta(fieldState));
            }
          },
      }
    );
    return unregister;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    latestPropsRef,
    latestStateRef,
    registerField,
    props.name,
    hasDefaultValueChanged,
  ]);

  const localValidate = (
    value: FieldValue,
    formState: FormState<FormData, Partial<FormData>>,
    fieldState?: FieldState<FieldValue> | Meta
  ) => {
    if (
      props.isRequired &&
      typeof value === "string" &&
      !value?.trim() &&
      (fieldState.modified || formState.submitting)
    ) {
      return "REQUIRED";
    }
  };

  React.useEffect(() => {
    if (props.resetAfterSubmit) {
      state.fieldProps.onChange(defaultValue as any);
      setState((prev) => ({
        ...prev,
        fieldProps: { ...prev.fieldProps, value: defaultValue },
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.meta.submitSucceeded]);

  const fieldId = React.useMemo(
    () => (props.id ? props.id : `${props.name}-${uid({ id: props.name })}`),
    [props.id, props.name]
  );

  const extendedFieldProps = {
    ...state.fieldProps,
    name: props.name,
    isDisabled,
    isInvalid: Boolean(state.error),
    isRequired: Boolean(props.isRequired),
    "aria-invalid": (state.error ? "true" : "false") as "true" | "false",
    "aria-labelledby": `${fieldId}-label ${fieldId}-helper ${fieldId}-valid ${fieldId}-error`,
    id: fieldId,
    dataTest: props.dataTest,
  };

  const handleSetError = (error?: string) => {
    setState((prev) => ({
      ...prev,
      error,
      meta: { ...prev.meta, error },
      fieldProps: { ...prev.fieldProps },
    }));
  };

  return (
    <FieldWrapper
      flex="1 1 100%"
      width="100%"
      m={props.margin || "0 0 20px 0"}
      {...props.boxStyles}
      isError={props.isError ?? (!!state.error && state.meta.touched)}
    >
      {props.label && (
        <Label
          required={props.isRequired}
          id={`${fieldId}-label`}
          htmlFor={fieldId}
        >
          {props.label}
        </Label>
      )}
      <FieldId.Provider value={fieldId}>
        {props.children({
          fieldProps: extendedFieldProps,
          error: state.error,
          setError: handleSetError,
          valid: state.valid,
          meta: state.meta,
        })}
      </FieldId.Provider>
      {!props.hideErrorMessage && state.error && state.meta.touched && (
        <ErrorMessage>{state.error}</ErrorMessage>
      )}
    </FieldWrapper>
  );
}

Field.defaultProps = {
  defaultValue: "",
  isDisabled: false,
};

export { Field };
