import {
  Box,
  InputField,
  ShiftLeftIcon,
  ShiftTopIcon,
  Stack,
  Text,
} from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import {
  TWidgetAppearance,
  TWidgetSettings,
} from "../../../../../shared/types";
interface ShiftProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function Shift({ appearance, update }: ShiftProps) {
  const horizontal = appearance.display.shift.horizontal;
  const vertical = appearance.display.shift.vertical;
  return (
    <Box width="100%">
      <Text style={{ color: "#1D2939" }} fontSize="sm" fontWeight="medium">
        <FormattedMessage defaultMessage="Shift" id="adUgpY" />
      </Text>
      <Stack height="40px" mt="6px" direction="row">
        <Box width="100%">
          <InputField
            borderRadius="8px"
            value={horizontal.replace("px", "")}
            onChange={(value) => {
              update({
                appearance: {
                  ...appearance,
                  display: {
                    ...appearance.display,
                    shift: {
                      ...appearance.display.shift,
                      horizontal: `${value}px`,
                    },
                  },
                },
              });
            }}
            prefix={<ShiftLeftIcon className="position-and-shift-icon" />}
            prefixBorder
            hideArrows
            width="100px"
            minWidth="40px"
            type="number"
          />
        </Box>
        <Box width="100%">
          <InputField
            borderRadius="8px"
            value={vertical.replace("px", "")}
            onChange={(value) => {
              update({
                appearance: {
                  ...appearance,
                  display: {
                    ...appearance.display,
                    shift: {
                      ...appearance.display.shift,
                      vertical: `${value}px`,
                    },
                  },
                },
              });
            }}
            prefix={<ShiftTopIcon className="position-and-shift-icon" />}
            prefixBorder
            hideArrows
            width="100px"
            minWidth="40px"
            type="number"
          />
        </Box>
      </Stack>
    </Box>
  );
}

export default Shift;
