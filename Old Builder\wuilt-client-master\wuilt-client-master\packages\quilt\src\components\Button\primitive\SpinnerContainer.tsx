import React from "react";
import styled from "styled-components";

const StyledContainer = styled.div`
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
`;

interface SpinnerContainerProps {
  children: React.ReactNode;
}

const SpinnerContainer: React.FC<SpinnerContainerProps> = ({ children }) => {
  return <StyledContainer>{children}</StyledContainer>;
};

export default SpinnerContainer;
