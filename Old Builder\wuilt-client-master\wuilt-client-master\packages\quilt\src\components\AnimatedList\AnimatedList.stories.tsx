import React from "react";
import styled from "styled-components";
import { Button } from "../Button";
import { AnimatedList } from "./AnimatedList";

export default {
  title: "Components/AnimatedList",
  component: AnimatedList,
};

const ITEMS = [
  { label: "item 1", id: 1 },
  { label: "item 2", id: 2 },
  { label: "item 3", id: 3 },
  { label: "item 4", id: 4 },
];

const StyledItem = styled.li`
  cursor: pointer;
  font-size: 18px;
`;

export const Playground = () => {
  const [items, setItems] = React.useState(ITEMS);
  return (
    <div>
      <ul>
        <AnimatedList ids={items.map((item) => item.id)}>
          {items.map((item) => (
            <StyledItem
              key={item.id}
              onClick={() =>
                setItems((prev) => prev.filter((curr) => curr.id !== item.id))
              }
            >
              {item.label}
            </StyledItem>
          ))}
        </AnimatedList>
      </ul>
      <br />
      <Button
        size="small"
        onClick={() =>
          setItems((prev) => {
            return prev.length === 0
              ? [{ label: "item 1", id: 1 }]
              : [
                  ...prev,
                  {
                    label: `item ${+prev[prev.length - 1].id + 1}`,
                    id: +prev[prev.length - 1].id + 1,
                  },
                ];
          })
        }
      >
        Add Item
      </Button>
    </div>
  );
};
