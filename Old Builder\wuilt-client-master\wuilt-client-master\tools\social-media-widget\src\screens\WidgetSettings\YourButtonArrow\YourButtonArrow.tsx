import React, { useState } from "react";
import { useWidget } from "../../../context/widget-provider";
import styled, { css } from "styled-components";
import { TWidgetSettings, WidgetPositionEnum } from "../../../shared/types";
import { CloseIcon, Stack, Text } from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";

const ArrowSvg = ({ rotate }: { rotate: boolean }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="50"
    height="50"
    viewBox="0 0 40 40"
    fill="none"
    rotate={rotate ? 180 : 0}
    transform={rotate ? "scale(-1, 1)" : undefined}
  >
    <path
      d="M26.7428 5.98537L26.7907 6.02789L26.7836 6.02242C26.768 6.01043 26.7552 6.0006 26.7428 5.98537Z"
      fill="#15B79E"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1.9082 5.89733L1.90798 5.9003L1.90815 5.89922L1.9082 5.89733ZM1.9082 5.89733L1.92824 7.52155L1.91315 5.83386C1.91336 5.82273 1.91336 5.81159 1.91336 5.80046C1.91336 5.81987 1.91201 5.8379 1.91067 5.85593C1.90965 5.86953 1.90864 5.88313 1.9082 5.89733ZM26.7907 6.02789L26.7428 5.98537C26.7552 6.0006 26.768 6.01043 26.7836 6.02242L26.7907 6.02789ZM28.1521 20.9643C28.1633 20.9538 28.1747 20.9433 28.1869 20.9329L28.1521 20.9643Z"
      fill="#15B79E"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.35241 0.197906C3.12688 0.674953 2.92822 1.16864 2.75639 1.66788C2.53086 2.36126 2.36439 3.06576 2.19793 3.78133L2.19314 3.80988C2.08028 4.48288 1.96821 5.15109 1.91315 5.83386C1.91336 5.82273 1.91336 5.81159 1.91336 5.80046C1.91336 5.81987 1.91201 5.8379 1.91067 5.85593C1.90965 5.86953 1.90864 5.88313 1.9082 5.89733L1.90798 5.9003L1.90815 5.89922L1.90798 5.91141V5.92805C1.88113 6.49385 1.88113 7.05409 1.90798 7.61434C1.94557 8.12467 2.01 8.62391 2.10128 9.12315C2.2033 9.62239 2.33754 10.1105 2.49863 10.5987C2.49057 10.5793 2.48386 10.5598 2.47716 10.5404C2.47045 10.521 2.46375 10.5016 2.45569 10.4822C2.7188 11.2477 3.02484 11.991 3.3685 12.7177C3.78196 13.5442 4.2384 14.343 4.7324 15.114C4.81011 15.2249 4.88771 15.336 4.96532 15.4471C5.65997 16.4411 6.35552 17.4365 7.13798 18.3646C7.48163 18.764 7.84141 19.1523 8.22265 19.5128C8.94843 20.1626 9.74397 20.7359 10.5346 21.3056L10.7947 21.4931C10.9719 21.6068 11.1477 21.7219 11.3236 21.837C11.4995 21.9521 11.6753 22.0672 11.8525 22.181C12.3895 22.5249 12.9371 22.8466 13.501 23.1406C13.4815 23.1272 13.4602 23.1159 13.438 23.1041C13.4235 23.0963 13.4085 23.0884 13.3936 23.0796C13.9252 23.3514 14.4621 23.5955 15.0206 23.8063C15.622 24.017 16.2341 24.1724 16.857 24.311C17.0286 24.3443 17.2008 24.3776 17.3725 24.4053C17.1705 24.1932 16.977 23.968 16.784 23.7434C16.6691 23.6097 16.5544 23.4762 16.4381 23.3459C16.3009 23.1883 16.169 23.0224 16.0368 22.856C15.9829 22.788 15.9288 22.72 15.8743 22.6525C15.8248 22.5928 15.7738 22.5332 15.7225 22.4732C15.6069 22.338 15.4898 22.2011 15.3857 22.0589C15.2785 21.9081 15.1681 21.7598 15.0578 21.6117C14.8477 21.3297 14.638 21.0481 14.4514 20.7498C14.3734 20.6281 14.2936 20.5065 14.2136 20.3844C14.0128 20.078 13.8103 19.7691 13.6299 19.4518C13.5661 19.3403 13.5021 19.229 13.4381 19.1178C13.114 18.5546 12.791 17.9933 12.513 17.4049C11.9492 16.2234 11.5464 14.942 11.3907 13.6385C11.2833 12.7232 11.1813 11.8135 11.2242 10.8871C11.2511 10.2659 11.2994 9.65014 11.38 9.03441C11.525 7.93054 11.729 6.81557 12.1478 5.77826C12.6579 4.51353 13.4043 3.32647 14.4192 2.42784C15.4448 1.51812 16.6905 0.935665 18.0222 0.69714C19.3485 0.458614 20.7607 0.491909 22.0601 0.896843C22.64 1.07435 23.2092 1.30178 23.7516 1.59023C24.0198 1.73989 24.2935 1.88401 24.5671 2.02812C25.4262 2.48853 26.2538 3.06575 27.0055 3.69811L27.123 3.79731C27.7929 4.36304 28.4632 4.92903 29.0567 5.58413C30.0125 6.63807 30.7535 7.85288 31.3174 9.16753C31.9134 10.5543 32.3483 12.0132 32.4611 13.5275C32.5738 15.0141 32.5362 16.5618 32.1174 17.9985C31.7469 19.2854 31.167 20.5058 30.3025 21.5153C29.8085 22.0978 29.2286 22.6247 28.6272 23.0962C28.1009 23.5067 27.5586 23.9061 26.9948 24.25C25.7544 25.0155 24.4174 25.598 23.0213 25.9807L22.9382 26.0026C22.693 26.0673 22.4512 26.1311 22.2051 26.186C22.4209 26.3521 22.6367 26.5147 22.8551 26.6794L23.032 26.8128C23.3805 27.0564 23.7343 27.3001 24.0881 27.5438L24.0898 27.545C24.3419 27.7173 24.6017 27.8791 24.8621 28.0412C24.9788 28.1139 25.0958 28.1867 25.2121 28.2606C25.8284 28.6561 26.4731 29.0096 27.1145 29.3614L27.1163 29.3624C27.212 29.4148 27.3075 29.4672 27.4029 29.5197C27.5747 29.603 27.7452 29.6862 27.9157 29.7694C28.0862 29.8526 28.2567 29.9358 28.4285 30.019C28.6386 30.1186 28.8575 30.2023 29.0768 30.286C29.1994 30.3329 29.322 30.3797 29.4433 30.4295C30.1951 30.6902 30.9522 30.9453 31.7147 31.1783C31.7442 31.145 31.7724 31.1118 31.8006 31.0785C31.8288 31.0452 31.857 31.0119 31.8865 30.9786C32.0959 30.7235 32.2946 30.4683 32.4772 30.1965C32.6436 29.9302 32.794 29.6584 32.9389 29.3811C33.0625 29.1204 33.1752 28.8541 33.2772 28.5767C33.3796 28.2562 33.4605 27.9282 33.5417 27.5993C33.6009 27.359 33.6603 27.1181 33.7283 26.8793C33.8894 26.3024 34.131 25.5314 34.7593 25.3539C35.2962 25.1986 35.8224 25.5369 36.0318 26.0473C36.1716 26.3901 36.2616 26.7512 36.351 27.1098C36.3715 27.1922 36.392 27.2745 36.4131 27.3564C36.5269 27.806 36.6725 28.2462 36.8165 28.6813L36.8534 28.7931C37.0306 29.2535 37.2132 29.6973 37.4118 30.1466C37.7018 30.7235 38.0186 31.2837 38.3354 31.844C38.4911 32.1213 39 33 39 33C39.3238 33.4017 40.007 34.4878 40.0037 34.5L38.421 35.2107C37.6672 35.4873 36.9145 35.7636 36.1553 36.0265C35.3601 36.3019 34.5534 36.5537 33.7481 36.8051C33.5965 36.8525 33.4448 36.8998 33.2934 36.9473C32.6424 37.1521 31.9837 37.3327 31.3244 37.5134C31.0374 37.5921 30.7502 37.6708 30.4636 37.7516C30.3075 37.7953 30.1512 37.8403 29.9947 37.8855C29.3164 38.0811 28.6325 38.2782 27.9345 38.3729C27.5425 38.4283 27.1935 38.2287 26.9841 37.8958C26.9841 37.8903 26.9787 37.8847 26.9787 37.8847C26.78 37.5685 26.8069 37.0693 27.0485 36.7864C27.1488 36.6681 27.2585 36.5645 27.3683 36.4609C27.4232 36.4092 27.4781 36.3574 27.5317 36.3038C27.5793 36.2565 27.6274 36.2092 27.6758 36.1617C27.7748 36.0644 27.8747 35.9662 27.9721 35.8656C28.1459 35.6902 28.3076 35.499 28.4685 35.3088C28.5213 35.2464 28.5741 35.1839 28.6272 35.1223C28.6991 35.0297 28.7715 34.9376 28.8437 34.8457C29.0356 34.6014 29.2263 34.3585 29.4058 34.1127C29.5082 33.9745 29.6098 33.8355 29.7115 33.6963C29.8712 33.4778 30.0311 33.259 30.1951 33.0421C30.2246 33.0033 30.2555 32.9631 30.2864 32.9229C30.3173 32.8826 30.3481 32.8424 30.3777 32.8036C29.9696 32.6705 29.5561 32.5318 29.148 32.3876C28.9227 32.3044 28.6968 32.2211 28.4768 32.1324C28.3909 32.0973 28.3038 32.064 28.2167 32.0307C28.0425 31.9642 27.8682 31.8976 27.7036 31.8162C27.58 31.7564 27.4556 31.6978 27.3311 31.6392C27.0175 31.4917 26.7022 31.3434 26.3988 31.1728C26.3167 31.1274 26.2347 31.0823 26.1527 31.0371L26.149 31.0351C25.7709 30.827 25.3926 30.6188 25.0188 30.3962C24.0684 29.8359 23.1502 29.2368 22.2373 28.61C21.3674 28.0109 20.5513 27.3453 19.7405 26.663C19.6922 26.6187 19.6386 26.5744 19.585 26.5301C19.2843 26.5412 18.9833 26.5354 18.6773 26.5188C17.9577 26.4799 17.2543 26.3856 16.5509 26.2414C15.9066 26.1083 15.2568 25.9585 14.6393 25.7366C13.9574 25.4926 13.3076 25.2097 12.6633 24.8824C12.1961 24.6439 11.7236 24.3998 11.2833 24.1114C10.7962 23.7962 10.3038 23.4754 9.81132 23.1547L9.80669 23.1517C9.48448 22.9409 9.17659 22.7104 8.8687 22.4798C8.71481 22.3646 8.56088 22.2494 8.4052 22.1366C7.47625 21.4654 6.6225 20.6943 5.86538 19.8179C5.20492 19.0469 4.59277 18.237 4.00748 17.4049C3.92674 17.2888 3.84565 17.1729 3.76459 17.0571C3.39284 16.5261 3.02161 15.9959 2.68658 15.4357C1.9241 14.171 1.2529 12.8231 0.775001 11.4086C0.538738 10.6985 0.329325 9.98295 0.205824 9.24518C0.0823225 8.52961 0.012515 7.80295 0.00177576 7.08183C-0.00896346 6.32743 0.0286133 5.56748 0.125266 4.81308C0.162828 4.5082 0.216488 4.20331 0.270148 3.89843C0.363773 3.3396 0.492773 2.79057 0.622773 2.23744L0.635386 2.18377C0.758887 1.63461 0.93608 1.09655 1.13476 0.575119C1.94684 -0.236969 2.95156 -0.0147247 3.35241 0.197906ZM19.2787 23.5677C19.6008 23.895 19.9284 24.2057 20.2774 24.5107C20.6963 24.4664 21.1151 24.4053 21.5285 24.3221C22.1944 24.1779 22.8441 23.9949 23.4884 23.7785C23.4854 23.7816 23.4807 23.783 23.4753 23.7846C23.471 23.7858 23.4663 23.7872 23.4616 23.7896L23.5099 23.773C23.5132 23.7695 23.5166 23.7682 23.5211 23.7664L23.5278 23.7637L23.5314 23.7619C24.0952 23.5566 24.6483 23.3181 25.1852 23.0463C25.7276 22.7579 26.2484 22.4361 26.7585 22.0811C26.745 22.0871 26.7322 22.0945 26.7198 22.1026C27.2128 21.7503 27.6913 21.3782 28.1424 20.973C28.4102 20.7255 28.6682 20.4677 28.9117 20.1951C29.0782 19.9899 29.2286 19.7846 29.3736 19.5739C29.5185 19.352 29.6474 19.1245 29.7655 18.8916C29.9266 18.5476 30.0662 18.1982 30.1951 17.8431C30.2971 17.5159 30.3884 17.1886 30.4582 16.8502C30.5334 16.4508 30.5817 16.0514 30.6139 15.6465C30.6408 15.0863 30.6408 14.5205 30.6139 13.9547V13.9325V13.5C30.5906 13.2038 30.5589 13.0742 30.5176 12.9054C30.4925 12.8026 30.4638 12.6852 30.4314 12.5069C30.3401 12.0686 30.2273 11.6471 30.0931 11.2255C29.8729 10.5931 29.6259 9.97185 29.336 9.36722C29.1104 8.91791 28.8688 8.48525 28.6003 8.06367C28.3855 7.74749 28.16 7.44239 27.913 7.15394C27.5425 6.74346 27.1505 6.35517 26.7371 5.97797L26.7428 5.98537C26.1452 5.45709 25.5223 4.9504 24.8738 4.4858C24.8925 4.49683 24.9098 4.50786 24.9265 4.51888C24.4858 4.22023 24.0349 3.93301 23.569 3.68702C23.4482 3.62323 23.3287 3.55944 23.2092 3.49565C23.0897 3.43185 22.9703 3.36807 22.8495 3.30428C22.5488 3.1656 22.2481 3.02692 21.9366 2.91043C21.652 2.81613 21.3567 2.72738 21.056 2.66081C20.7499 2.60534 20.4493 2.56097 20.1378 2.53324C19.8156 2.5166 19.4935 2.5166 19.1659 2.53324C18.8384 2.56097 18.5162 2.61089 18.194 2.67191C17.9202 2.73293 17.657 2.80504 17.3939 2.8938C17.1738 2.977 16.959 3.07129 16.7496 3.17669C16.5509 3.28208 16.363 3.39304 16.175 3.52062C15.9978 3.65375 15.826 3.78688 15.6542 3.93665C15.4823 4.09752 15.3159 4.26946 15.1548 4.44697C14.9722 4.66885 14.8058 4.89074 14.6447 5.12926C14.4675 5.40662 14.301 5.70062 14.1507 6.00016C14.0218 6.27197 13.909 6.54377 13.807 6.82667C13.662 7.26489 13.5493 7.70865 13.4526 8.16352C13.3077 8.9512 13.2218 9.74442 13.1573 10.5432C13.1251 11.1256 13.1251 11.697 13.1627 12.2739C13.2057 12.8397 13.2862 13.4055 13.3882 13.9602C13.4902 14.4539 13.6137 14.9365 13.7694 15.4135C13.909 15.8074 14.0701 16.1957 14.2473 16.5729C14.7843 17.6546 15.4179 18.6808 16.0676 19.6959L16.5026 20.295C16.5419 20.3502 16.5809 20.4066 16.6198 20.4629C16.7138 20.5988 16.8082 20.7353 16.9107 20.8608C17.0738 21.0623 17.2384 21.2639 17.4038 21.4664L17.4045 21.4672L17.4069 21.4702C17.5455 21.6397 17.6845 21.8099 17.8235 21.9813L19.2787 23.5677ZM28.1521 20.9643C28.1633 20.9538 28.1747 20.9433 28.1869 20.9329L28.1521 20.9643Z"
      fill="#15B79E"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M28.1521 20.9643C28.1633 20.9538 28.1747 20.9433 28.1869 20.9329L28.1521 20.9643Z"
      fill="#15B79E"
    />
  </svg>
);

interface YourButtonArrowProps {}

const YourButtonArrow: React.FC<YourButtonArrowProps> = () => {
  const [hideArrow, setHideArrow] = useState(false);
  const widget = useWidget();
  const isLeft =
    widget?.settings?.appearance?.display?.position === WidgetPositionEnum.Left;

  if (!widget?.settings?.apps?.length) return null;

  return (
    <StyledArrowPosition hideArrow={hideArrow} settings={widget.settings}>
      <Stack align="baseline" spacing="none" direction="row">
        <Text
          color="primary"
          align="center"
          fontSize="md"
          fontWeight="semiBold"
          width="min-content"
        >
          <FormattedMessage defaultMessage="Your Button" id="gkHlau" />
        </Text>
        <Stack
          cursor="pointer"
          border="1px solid #00A991"
          height="16px"
          width="16px"
          borderRadius="50%"
          justify="center"
          align="center"
          onClick={() => setHideArrow(true)}
        >
          <CloseIcon viewBox="0 0 18 20" size="xxs" color="primary" />
        </Stack>
      </Stack>
      <ArrowSvg rotate={isLeft} />
    </StyledArrowPosition>
  );
};

export { YourButtonArrow };

/**
 * Styles
 */

const positionsStyles = ({ settings }: { settings: TWidgetSettings }) => {
  const withText = settings?.appearance?.content?.withText;
  const shift = settings?.appearance?.display?.shift;
  const size = settings?.appearance?.style?.size;
  const height = size;
  const width = `calc(${size} * ${withText ? 3 : 1})`;
  const isLeft =
    settings.appearance.display.position === WidgetPositionEnum.Left;
  const horizontalValue = `calc(${shift.horizontal} + ${width} - 10px );`;

  return css`
    position: fixed;
    bottom: calc(${shift.vertical} + ${height} + 15px);
    left: ${isLeft && horizontalValue};
    right: ${!isLeft && horizontalValue};
  `;
};

const StyledArrowPosition = styled.div<{
  settings: TWidgetSettings;
  hideArrow: boolean;
}>`
  display: ${({ hideArrow }) => (hideArrow ? "none" : "block")};
  ${positionsStyles};
`;
