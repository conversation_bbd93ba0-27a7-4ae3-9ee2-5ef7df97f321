import React from "react";
import styled, { css } from "styled-components";
import { TAppSettings, TWidgetSettings } from "../../shared/types";
import { AppsIcon } from "../../shared/IconsList";
import { CloseIcon, getWhatsappAgentRedirect } from "./utils";

interface WhatsAppWidgetProps {
  widget: TWidgetSettings;
  app: TAppSettings | undefined;
  isOpen: boolean;
  onClose: () => void;
}

const WhatsAppWidget: React.FC<WhatsAppWidgetProps> = ({
  widget,
  app,
  isOpen,
  onClose,
}) => {
  return (
    <StyledContainer
      widgetSize={widget?.appearance?.style?.size}
      isOpen={isOpen}
    >
      <StyledHeaderContainer background={app?.background?.color!}>
        <StyledRow>
          <StyledIcon>
            <AppsIcon.Whatsapp color="white" />
          </StyledIcon>
          <StyledColumn>
            <StyledTitle>{app?.whatsAppSettings?.form?.title}</StyledTitle>
            <StyledSubTitle>
              {app?.whatsAppSettings?.form?.subtitle}
            </StyledSubTitle>
          </StyledColumn>
        </StyledRow>
        <StyledCloseButton onClick={onClose}>
          <CloseIcon size="20px" />
        </StyledCloseButton>
      </StyledHeaderContainer>
      <StyledBodyContainer>
        {app?.whatsAppSettings?.agents?.map((agent, i) => (
          <StyledAgent
            key={i}
            target="_blank"
            rel="noopener"
            href={getWhatsappAgentRedirect(agent)}
          >
            <StyledRow>
              <StyledAgentImage src={agent.image} />
              <StyledColumn>
                <StyledAgentName>{agent.name}</StyledAgentName>
                <StyledAgentPosition>{agent.position}</StyledAgentPosition>
              </StyledColumn>
            </StyledRow>
          </StyledAgent>
        ))}
      </StyledBodyContainer>
    </StyledContainer>
  );
};

export default WhatsAppWidget;

/**
 * Styles
 */

const StyledContainer = styled.div<{ widgetSize: string; isOpen: boolean }>`
  direction: ltr;
  position: absolute;
  bottom: ${({ widgetSize }) => `calc(${widgetSize} + 20px)`};
  width: 310px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03),
    0px 12px 16px -4px rgba(16, 24, 40, 0.08);

  visibility: hidden;
  opacity: 0;
  width: 0;
  height: 0;
  transform: translateY(10px);
  transition: opacity 200ms ease-in-out, transform 200ms ease-in-out;

  ${({ isOpen }) =>
    isOpen &&
    css`
      visibility: visible;
      opacity: 1;
      width: 310px;
      height: auto;
      transform: translateY(0);
    `}
`;

const StyledHeaderContainer = styled.div<{ background: string }>`
  padding: 16px;
  background: ${({ background }) => background};
  display: flex;
  justify-content: space-between;
`;

const StyledIcon = styled.div`
  flex-shrink: 0;
`;

const StyledRow = styled.div`
  display: flex;
`;

const StyledColumn = styled.div`
  padding-inline-start: 10px;
`;

const StyledTitle = styled.h4`
  margin: 0;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
`;

const StyledSubTitle = styled.p`
  padding-top: 2px;
  margin: 0;
  color: #dcfae6;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
`;

const StyledCloseButton = styled.button`
  cursor: pointer;
  height: min-content;
  color: white;
  background: none;
  border: none;
  :active,
  :focus {
    outline: none;
  }
`;

const StyledBodyContainer = styled.div`
  background-color: white;
  height: 240px;
  overflow-y: scroll;
`;

const StyledAgent = styled.a`
  cursor: pointer;
  display: block;
  padding: 16px;
  text-decoration: none;

  :hover {
    background-color: #eee;
  }
`;

const StyledAgentImage = styled.img`
  width: 48px;
  height: 48px;
  border-radius: 200px;
  overflow: hidden;
`;

const StyledAgentName = styled.p`
  margin: 0;
  color: #344054;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
`;

const StyledAgentPosition = styled.p`
  margin: 0;
  color: #98a2b3;
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
`;
