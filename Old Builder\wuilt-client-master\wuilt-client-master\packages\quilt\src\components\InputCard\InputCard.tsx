import React from "react";
import styled from "styled-components";
import { GeneralInputWrapper, StyledInput } from "../InputField/InputField";
import { Stack } from "../Stack";
import { CreditCardEmptyIcon } from "../icons";
import { Field, FieldProps } from "../Form";

type CardValuesType = {
  number: string;
  expiry: { mm: string; yy: string };
  cvc: string;
};
export interface InputCardProps {
  isDisabled?: boolean;
  backgroundColor?: string;
  placeHolder?: string;
  isError?: boolean;
  dataTest?: string;
  onChange?: (cardValues: CardValuesType) => void;
}

const InputCard: React.FC<InputCardProps> = ({
  isDisabled,
  backgroundColor,
  placeHolder,
  isError,
  dataTest,
  onChange,
}) => {
  const [displayedCardNumber, setDisplayedCardNumber] = React.useState("");
  const [card, setCard] = React.useState({
    number: "",
    MM: "",
    YY: "",
    cvc: "",
  });

  const monthRef = React.useRef<HTMLInputElement>(null);
  const yearRef = React.useRef<HTMLInputElement>(null);
  const cvcRef = React.useRef<HTMLInputElement>(null);

  const handleCardNumber = (value: string, fieldProps: FieldProps<string>) => {
    if (!value) {
      setDisplayedCardNumber("");
      setCard((prev) => ({ ...prev, number: "" }));
      fieldProps.onChange("");
    } else if (card.number !== getNumber(value)) {
      setDisplayedCardNumber(addHyphen(getNumber(value) || "") || "");
      setCard((prev) => ({ ...prev, number: getNumber(value) || "" }));
      fieldProps.onChange(getNumber(value) || "");
      if (getNumber(value)?.length === 16) {
        monthRef.current?.focus();
      }
    }
  };

  const handleTyping = (
    value: string,
    name: string,
    fieldProps: FieldProps<string>
  ) => {
    if (isNumber(value)) {
      let tempValue = value;
      if (name === "MM" && value?.length === 1 && +value > 1) {
        tempValue = `0${value}`;
      }
      fieldProps.onChange(tempValue);
      setCard((prev) => ({
        ...prev,
        [name]: tempValue,
      }));
    }
  };

  React.useEffect(() => {
    if (card.MM.length === 2) {
      yearRef.current?.focus();
    }
    if (card.YY.length === 2) {
      cvcRef.current?.focus();
    }
  }, [card.MM, card.YY]);

  React.useEffect(() => {
    if (onChange) {
      onChange({
        number: card.number,
        expiry: { yy: card.YY, mm: card.MM },
        cvc: card.cvc,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [card]);

  return (
    <GeneralInputWrapper
      isDisabled={isDisabled}
      backgroundColor={backgroundColor}
      isError={isError}
      dir="ltr"
    >
      <Stack
        direction="row"
        justify="between"
        align="center"
        spacing="tight"
        largeMobile={{ direction: "column" }}
      >
        <Stack direction="row" flex="3" align="center" spacing="tight">
          <CreditCardEmptyIcon />
          <Field name="card.number" margin="0" defaultValue="">
            {({ fieldProps }) => (
              <StyledInput
                type="tel"
                data-test={dataTest}
                style={{ minWidth: "auto" }}
                inputMode="numeric"
                placeholder={placeHolder || "Card Number"}
                autoComplete="cc-number"
                value={displayedCardNumber}
                onChange={(event) =>
                  handleCardNumber(event.target.value, fieldProps)
                }
              />
            )}
          </Field>
        </Stack>

        <Stack
          direction="row"
          justify="between"
          align="center"
          spacing="tight"
          flex="1"
        >
          <StyledExpiry>
            <StyledInputWrapper>
              <Field name="card.expiry.mm" margin="0" defaultValue="">
                {({ fieldProps }) => (
                  <StyledInput
                    ref={monthRef}
                    style={{ minWidth: "auto" }}
                    type="tel"
                    name="MM"
                    inputMode="numeric"
                    placeholder="MM"
                    autoComplete="cc-exp-month"
                    value={card.MM}
                    onChange={(event) => handleTyping(
                      event.target.value.substring(0, 2),
                      event.target.name,
                      fieldProps
                    )} />
                )}
              </Field>
            </StyledInputWrapper>

            <StyledSlash> / </StyledSlash>

            <StyledInputWrapper>
              <Field name="card.expiry.yy" margin="0" defaultValue="">
                {({ fieldProps }) => (
                  <StyledInput
                    ref={yearRef}
                    style={{ minWidth: "auto" }}
                    type="tel"
                    name="YY"
                    inputMode="numeric"
                    placeholder="YY"
                    autoComplete="cc-exp-year"
                    value={card.YY}
                    onChange={(event) =>
                      handleTyping(
                        event.target.value.substring(0, 2),
                        event.target.name,
                        fieldProps
                      )
                    }
                  />
                )}
              </Field>
            </StyledInputWrapper>
          </StyledExpiry>

          <Field name="card.cvc" margin="0" defaultValue="">
            {({ fieldProps }) => (
              <StyledInput
                style={{ minWidth: "auto" }}
                ref={cvcRef}
                type="tel"
                name="cvc"
                inputMode="numeric"
                placeholder="CVC"
                autoComplete="cc-csc"
                value={card.cvc}
                onChange={(event) =>
                  handleTyping(
                    event.target.value.substring(0, 4),
                    event.target.name,
                    fieldProps
                  )
                }
              />
            )}
          </Field>
        </Stack>
      </Stack>
    </GeneralInputWrapper>
  );
};

InputCard.displayName = "InputCard";

export { InputCard };

/**
 *
 *
 * helper functions
 *
 *
 */

const isNumber = (value: string) => {
  return !!value?.match(/^[0-9]*$/);
};

const getNumber = (value: string) => {
  return value?.match(/\d+/g)?.join("")?.substring(0, 16);
};

const addHyphen = (value: string) => {
  return value?.match(/.{1,4}/g)?.join("  ");
};

/**
 *
 *
 * Styles
 *
 *
 */

const StyledExpiry = styled.div`
  width: 100%;
  display: flex;
  justify-content: start;
  align-items: center;
`;

const StyledSlash = styled.span`
  margin: 0 2px;
  color: ${({ theme }) => theme.palette.ink.light};
`;

const StyledInputWrapper = styled.div`
  width: 30px;
`;
