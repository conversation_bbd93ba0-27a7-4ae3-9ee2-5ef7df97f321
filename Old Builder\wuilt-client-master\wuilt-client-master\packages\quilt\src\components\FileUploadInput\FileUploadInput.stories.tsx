import React from "react";
import { FileUploadInput } from "./FileUploadInput";

export default {
  title: "Components/FileUpload",
  component: FileUploadInput,
};

export const InputFileUpload = () => {
  const [file, setFile] = React.useState<File>();

  const handleUpload = (e) => {
    const selectedFile = e.target.files[0];
    const Size = e.target.files[0].size / 1024 / 1024;
    if (selectedFile && Size < 25) {
      setFile(e.target.files[0]);
    } else {
      alert("error");
    }
  };
  const handleDelete = () => {
    setFile(null);
  };
  return (
    <FileUploadInput
      value={file}
      noFileText="No files selected"
      onChange={handleUpload}
      deleteFile={handleDelete}
      buttonText="Browse"
    />
  );
};
