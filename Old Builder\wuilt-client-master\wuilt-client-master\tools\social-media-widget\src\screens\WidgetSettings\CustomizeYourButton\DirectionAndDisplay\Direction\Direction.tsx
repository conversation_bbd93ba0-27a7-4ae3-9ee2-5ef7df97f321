import {
  Box,
  HorizontalAlignIcon,
  RadioButton,
  RadioGroup,
  Stack,
  VerticalAlignIcon,
} from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import {
  TWidgetAppearance,
  TWidgetSettings,
  WidgetOrientationEnum,
} from "../../../../../shared/types";
import CustomText from "../../../ui/CustomText";
interface DirectionProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function Direction({ appearance, update }: DirectionProps) {
  return (
    <Box width="100%">
      <CustomText>
        <FormattedMessage defaultMessage="Opening direction" id="3qRjvg" />
      </CustomText>
      <Box mt="6px">
        <RadioGroup
          defaultValue={appearance.display.orientation}
          onChange={(value: WidgetOrientationEnum) => {
            update({
              appearance: {
                ...appearance,
                display: { ...appearance.display, orientation: value },
              },
            });
          }}
        >
          <RadioButton
            label={
              <Stack spacing="condensed" align="center" direction="row">
                <VerticalAlignIcon
                  className="direction-and-display-icon"
                  color="transparent"
                />
                <CustomText>
                  <FormattedMessage defaultMessage="Vertical" id="cLrroF" />
                </CustomText>
              </Stack>
            }
            value={WidgetOrientationEnum.Vertical}
          />
          <RadioButton
            label={
              <Stack spacing="condensed" align="center" direction="row">
                <HorizontalAlignIcon
                  className="direction-and-display-icon"
                  color="transparent"
                />
                <CustomText>
                  <FormattedMessage defaultMessage="Horizontal" id="NfU3/O" />
                </CustomText>
              </Stack>
            }
            value={WidgetOrientationEnum.Horizontal}
          />
        </RadioGroup>
      </Box>
    </Box>
  );
}

export default Direction;
