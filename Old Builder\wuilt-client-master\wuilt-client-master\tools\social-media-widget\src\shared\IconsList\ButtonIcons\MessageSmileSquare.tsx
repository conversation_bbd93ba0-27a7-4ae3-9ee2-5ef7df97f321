import { TIconType } from "../types";

export function MessageSmileSquare({
  size = 40,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.08333 10.4167C7.08333 10.4167 8.17708 11.6667 10 11.6667C11.8229 11.6667 12.9167 10.4167 12.9167 10.4167M12.2917 6.25H12.3M7.70833 6.25H7.71667M5.83333 15V16.9463C5.83333 17.3903 5.83333 17.6123 5.92436 17.7263C6.00352 17.8255 6.12356 17.8832 6.25045 17.8831C6.39636 17.8829 6.56973 17.7442 6.91646 17.4668L8.90434 15.8765C9.31043 15.5517 9.51347 15.3892 9.73957 15.2737C9.94017 15.1712 10.1537 15.0963 10.3743 15.051C10.6231 15 10.8831 15 11.4031 15H13.5C14.9001 15 15.6002 15 16.135 14.7275C16.6054 14.4878 16.9878 14.1054 17.2275 13.635C17.5 13.1002 17.5 12.4001 17.5 11V6.5C17.5 5.09987 17.5 4.3998 17.2275 3.86502C16.9878 3.39462 16.6054 3.01217 16.135 2.77248C15.6002 2.5 14.9001 2.5 13.5 2.5H6.5C5.09987 2.5 4.3998 2.5 3.86502 2.77248C3.39462 3.01217 3.01217 3.39462 2.77248 3.86502C2.5 4.3998 2.5 5.09987 2.5 6.5V11.6667C2.5 12.4416 2.5 12.8291 2.58519 13.147C2.81635 14.0098 3.49022 14.6836 4.35295 14.9148C4.67087 15 5.05836 15 5.83333 15ZM12.7083 6.25C12.7083 6.48012 12.5218 6.66667 12.2917 6.66667C12.0615 6.66667 11.875 6.48012 11.875 6.25C11.875 6.01988 12.0615 5.83333 12.2917 5.83333C12.5218 5.83333 12.7083 6.01988 12.7083 6.25ZM8.125 6.25C8.125 6.48012 7.93845 6.66667 7.70833 6.66667C7.47821 6.66667 7.29167 6.48012 7.29167 6.25C7.29167 6.01988 7.47821 5.83333 7.70833 5.83333C7.93845 5.83333 8.125 6.01988 8.125 6.25Z"
        stroke={color || "currentColor"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
