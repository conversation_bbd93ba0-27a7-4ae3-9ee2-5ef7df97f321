import React from "react";
import styled, { css } from "styled-components";
import { Box, BoxProps } from "../Box";
import { AccordionContext } from "./AccordionContext";
import { useTimeout } from "../../hooks/useTimeout";

export type BodyProps = BoxProps;

const Body: React.FC<BodyProps> = ({
  children,
  maxHeight = "250px",
  ...boxProps
}) => {
  const isOpen = React.useContext(AccordionContext)[0];
  const [hideChildren, setHideChildren] = React.useState(!isOpen);
  const { reset } = useTimeout(() => {
    if (!isOpen) setHideChildren(true);
    if (isOpen) setHideChildren(false);
  }, 200);

  React.useEffect(() => {
    if (!isOpen) reset();
    if (isOpen) setHideChildren(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  return !children ? null : (
    <StyledWrapper isOpen={isOpen} maxHeight={maxHeight}>
      <Box p="0 20px 10px" overflow="auto" maxHeight={maxHeight} {...boxProps}>
        {!hideChildren && children}
      </Box>
    </StyledWrapper>
  );
};

Body.displayName = "Body";
export { Body };

/**
 *
 * Styles
 *
 */

const StyledWrapper = styled(Box)<{ isOpen: boolean; maxHeight: any }>`
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-out;

  ${({ isOpen, maxHeight }) =>
    isOpen &&
    css`
      max-height: ${maxHeight};
      transition: max-height 0.2s ease-in;
    `}
`;
