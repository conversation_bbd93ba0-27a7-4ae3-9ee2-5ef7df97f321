import React from "react";
import { SelectTabs } from "./index";
import { SelectTab } from "./SelectTab";

export default {
  title: "Components/SelectTabs",
  component: SelectTabs,
};

export const Playground = () => {
  return (
    <SelectTabs
      name="test"
      defaultValue="option1"
      fitContentWidth
      onChange={(value) => console.log(value)}
    >
      <SelectTab label={<span>SelectTab 1</span>} value="option1" />
      {/* <SelectTab label="SelectTab 1" value="option1" /> */}
      <SelectTab label="SelectTab 2" value="option2" />
      <SelectTab label="SelectTab 3" value="option3" />
      <SelectTab label="SelectTab 4" value="option4" />
      <SelectTab label="SelectTab 5" value="option5" />
      <SelectTab label="Disabled" value="option6" isDisabled />
    </SelectTabs>
  );
};
