import React from "react";
import styled, { css } from "styled-components";
import onlyIE from "../../../utils/onlyIE";

const ieStyle = css`
  min-width: 100%;
  max-width: 1px;
`;
const StyledButtonPrimitiveContent = styled(
  ({
    theme,
    loading,
    hasCenteredContent,
    onlyIcon,
    contentAlign,
    ...props
  }) => <div {...props} />
)`
  visibility: ${({ loading }) => loading && "hidden"};
  height: 100%;
  display: flex;
  justify-content: ${({ contentAlign }) => contentAlign};
  flex-basis: 100%;
  align-items: center;
  // IE flexbox bug
  ${onlyIE(ieStyle)}
`;

const ButtonPrimitiveContent = ({ children, ...props }) => (
  <StyledButtonPrimitiveContent {...props}>
    {children}
  </StyledButtonPrimitiveContent>
);

export default ButtonPrimitiveContent;
