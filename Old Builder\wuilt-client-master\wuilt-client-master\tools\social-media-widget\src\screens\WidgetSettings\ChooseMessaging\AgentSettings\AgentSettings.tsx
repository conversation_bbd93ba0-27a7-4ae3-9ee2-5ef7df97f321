import { Box, Text } from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import { TAppSettings, TWhatsAppAgent } from "../../../../shared/types";
import WhatsAppSettings from "../WhatsAppSettings";
interface AgentSettings {
  agentIndex: number;
  agent: TWhatsAppAgent;
  updateAgents: (
    apps: TAppSettings[],
    agentIndex: number,
    key: string,
    value: string
  ) => void;
}
function AgentSettings({ agent, updateAgents, agentIndex }) {
  return (
    <Box padding="10px" width="347px">
      <Text fontWeight="semiBold" color="#1D2939">
        <FormattedMessage defaultMessage="Agent Settings" id="99GPNh" />
      </Text>
      <WhatsAppSettings
        agent={agent}
        agentIndex={agentIndex}
        updateAgents={updateAgents}
      />
    </Box>
  );
}

export default AgentSettings;
