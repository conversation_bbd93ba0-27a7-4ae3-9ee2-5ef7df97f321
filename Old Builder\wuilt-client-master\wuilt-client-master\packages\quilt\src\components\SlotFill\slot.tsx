import React, { FunctionComponent, useEffect, useRef } from "react";
import { useSlotFillContext } from "./SlotFillContext";

interface SlotProps {
  name: string;
  children?: React.ReactNode;
  Wrapper?: FunctionComponent;
}

const Slot: React.FC<SlotProps> = ({ name, children, Wrapper }) => {
  const { registerSlot, unregisterSlot } = useSlotFillContext();
  const ref = useRef(null);
  useEffect(() => {
    if (name) {
      registerSlot(name, ref, Wrapper);
    }
    return () => {
      if (name) {
        unregisterSlot(name);
      }
    };
  }, []);

  return <div ref={ref}>{children}</div>;
};

export default Slot;
