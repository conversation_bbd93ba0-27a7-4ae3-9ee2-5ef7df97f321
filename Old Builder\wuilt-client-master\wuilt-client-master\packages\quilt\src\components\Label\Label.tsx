import React, { FC } from "react";
import styled from "styled-components";
import { layout } from "styled-system";
import { HeadingStyleProps } from "../Heading";
import {
  Color,
  Typography,
  TextAlignmentCss,
  Space,
} from "../../themes/property-overriding";

export interface LabelProps extends HeadingStyleProps {
  id?: string;
  className?: string;
  children?: React.ReactNode;
  required?: boolean;
  dataTest?: any;
  disabled?: boolean;
  htmlFor?: string;
}

const StyledAsterisk = styled.span`
  font-size: ${({ theme }) => theme.base.fontSize.md};
  font-weight: ${({ theme }) => theme.base.fontWeight.bold};
  color: ${({ theme }) => theme.palette.red.normal};
  vertical-align: top;
`;

const Label: FC<LabelProps> = ({
  id,
  className,
  children,
  required,
  dataTest,
  htmlFor,
  disabled,
  wordBreak,
  lineHeight,
  ...restLabelStyle
}) => (
  // @ts-ignore
  <StyledLabel
    id={id}
    htmlFor={htmlFor}
    className={className}
    data-test={dataTest}
    disabled={disabled}
    wordBreak={wordBreak}
    lineHeight={lineHeight}
    {...restLabelStyle}
  >
    <span>{children}</span>
    {required && <StyledAsterisk aria-hidden="true"> *</StyledAsterisk>}
  </StyledLabel>
);

const StyledLabel = styled.label<{ disabled; wordBreak; lineHeight }>`
  display: block;
  font-family: ${({ theme }) => theme.base.fontFamily};
  font-weight: ${({ theme }) => theme.base.fontWeight.normal};
  font-size: ${({ theme }) => theme.base.fontSize.md};
  color: ${({ theme, disabled }) =>
    disabled ? theme.palette.ink.lighter : theme.palette.ink.normal};
  margin-bottom: ${({ theme }) => theme.base.space.xxs};

  ${Color};
  ${TextAlignmentCss}
  ${Typography};
  ${Space}
  ${layout}

  ${({ wordBreak }) => wordBreak && `word-break:${wordBreak}`}
  ${({ lineHeight }) => lineHeight && `line-height:${lineHeight}`}
`;

export { Label };
