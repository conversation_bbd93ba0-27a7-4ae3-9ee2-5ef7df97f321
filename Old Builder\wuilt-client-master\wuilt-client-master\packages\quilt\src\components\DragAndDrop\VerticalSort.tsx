import React from "react";
import {
  DragStartEvent,
  DragEndEvent,
  DragMoveEvent,
  UniqueIdentifier,
} from "@dnd-kit/core";
import { Props as DndContextProps } from "@dnd-kit/core/dist/components/DndContext/DndContext";
import { Box, BoxProps } from "../Box";
import { SortableItem, SortableItemChildrenArgs } from "./SortableItem";
import { SortableContext } from "./SortableContext";

interface VerticalSortProps<Type> extends Omit<DndContextProps, "children"> {
  value: Type[];
  onChange: (value: Type[], oldIndex: number, newIndex: number) => void;
  children: (args: SortableItemChildrenArgs<Type>) => React.ReactNode;
  useHandleOnly?: boolean;
  uniqueFieldName?: string;
  limitToContainerEdges?: boolean;
  className?: string;
  boxProps?: BoxProps;
  distance?: number;
  disableItem?: (id: UniqueIdentifier) => boolean;
  extraItem?: React.ReactNode;
}

function VerticalSort<Type>(props: VerticalSortProps<Type>) {
  const {
    value = [],
    children,
    useHandleOnly = false,
    uniqueFieldName = "id",
    className,
    boxProps,
    disableItem = () => false,
    extraItem = null,
  } = props;

  return (
    <Box className={className} display="grid" {...boxProps}>
      <SortableContext {...props}>
        {value.map((item, index) => (
          <SortableItem
            key={item[uniqueFieldName]}
            id={item[uniqueFieldName]}
            item={item}
            index={index}
            children={children}
            useHandleOnly={useHandleOnly}
            disabled={disableItem(item[uniqueFieldName])}
          />
        ))}
        {extraItem}
      </SortableContext>
    </Box>
  );
}

export {
  VerticalSort,
  VerticalSortProps,
  DragStartEvent,
  DragMoveEvent,
  DragEndEvent,
};
