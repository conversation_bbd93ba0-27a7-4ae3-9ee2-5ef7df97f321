import {
  createContext,
  FC,
  ReactElement,
  useCallback,
  useContext,
  useEffect,
  useMemo,
} from "react";
import { useAuth } from "../auth/auth-provider";
import { identifyChatMutation } from "./identify-chat-user-mutation";

interface ChatSupportContextType {
  load: () => void;
  remove: () => void;
  refresh: () => void;
  open: () => void;
  close: () => void;
  hide: () => void;
  show: () => void;
}

const noopChatSupport: ChatSupportContextType = {
  load: () => undefined,
  remove: () => undefined,
  refresh: () => undefined,
  open: () => undefined,
  close: () => undefined,
  hide: () => undefined,
  show: () => undefined,
};

export const ChatSupportContext =
  createContext<ChatSupportContextType>(noopChatSupport);

interface TrackingProviderProps {
  children: ReactElement;
}

export const ChatSupportProvider: FC<TrackingProviderProps> = ({
  children,
}) => {
  const auth = useAuth();
  const isEnabled =
    // todo: handle backoffice access
    // !oidcUser?.aud.includes("wuilt-backoffice") &&
    // auth.user?.id;
    false;

  const onConversationsAPIReady = useCallback(() => {
    if (window.hsConversationsSettings?.identificationToken) return;
    identifyChatMutation()
      .then((res) => {
        if (res.data?.identifyChat) {
          window.hsConversationsSettings = {
            loadImmediately: false,
            identificationEmail: res.data.identifyChat.email,
            identificationToken: res.data.identifyChat.token,
          };
          // window.HubSpotConversations?.widget?.load?.();
        }
      })
      .catch(() => {
        // don't do anything if there's an error
      });
  }, []);

  useEffect(() => {
    if (!isEnabled) {
      return;
    }
    if (window.HubSpotConversations) {
      onConversationsAPIReady();
    } else {
      window.hsConversationsOnReady = [onConversationsAPIReady];
    }
  }, [isEnabled, onConversationsAPIReady]);

  const value = useMemo(() => {
    if (!isEnabled) {
      return noopChatSupport;
    }
    return {
      load: () => {
        window.HubSpotConversations?.widget?.load?.();
      },
      refresh: () => {
        window.HubSpotConversations?.widget?.refresh?.();
      },
      remove: () => {
        window.HubSpotConversations?.widget?.remove?.();
      },
      open: () => {
        showHubSpotChat();
        window.HubSpotConversations?.widget?.open?.();
      },
      close: () => {
        window.HubSpotConversations?.widget?.close?.();
      },
      hide: () => {
        hideHubSpotChat();
      },
      show: () => {
        showHubSpotChat();
      },
    };
  }, [isEnabled]);

  return (
    <ChatSupportContext.Provider value={value}>
      {children}
    </ChatSupportContext.Provider>
  );
};

export const useChatSupport = () => {
  const context = useContext(ChatSupportContext);
  return context;
};

function showHubSpotChat() {
  const el = document.getElementById("hubspot-messages-iframe-container");
  el && el.style.setProperty("display", "none", "important");
}

function hideHubSpotChat() {
  const el = document.getElementById("hubspot-messages-iframe-container");
  el && el.style.setProperty("display", "none", "important");
}
