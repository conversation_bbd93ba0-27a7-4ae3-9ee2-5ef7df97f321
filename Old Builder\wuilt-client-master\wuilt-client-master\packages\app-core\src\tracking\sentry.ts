import * as Sentry from "@sentry/react";
import { Attributes } from "./types";

const init = (dsn: string, user: any) => {
  const isInitialized = Sentry?.getCurrentHub?.()?.getClient?.() != null;

  if (isInitialized) return;

  Sentry.init({
    dsn,
    integrations: [
      new Sentry.BrowserTracing({
        // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
        tracePropagationTargets: [/^https:\/\/*\.wuilt\.com/],
      }),
      new Sentry.Replay({
        maskAllText: false,
        blockAllMedia: false,
        maskAllInputs: false,
        networkDetailAllowUrls: [
          window.location.origin,
          /^https:\/\/*\.wuilt\.com/,
        ],
      }),
    ],
    // Performance Monitoring
    tracesSampleRate: 0, // Capture 100% of the transactions, reduce in production!
    // Session Replay
    replaysSessionSampleRate: 0, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
    replaysOnErrorSampleRate: 1, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
  });

  Sentry.setUser({
    email: user?.email,
    id: user?.id,
    username: user?.name,
  });
};

export const createSentryProvider = ({
  dsn,
  user,
}: {
  dsn: string;
  user: any;
}) => {
  init(dsn, user);

  return {
    setContext: (key: string, context?: Attributes) => {
      // Not implemented
    },
    pushError: (message: string, context?: Attributes) => {
      // Not implemented
    },
    pushEvent: (name: string, attributes?: Attributes) => {
      // Not implemented
    },
    view: (name: string, attributes?: Attributes) => {
      // Not implemented
    },
  };
};
