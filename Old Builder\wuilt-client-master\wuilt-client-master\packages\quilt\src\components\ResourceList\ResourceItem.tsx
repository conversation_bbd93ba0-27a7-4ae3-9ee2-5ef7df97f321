import React from "react";
import styled from "styled-components";
import cuid from "cuid";
import { Checkbox } from "../Checkbox";
import { Stack } from "../Stack";
import { ResourceListContext } from "./ResourceListContext";

const StyledResourceItem = styled(Stack)`
  position: relative;
  outline: none;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;

  &:hover {
    background-image: linear-gradient(
      rgba(223, 227, 232, 0.3),
      rgba(223, 227, 232, 0.3)
    );
  }
`;

const StyledMedia = styled.div`
  flex: 0 0 auto;
  margin-right: spacing(loose);
  color: inherit;
  text-decoration: none;
`;

function isSelected(id, selectedItems) {
  return Boolean(selectedItems.includes(id));
}

export interface ResourceItemProps {
  children?: React.ReactNode;
  id: string;
  media: React.ReactNode;
}

const ResourceItem: React.FC<ResourceItemProps> = ({
  id: propId,
  children,
  media,
}) => {
  const { selectedItems, onSelectionChange, selectable, selectMode } =
    React.useContext(ResourceListContext);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const id = React.useMemo(() => propId || cuid(), []);
  const selected = isSelected(id, selectedItems);

  const handleSelect = (newValue, id) => {
    if (onSelectionChange) {
      onSelectionChange(newValue, id, undefined, false);
    }
  };
  const handleClick = () => {
    if (selectMode && selectable) {
      handleSelect(!selected, id);
    }
  };

  return (
    <div onClick={handleClick}>
      <StyledResourceItem>
        {selectable && (
          <Checkbox id={id} label="" value={selected} onChange={handleSelect} />
        )}
        <StyledMedia>{media}</StyledMedia>
        <div>{children}</div>
      </StyledResourceItem>
    </div>
  );
};

export { ResourceItem };
