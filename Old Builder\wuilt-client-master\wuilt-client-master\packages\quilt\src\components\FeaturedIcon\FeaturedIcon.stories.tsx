import React from "react";
import { FeaturedIcon } from "./FeaturedIcon";
import { TrashDeleteBinIcon } from "../icons";
import { Stack } from "../Stack";

export default {
  title: "Components/FeaturedIcon",
  component: FeaturedIcon,
};
export const Playground = () => {
  return (
    <Stack bgColor="white" p="20px" direction="row">
      <FeaturedIcon
        type="success"
        icon={<TrashDeleteBinIcon color="primary" />}
      />
      <FeaturedIcon
        type="danger"
        icon={<TrashDeleteBinIcon color="danger" />}
      />
      <FeaturedIcon
        type="warning"
        icon={<TrashDeleteBinIcon color="danger" />}
      />
      <FeaturedIcon type="info" icon={<TrashDeleteBinIcon color="info" />} />
    </Stack>
  );
};
