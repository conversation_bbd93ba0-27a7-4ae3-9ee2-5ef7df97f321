import React from "react";
import { Box, Heading, ChooseMessagingAppsIcon, Stack } from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import AppsList from "./AppsList";
import SortApps from "./SortApps";

function ChooseMessaging() {
  return (
    <Box>
      <Stack mb="20px" direction="row" align="center">
        <ChooseMessagingAppsIcon className="title-icon" />
        <Heading variant="h1" fontSize="xl" fontWeight="semiBold" font="inter">
          <FormattedMessage
            defaultMessage="Choose messaging apps"
            id="IHJ0nL"
          />
        </Heading>
      </Stack>
      <Stack
        spacing="comfy"
        desktop={{ direction: "column" }}
        direction="row"
        align="start"
      >
        <AppsList />
        <SortApps />
      </Stack>
    </Box>
  );
}

export default ChooseMessaging;
