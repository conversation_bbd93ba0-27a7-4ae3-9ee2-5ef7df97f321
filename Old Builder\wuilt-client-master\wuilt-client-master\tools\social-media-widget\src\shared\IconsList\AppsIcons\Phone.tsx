import React from "react";
import { TIconType } from "../types";

export function Phone({ size = 40, width, height, color }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M24.0558 29.3806C20.8626 28.4743 17.8497 26.7639 15.338 24.2523C12.8264 21.7406 11.1161 18.7277 10.2097 15.5345C10.2044 15.5156 10.199 15.4968 10.1937 15.4782C10.0462 14.9592 9.93183 14.5567 9.93019 14.0048C9.92831 13.374 10.1338 12.5838 10.4427 12.0338C10.974 11.0881 12.1148 9.87601 13.1001 9.37764C13.9515 8.94704 14.9569 8.94704 15.8082 9.37764C16.6504 9.80364 17.5884 10.7572 18.1113 11.5615C18.7578 12.5558 18.7578 13.8377 18.1113 14.832C17.9381 15.0985 17.6912 15.345 17.4045 15.6311C17.3152 15.7203 17.217 15.7846 17.2822 15.9205C17.9301 17.2698 18.8136 18.5354 19.9342 19.6561C21.0549 20.7768 22.3205 21.6603 23.6698 22.3081C23.8105 22.3756 23.8659 22.2792 23.9592 22.1858C24.2454 21.8991 24.4919 21.6522 24.7583 21.479C25.7527 20.8325 27.0345 20.8325 28.0289 21.479C28.8116 21.9879 29.79 22.9465 30.2127 23.7821C30.6433 24.6335 30.6433 25.6389 30.2127 26.4902C29.7143 27.4755 28.5023 28.6163 27.5565 29.1476C27.0065 29.4565 26.2163 29.662 25.5855 29.6601C25.0336 29.6585 24.6312 29.5441 24.1122 29.3966C24.0935 29.3913 24.0748 29.386 24.0558 29.3806Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
