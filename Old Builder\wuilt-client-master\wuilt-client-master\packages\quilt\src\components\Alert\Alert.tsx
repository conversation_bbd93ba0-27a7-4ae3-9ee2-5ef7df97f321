import React from "react";
import styled from "styled-components";
import { Stack, StackFlexStyles } from "../Stack";
import { color } from "../../utils";
import { SuccessIcon, InfoIcon, WarningIcon, ErrorIcon } from "../icons";

export enum AlertType {
  error = "error",
  success = "success",
  warning = "warning",
  info = "info",
}

export interface AlertProps {
  type: keyof typeof AlertType;
  title?: string | React.ReactNode;
  description?: string | React.ReactNode;
  icon?: React.ReactNode;
  controls?: React.ReactNode;
  contentSpacing?: StackFlexStyles["spacing"];
  textContainerOverflow?: string;
}

const Alert = ({
  type,
  title,
  description,
  controls,
  icon,
  contentSpacing = "tight",
  textContainerOverflow = "none",
}: AlertProps) => {
  return (
    <StyledAlertContainer
      bgColor={theme[type].bgColor}
      borderColor={theme[type].borderColor}
    >
      {icon ||
        (type === "success" ? (
          <SuccessIcon color="primary" size="lg" />
        ) : type === "info" ? (
          <InfoIcon color="transparent" size="xl" />
        ) : type === "warning" ? (
          <WarningIcon color="transparent" size="lg" />
        ) : type === "error" ? (
          <ErrorIcon color="danger" size="lg" />
        ) : null)}

      <StyledTextContainer
        overflow={textContainerOverflow}
        spacing={contentSpacing}
      >
        {title && (
          <StyledTitleContainer textColor={theme[type].textColor}>
            {title}
          </StyledTitleContainer>
        )}
        {description && (
          <StyledDescriptionContainer textColor={theme[type].textColor}>
            {description}
          </StyledDescriptionContainer>
        )}
        {controls && (
          <StyledDescriptionContainer textColor={theme[type].textColor}>
            {controls}
          </StyledDescriptionContainer>
        )}
      </StyledTextContainer>
    </StyledAlertContainer>
  );
};

const theme = {
  success: {
    bgColor: ["product", "light"],
    borderColor: ["product", "lightActive"],
    textColor: ["product", "dark"],
  },
  info: {
    bgColor: ["cloud", "normal"],
    borderColor: ["cloud", "normalActive"],
    textColor: ["ink", "lightActive"],
  },
  warning: {
    bgColor: ["orange", "light"],
    borderColor: ["orange", "lightActive"],
    textColor: ["orange", "dark"],
  },
  error: {
    bgColor: ["red", "light"],
    borderColor: ["red", "lightActive"],
    textColor: ["red", "dark"],
  },
};

const StyledAlertContainer = styled.div<{
  bgColor: any[];
  borderColor: any[];
}>`
  display: flex;
  gap: 10px;
  padding: 16px;
  border-radius: 8px;
  margin: 10px 0px;
  background: ${({ bgColor }) => color(bgColor[0], bgColor[1])};
  border: 2px solid;
  border-color: ${({ borderColor }) => color(borderColor[0], borderColor[1])};
`;

const StyledTextContainer = styled(Stack)<{
  spacing: StackFlexStyles["spacing"];
}>`
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: ${({ spacing, theme }) => theme.base.space[spacing || "none"]};
`;

const StyledTitleContainer = styled.h4<{
  textColor: any[];
}>`
  margin: 0px;
  color: ${({ textColor }) => color(textColor[0], textColor[1])};
`;

const StyledDescriptionContainer = styled.p<{
  textColor: any[];
}>`
  font-weight: 400;
  margin: 0px;
  color: ${({ textColor }) => color(textColor[0], textColor[1])};
`;

export { Alert };
