import React from "react";
import { Stack } from "@wuilt/quilt";
import {
  TWidgetAppearance,
  TWidgetSettings,
} from "../../../../../shared/types";
import ChooseButtonIcon from "../ChooseButtonIcon";
import ChooseAnimation from "../ChooseAnimation";
interface IconAndAnimationProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function IconAndAnimation({ update, appearance }: IconAndAnimationProps) {
  return (
    <Stack largeMobile={{ direction: "column" }} mt="16px" direction="row">
      <ChooseButtonIcon appearance={appearance} update={update} />
      <ChooseAnimation appearance={appearance} update={update} />
    </Stack>
  );
}

export default IconAndAnimation;
