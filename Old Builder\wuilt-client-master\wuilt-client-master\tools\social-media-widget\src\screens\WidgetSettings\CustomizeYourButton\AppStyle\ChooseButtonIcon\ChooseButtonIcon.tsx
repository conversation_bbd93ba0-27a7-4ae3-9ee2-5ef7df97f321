import { Box, Stack, Text } from "@wuilt/quilt";
import React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Popover } from "../../../ui";
import { ButtonIcon, ButtonIconsList } from "../../../../../shared/IconsList";
import {
  TWidgetAppearance,
  TWidgetSettings,
} from "../../../../../shared/types";
import CustomText from "../../../ui/CustomText";

interface ChooseButtonIconProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function ChooseButtonIcon({ update, appearance }: ChooseButtonIconProps) {
  const {
    content: { icon },
  } = appearance;
  const SelectedBtnIcon = ButtonIcon[icon];
  const { locale } = useIntl();
  return (
    <Box width="100%">
      <CustomText>
        <FormattedMessage defaultMessage="Button Icon" id="6m2iAS" />
      </CustomText>
      <Popover
        closeButton
        translateX={locale.substring(0, 2) === "ar" ? 65 : 25}
        button={
          <Stack mt="6px" spacing="tight" height="40px" direction="row">
            <Stack
              boxShadow="0px 1px 2px 0px #1018280D"
              width="40px"
              borderRadius="8px"
              align="center"
              justify="center"
              border="1px solid  #D0D5DD"
            >
              <SelectedBtnIcon size={22} color="#667085" />
            </Stack>
            <Stack
              boxShadow="0px 1px 2px 0px #1018280D"
              cursor="pointer"
              borderRadius="8px"
              align="center"
              justify="center"
              width="117px"
              border="1px solid  #D0D5DD"
            >
              <Text color={"black"} fontSize="sm" fontWeight="semiBold">
                <FormattedMessage defaultMessage="Change icon" id="ZWysYw" />
              </Text>
            </Stack>
          </Stack>
        }
        content={
          <Stack height="320px" spacing="none">
            <Box p="16px" pt="10px" pb="0">
              <Text
                fontWeight="semiBold"
                fontSize="sm"
                style={{ color: "#1D2939" }}
              >
                <FormattedMessage defaultMessage="Button icon" id="CgNuyQ" />
              </Text>
            </Box>
            <Stack
              className="chooseContainer"
              style={{ gap: "16px", paddingInlineEnd: "0" }}
              height="300px"
              overflowY="scroll"
              width="240px"
              spacing="none"
              padding="16px"
              wrap
              direction="row"
            >
              {ButtonIconsList.map(({ Icon, name }) => {
                return (
                  <Stack
                    key={name}
                    cursor="pointer"
                    justify="center"
                    align="center"
                    border={
                      icon === name ? "1px solid #0E9384" : "1px solid #D0D5DD"
                    }
                    style={{ backgroundColor: icon === name ? "#F6FEFC" : "" }}
                    height="40px"
                    width="40px"
                    borderRadius="6px"
                    onClick={() => {
                      update({
                        appearance: {
                          ...appearance,
                          content: { ...appearance.content, icon: name },
                        },
                      });
                    }}
                  >
                    <Icon
                      size={22}
                      color={icon === name ? "#107569" : "#667085"}
                    />
                  </Stack>
                );
              })}
            </Stack>
          </Stack>
        }
      />
    </Box>
  );
}

export default ChooseButtonIcon;
