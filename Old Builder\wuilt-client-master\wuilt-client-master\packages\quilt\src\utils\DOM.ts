import * as focusable from "./focusable";
import * as tabbable from "./tabbable";

export const findVisibleParent = (element) => {
  if (!element) {
    return undefined;
  }
  return element.offsetParent
    ? element
    : findVisibleParent(element.parentElement) || element;
};

/**
 * Given a DOM node, finds the closest scrollable container node.
 */
export function getScrollContainer(node: Element | null): Element | undefined {
  if (!node) {
    return undefined;
  }
  // Scrollable if scrollable height exceeds displayed...
  if (node.scrollHeight > node.clientHeight) {
    // ...except when overflow is defined to be hidden or visible
    const { overflowY } = window.getComputedStyle(node);
    if (/(auto|scroll)/.test(overflowY)) {
      return node;
    }
  }
  // Continue traversing
  return getScrollContainer(node.parentNode as Element);
}

/**
 * Get the rectangle of a given Range.
 */
export function getRectangleFromRange(range: Range): DOMRect {
  // For uncollapsed ranges, get the rectangle that bounds the contents of the
  // range; this a rectangle enclosing the union of the bounding rectangles
  // for all the elements in the range.
  if (!range.collapsed) {
    return range.getBoundingClientRect();
  }

  const { startContainer } = range;

  // Correct invalid "BR" ranges. The cannot contain any children.
  if (startContainer.nodeName === "BR" && startContainer.parentNode) {
    const { parentNode } = startContainer;
    const index = Array.from(parentNode.childNodes).indexOf(
      startContainer as ChildNode
    );
    range = document.createRange();
    range.setStart(parentNode, index);
    range.setEnd(parentNode, index);
  }

  let rect = range.getClientRects()[0];

  // If the collapsed range starts (and therefore ends) at an element node,
  // `getClientRects` can be empty in some browsers. This can be resolved
  // by adding a temporary text node with zero-width space to the range.
  //
  // See: https://stackoverflow.com/a/6847328/995445
  if (!rect) {
    const padNode = document.createTextNode("\u200b");
    // Do not modify the live range.
    range = range.cloneRange();
    range.insertNode(padNode);
    const fRect = range.getClientRects().item(0);
    if (fRect) {
      rect = fRect;
    }
    padNode?.parentNode?.removeChild(padNode);
  }

  return rect;
}

export const focus = { focusable, tabbable };
