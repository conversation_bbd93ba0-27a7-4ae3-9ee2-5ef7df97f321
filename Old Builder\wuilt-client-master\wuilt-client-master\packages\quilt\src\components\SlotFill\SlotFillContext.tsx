import { FunctionComponent, ReactNode, createContext, useContext } from "react";

export interface SlotFillContextType {
  registerSlot: (
    name: string,
    ref: React.RefObject<HTMLDivElement>,
    Wrapper?: FunctionComponent<{ children: ReactNode }>
  ) => void;
  getSlot: (name: string) => {
    ref: React.RefObject<HTMLDivElement>;
    Wrapper?: FunctionComponent<{ children: ReactNode }>;
  };
  unregisterSlot: (name: string) => void;
}

const SlotFillContext = createContext<SlotFillContextType>(
  {} as SlotFillContextType
);

export const useSlotFillContext = () => useContext(SlotFillContext);

export default SlotFillContext;
