import React from "react";
import { SidePanel } from "./SidePanel";
import { Navigation } from "../Navigation";
import { Section } from "../Navigation/Section";
import { Item } from "../Navigation/Item";
import {
  PhotoCameraIcon,
  DropSilhouetteIcon,
  FontIcon,
  SettingsIcon,
  EditIcon,
  EyeIcon,
} from "../icons";

const AppNavigation = (
  <Navigation>
    <Section title="My Store">
      <Item label="Orders" isActive icon={<PhotoCameraIcon />} />
      <Item label="Products" icon={<DropSilhouetteIcon />} />
      <Item label="Customers" icon={<FontIcon />} />
    </Section>
    <Section title="Settings">
      <Item
        label="General"
        icon={<SettingsIcon />}
        badge={<div style={{ width: "60px" }}>GO LIVE</div>}
      />
      <Item label="Payment" icon={<EditIcon />} />
      <Item label="Shipping" icon={<EyeIcon />} />
    </Section>
  </Navigation>
);

export default {
  title: "Components/SidePanel",
  components: SidePanel,
};

export const playground = () => {
  return <SidePanel>{AppNavigation}</SidePanel>;
};
