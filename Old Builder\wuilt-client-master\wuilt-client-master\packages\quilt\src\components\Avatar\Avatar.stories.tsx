import React from "react";
import { Avatar } from "./Avatar";
import { Stack } from "../Stack/Stack";

export default {
  title: "Components/Avatar",
  component: Avatar,
};

export const Playground = () => {
  return (
    <>
      <Stack direction="row">
        <Avatar fullName="<PERSON>" size="xsmall" />
        <Avatar fullName="<PERSON>" />
        <Avatar fullName="<PERSON>" size="medium" />
        <Avatar fullName="<PERSON> " size="large" />
        <Avatar fullName="<PERSON>" size="xLarge" />
        <Avatar fullName="<PERSON>" size="xxLarge" />
        <Avatar fullName="<PERSON>" size="xxxLarge" />
      </Stack>

      <br />
      <Stack direction="row">
        <Avatar
          src="https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"
          size="xsmall"
        />
        <Avatar src="https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500" />
        <Avatar
          src="https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"
          size="medium"
        />
        <Avatar
          src="https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"
          size="large"
        />
        <Avatar
          src="https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"
          size="xLarge"
        />
        <Avatar
          src="https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"
          size="xxLarge"
        />
        <Avatar
          src="https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"
          size="xxxLarge"
        />
      </Stack>
    </>
  );
};
