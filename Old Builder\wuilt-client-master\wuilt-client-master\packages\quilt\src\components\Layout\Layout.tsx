import React from "react";
import styled, { css } from "styled-components";

const primaryBasis = 480;
const secondaryBasis = 240;
const relativeSize = primaryBasis / secondaryBasis;
const StyledLayout = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
`;

function getFlex(props) {
  if (props.secondary) {
    return css`
      flex: 1 1 ${secondaryBasis}px;
      min-width: 0%;
    `;
  }
  if (props.fullWidth) {
    return css`
      flex: 1 1 100%;
      min-width: 0%;
    `;
  }
  return css`
    flex: ${relativeSize} ${relativeSize} ${primaryBasis}px;
    min-width: 51%;
  `;
}
const StyledLayoutSecion = styled.div<{
  secondary: boolean;
  fullWidth: boolean;
  oneHalf: boolean;
  oneThird: boolean;
}>`
  ${getFlex};
  max-width: calc(100% - ${({ theme }) => theme.base.space.sm});
  margin-top: ${({ theme }) => theme.base.space.sm};
  ${({ theme }) => {
    if (theme.rtl) {
      return css`
        margin-right: ${({ theme }) => theme.base.space.sm};
      `;
    }
    return css`
      margin-left: ${({ theme }) => theme.base.space.sm};
    `;
  }}
`;

const LayoutSection = (props) => {
  const { secondary, fullWidth, oneHalf, oneThird, children } = props;
  return (
    <StyledLayoutSecion
      secondary={secondary}
      fullWidth={fullWidth}
      oneHalf={oneHalf}
      oneThird={oneThird}
    >
      {children}
    </StyledLayoutSecion>
  );
};

const Layout = (props) => {
  return <StyledLayout>{props.children}</StyledLayout>;
};

Layout.Section = LayoutSection;

export { Layout, LayoutSection };
