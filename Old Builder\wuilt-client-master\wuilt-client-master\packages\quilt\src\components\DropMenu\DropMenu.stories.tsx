import React from "react";
import { DropMenu } from "./DropMenu";
import { Tooltip } from "../Tooltip";
import { ButtonIcon } from "../ButtonIcon";
import { CopyIcon, GarbageIcon, MoreHorizIcon } from "../icons";
import { Button } from "../Button";

export default {
  title: "Components/DropMenu",
  component: DropMenu,
};

export const Playground = () => {
  return (
    <DropMenu
      applyHoverEffect
      activator={
        <Tooltip content="More Action">
          <ButtonIcon size="small" color="white" stopOpacity>
            <MoreHorizIcon />
          </ButtonIcon>
        </Tooltip>
      }
    >
      <Button plain compact prefixIcon={<CopyIcon size="xl" />}>
        Copy
      </Button>
      <Button
        plain
        compact
        color="danger"
        prefixIcon={<GarbageIcon size="sm" />}
      >
        Delete
      </Button>
    </DropMenu>
  );
};
