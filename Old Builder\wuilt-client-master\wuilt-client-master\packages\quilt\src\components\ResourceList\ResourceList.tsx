/* eslint-disable react/no-unused-prop-types */
import React from "react";
import styled from "styled-components";
import { Spinner } from "../Spinner";
import { ResourceItem } from "./ResourceItem";
import {
  ResourceListContext,
  ResourceListContextType,
} from "./ResourceListContext";
import { ResourceListSelectedItems } from "./types";

const StyledResourceList = styled.ul`
  position: relative;
  z-index: 9;
  margin: 0;
  padding: 0;
  list-style: none;
`;

const StyledItemWrapper = styled.li`
  position: relative;
  z-index: 9;
  overflow: hidden;
  max-width: 100%;
`;

export interface ResourceListProps<ItemType extends { id?: string } = any> {
  items: ItemType[];
  filterControl?: React.ReactNode;
  emptyState?: React.ReactNode;
  emptySearchState?: React.ReactNode;
  selectMode?: boolean;
  resourceName?: {
    singular: string;
    plural: string;
  };
  selectedItems?: ResourceListSelectedItems;
  selectable?: boolean;
  hasMoreItems?: boolean;
  loading?: boolean;
  showHeader?: boolean;
  totalItemsCount?: number;
  sortValue?: string;
  // sortOptions?: SelectOption[];
  alternateTool?: React.ReactNode;
  onSortChange?(selected: string, id: string): void;
  onSelectionChange?(selectedItems: ResourceListSelectedItems): void;
  renderItem(item: ItemType, id: string, index: number): React.ReactNode;
  idForItem?(item: ItemType, index: number): string;
  resolveItemId?(item: ItemType): string;
}

function defaultIdForItem<ItemType extends { id?: any }>(
  item: ItemType,
  index: number
) {
  return Object.prototype.hasOwnProperty.call(item, "id")
    ? item.id
    : index.toString();
}

export type ResourceListType = (<ItemType extends { id?: any } = any>(
  value: ResourceListProps<ItemType>
) => React.ReactElement) & {
  Item: typeof ResourceItem;
  displayName: string;
};

const ResourceList: ResourceListType = function ResourceList<
  ItemType extends { id?: any }
>(props: ResourceListProps<ItemType>) {
  const {
    items,
    emptyState,
    renderItem,
    loading,
    selectable,
    selectMode,
    onSelectionChange,
  } = props;
  const itemsExist = !!items && items.length > 0;
  const showEmptyState = emptyState && !itemsExist && !loading;
  const [selectedItems, setSelectedItems] = React.useState([]);
  const isSelectable = selectable;
  const loadingOverlay = loading ? (
    <>
      <div>
        <Spinner />
      </div>
      {/* LoadingOverlay */}
    </>
  ) : null;

  const renderItemWithId = (item: ItemType, index: number) => {
    const id = defaultIdForItem(item, index);

    return (
      <StyledItemWrapper key={id}>
        {renderItem(item, id, index)}
      </StyledItemWrapper>
    );
  };
  const listMarkup = itemsExist ? (
    <StyledResourceList
      // className={resourceListClassName}
      // ref={listRef}
      aria-live="polite"
      aria-busy={loading}
    >
      {loadingOverlay}
      {items.map(renderItemWithId)}
    </StyledResourceList>
  ) : null;
  const markup = showEmptyState ? emptyState : <>{listMarkup}</>;
  const context: ResourceListContextType = {
    selectMode: selectMode,
    selectedItems,
    selectable: isSelectable,
    onSelectionChange: (isSelected, id) => {
      let newSelectedItem;
      if (isSelected) {
        newSelectedItem = [...selectedItems, id];
      } else {
        newSelectedItem = selectedItems.filter((current) => current !== id);
      }
      setSelectedItems(newSelectedItem);
      if (onSelectionChange) {
        onSelectionChange(newSelectedItem);
      }
    },
  };

  return (
    <ResourceListContext.Provider value={context}>
      {markup}
    </ResourceListContext.Provider>
  );
};

ResourceList.displayName = "ResourceList";
ResourceList.Item = ResourceItem;
export { ResourceList };
