import React from "react";
import { ComponentStory, ComponentMeta } from "@storybook/react";
import { SortableContext } from "../SortableContext";
import { Heading } from "../../Heading";
import { Stack } from "../../Stack";
import { Box } from "../../Box";
import { Drag<PERSON>andle } from "../DragHandle";
import { SortableItem } from "../SortableItem";

// https://storybook.js.org/docs/react/essentials/controls
export default {
  title: "Components/Sorting/SortableContext",
  component: SortableContext,
  argTypes: {
    value: { control: false },
    uniqueFieldName: { control: false },
    className: { control: false },
    boxProps: { control: false },
  },
} as ComponentMeta<typeof SortableContext>;

const ITEMS = [
  { title: "item 1", id: "1" },
  { title: "item 2", id: "2" },
  { title: "item 3", id: "3" },
  { title: "item 4", id: "4" },
  { title: "item 5", id: "5" },
];

const Template: ComponentStory<typeof SortableContext> = ({
  limitToContainerEdges,
  ...restProps
}) => {
  const [items, setItems] = React.useState(ITEMS);

  const handleChange = (sortedItems) => {
    setItems(sortedItems);
  };

  return (
    <Box display="flex">
      <SortableContext
        {...restProps}
        limitToContainerEdges={limitToContainerEdges}
        value={items}
        onChange={handleChange}
      >
        {items.map((item) => (
          <SortableItem key={item?.id} id={item?.id} useHandleOnly>
            <Box
              mr="10px"
              className="dnd-item"
              border="1px solid"
              borderColor="disabled"
              p="20px"
              bg="warning"
            >
              <Stack direction="row">
                <DragHandle id={item?.id} />
                <Heading>{item.title}</Heading>
              </Stack>
            </Box>
          </SortableItem>
        ))}
      </SortableContext>
    </Box>
  );
};

export const CustomContext = Template.bind({});
CustomContext.args = {
  limitToContainerEdges: true,
};
