import React from "react";
import { palette, base } from "../themes/original";
import { DataTable } from "../components/DataTable";
import { Box } from "../components/Box";

function getShortcut(code: string) {
  return Object.keys(base.colors).find((i) => base.colors[i] === code) || "-";
}

function Display({ code }) {
  return (
    <Box
      width="40px"
      height="20px"
      border="1px solid #ccc"
      style={{ backgroundColor: code }}
    />
  );
}

const ColorsSchema = Object.keys(palette).reduce((acc: any, curr: any) => {
  const arr = Object.keys(palette[curr]).map((i) => ({
    color: curr,
    variant: i,
    shortcut: getShortcut(palette[curr][i]),
    code: palette[curr][i],
  }));
  return [...acc, ...arr];
}, []);

export const Colors = () => {
  return (
    <Box margin="0 auto" maxWidth="500px">
      <DataTable
        header={{
          cells: [
            { content: "Color" },
            { content: "Variant" },
            { content: "Shortcut" },
            { content: "Code" },
            { content: "Display" },
          ],
        }}
        rows={ColorsSchema.map((color) => ({
          cells: [
            { content: color.color },
            { content: color.variant },
            { content: color.shortcut },
            { content: color.code },
            { content: <Display code={color.code} /> },
          ],
        }))}
      />
    </Box>
  );
};

Colors.title = "Intro/Guide";

Colors.story = {
  name: "Colors",
};

export default Colors;
