import { css } from "styled-components";
import { QUERIES } from "./consts";

const getBreakpointWidth = (name, theme, pure = false) => {
  const tokens = {
    [QUERIES.SMALLMOBILE]: theme.base.breakpoints.smallMobile,
    [QUERIES.MEDIUMMOBILE]: theme.base.breakpoints.mediumMobile,
    [QUERIES.LARGEMOBILE]: theme.base.breakpoints.largeMobile,
    [QUERIES.TABLET]: theme.base.breakpoints.tablet,
    [QUERIES.DESKTOP]: theme.base.breakpoints.desktop,
  };
  if (pure) {
    return tokens[name];
  }
  return `(max-width: ${tokens[name]}px)`;
};

type MediaQueries = {
  desktop: (input: any) => any;
  tablet: (input: any) => any;
  largeMobile: (input: any) => any;
  mediumMobile: (input: any) => any;
  smallMobile: (input: any) => any;
};

export const mediaQueries: MediaQueries = Object.keys(QUERIES).reduce(
  (o, name) => ({
    ...o,
    [QUERIES[name]]: (style) =>
      css`
        @media ${({ theme }) => getBreakpointWidth(QUERIES[name], theme)} {
          ${style};
        }
      `,
  }),
  {}
) as any;

export default mediaQueries;
