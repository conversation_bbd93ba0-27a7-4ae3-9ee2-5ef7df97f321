import React, { useContext } from "react";
import { Stack } from "../Stack";
import { ChevronUpIcon, ChevronDownIcon } from "../icons";
import { ButtonIcon } from "../ButtonIcon";
import { Box, BoxProps } from "../Box";
import { AccordionContext } from "./AccordionContext";

export interface HeaderProps extends BoxProps {
  expandIcon?: React.ReactNode;
  shrinkIcon?: React.ReactNode;
  hideIcons?: boolean;
  disableExpand?: boolean;
  openOnIconClickOnly?: boolean;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
  buttonDataTest?: string;
}

const Header: React.FC<HeaderProps> = ({
  children,
  expandIcon = <ChevronDownIcon />,
  shrinkIcon = <ChevronUpIcon />,
  hideIcons,
  disableExpand,
  openOnIconClickOnly,
  onClick,
  buttonDataTest,
  ...boxProps
}) => {
  const [isOpen, setIsOpen] = useContext(AccordionContext);

  const handleHeaderClick = (event) => {
    event.stopPropagation();
    if (openOnIconClickOnly || disableExpand) {
      return undefined;
    }
    setIsOpen(!isOpen);
  };

  const handleButtonClick = (event) => {
    event.stopPropagation();
    if (disableExpand) {
      return undefined;
    }
    setIsOpen(!isOpen);
  };

  return (
    <Box
      p="8px 20px"
      onClick={onClick || handleHeaderClick}
      cursor={openOnIconClickOnly || disableExpand ? "initial" : "pointer"}
      {...boxProps}
    >
      <Stack direction="row" justify="between" align="center">
        {children}

        {!hideIcons && !disableExpand && (
          <ButtonIcon
            dataTest={buttonDataTest}
            onlyIcon
            transparent
            compact
            onClick={handleButtonClick}
          >
            {isOpen ? shrinkIcon : expandIcon}
          </ButtonIcon>
        )}
      </Stack>
    </Box>
  );
};

Header.displayName = "Header";

export { Header };
