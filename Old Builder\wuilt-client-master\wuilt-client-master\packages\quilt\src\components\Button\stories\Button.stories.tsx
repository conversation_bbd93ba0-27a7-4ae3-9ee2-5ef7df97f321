import React from "react";
import { But<PERSON> } from "../Button";
import { Stack } from "../../Stack";
import { EyeIcon } from "../../icons";
import { Box } from "../../Box";
import { Heading } from "../../Heading";

export default {
  title: "Components/Buttons/Button",
  component: Button,
};

// const sizes = ["small", "normal", "large"];
const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export const Playground = () => {
  const [loading, setLoading] = React.useState(false);

  const handleLoading = async () => {
    setLoading(true);
    await sleep(2000);
    setLoading(false);
  };

  // const IconBefor = getIcon(getIcons("prefix", "PlusCircle"));
  // const IconAfter = getIcon(getIcons("suffix", "ChevronDown"));
  return (
    <Box maxWidth="50%">
      <Stack direction="column">
        {/* <Button
          type={
            select(
              "type",
              Object.values(TYPE_OPTIONS),
              TYPE_OPTIONS.PRIMARY,
              "Component"
            ) as any
          }
          size={select("size", sizes, "normal", "Component") as any}
          fullWidth={boolean("fullWidth", false, "Component")}
          disabled={boolean("disabled", false, "Component")}
          prefixIcon={IconBefor && <IconBefor />}
          suffixIcon={IconAfter && <IconAfter />}
          onClick={action("clicked")}
          asComponent={
            select("asComponent", ["a", "button"], "button", "Component") as any
          }
          href="https://wuilt.com"
          spaceAfter="largest"
        >
          {text("children", "Default Button", "Component")}
        </Button> */}
        <Button>Default Button</Button>
        <Button disabled>Default Disabled</Button>
        <Button color="danger" prefixIcon={<EyeIcon />}>
          Danger with icon
        </Button>
        <Button outlined>Outlined</Button>
        <Button plain color="gold">
          Gold Plain
        </Button>
        <Button plain compact underlined>
          Plain Compact Underlined
        </Button>
        <Button squared outlined>
          Squared Outlined
        </Button>
        <Button
          color="primary"
          prefixIcon={<EyeIcon color="primary" />}
          outlined
          fullWidth
          transparent
          contentAlign="center"
          contentWidth="auto"
        >
          Full width with icon, transparent and centered
        </Button>

        <Button onClick={handleLoading} loading={loading}>
          Click Me to Start Loading
        </Button>

        <Heading my="20px">All Buttons Colors</Heading>
        <Stack direction="row" wrap>
          <Button color="primary">primary</Button>
          <Button color="secondary">secondary</Button>
          <Button color="info">info</Button>
          <Button color="grey">grey</Button>
          <Button color="overlay">overlay</Button>
          <Button color="disabled">disabled</Button>
          <Button color="gold">gold</Button>
          <Button color="success">success</Button>
          <Button color="warning">warning</Button>
          <Button color="danger">danger</Button>
          <Button color="black">black</Button>
          <Button color="white">white</Button>
          <Button color="transparent">transparent</Button>
        </Stack>
      </Stack>
    </Box>
  );
};
