import React, { createContext, useState } from "react";
import cuid from "cuid";
import { Stack, StackProps } from "../Stack";

export const SelectTabsContext = createContext({
  name: "",
  // eslint-disable-next-line no-constant-binary-expression
  value: 0 as any,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onChange: (value: any) => {},
});

export interface SelectTabsProps<Type = string> {
  name?: string;
  value?: Type;
  children?: any;
  defaultValue?: Type;
  fitContentWidth?: boolean;
  onChange?: (value: Type) => void;
  stackProps?: StackProps;
}

function SelectTabs<Type>({
  value,
  onChange,
  children,
  fitContentWidth = false,
  defaultValue,
  name = cuid(),
  stackProps,
}: SelectTabsProps<Type>) {
  const [selectedValue, setSelectedValue] = useState<Type>(
    (defaultValue || value) as Type
  );

  const handleChange = (value: Type) => {
    setSelectedValue(value);
    onChange?.(value);
  };

  return (
    <SelectTabsContext.Provider
      value={{ name, value: selectedValue, onChange: handleChange }}
    >
      <Stack
        direction="row"
        spacing="none"
        width={`${fitContentWidth ? "fit-content" : "auto"}`}
        overflowX="auto"
        overflowY="hidden"
        border="1px solid "
        borderColor="darkerGrey"
        borderRight="none"
        borderRadius="8px"
        backgroundColor="white"
        {...stackProps}
      >
        {children}
      </Stack>
    </SelectTabsContext.Provider>
  );
}

export { SelectTabs };
