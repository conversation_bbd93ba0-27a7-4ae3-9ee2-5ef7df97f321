import React, { useState } from "react";
import { ComponentStory, ComponentMeta } from "@storybook/react";
import { Resizable } from "./Resizable";

const imageSrc =
  "https://images.unsplash.com/photo-1525609004556-c46c7d6cf023?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NHx8Y2Fyc3xlbnwwfHwwfHw%3D&w=1000&q=80";

// https://storybook.js.org/docs/react/essentials/controls
export default {
  title: "Components/Resizable",
  component: Resizable,
  argTypes: {
    className: { control: false },
  },
} as ComponentMeta<typeof Resizable>;

const Template: ComponentStory<typeof Resizable> = (props) => {
  const [value, onChange] = useState({ width: 400, height: 400 });
  console.log(value);

  return (
    <Resizable all value={value} onChange={onChange} {...props}>
      <img
        src={imageSrc}
        width="100%"
        height="100%"
        style={{ objectFit: "cover" }}
      />
    </Resizable>
  );
};

export const Playground = Template.bind({});
Playground.args = {};
