import React from "react";

export default function ButtonWithTextIcon() {
  return (
    <svg
      width="76"
      height="34"
      viewBox="0 0 76 34"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_d_3830_10732)">
        <rect x="2" y="1" width="72" height="30" rx="15" fill="white" />
        <rect
          x="2.5"
          y="1.5"
          width="71"
          height="29"
          rx="14.5"
          stroke="#D0D5DD"
        />
        <path
          d="M16.375 16.375C16.375 16.375 17.3594 17.5 19 17.5C20.6406 17.5 21.625 16.375 21.625 16.375M21.0625 12.625H21.07M16.9375 12.625H16.945M15.25 20.5V22.2516C15.25 22.6513 15.25 22.8511 15.3319 22.9537C15.4032 23.043 15.5112 23.0949 15.6254 23.0948C15.7567 23.0946 15.9128 22.9698 16.2248 22.7201L18.0139 21.2889C18.3794 20.9965 18.5621 20.8503 18.7656 20.7463C18.9461 20.6541 19.1383 20.5867 19.3369 20.5459C19.5608 20.5 19.7948 20.5 20.2628 20.5H22.15C23.4101 20.5 24.0402 20.5 24.5215 20.2548C24.9448 20.039 25.289 19.6948 25.5048 19.2715C25.75 18.7902 25.75 18.1601 25.75 16.9V12.85C25.75 11.5899 25.75 10.9598 25.5048 10.4785C25.289 10.0552 24.9448 9.71095 24.5215 9.49524C24.0402 9.25 23.4101 9.25 22.15 9.25H15.85C14.5899 9.25 13.9598 9.25 13.4785 9.49524C13.0552 9.71095 12.711 10.0552 12.4952 10.4785C12.25 10.9598 12.25 11.5899 12.25 12.85V17.5C12.25 18.1975 12.25 18.5462 12.3267 18.8323C12.5347 19.6088 13.1412 20.2153 13.9177 20.4233C14.2038 20.5 14.5525 20.5 15.25 20.5ZM21.4375 12.625C21.4375 12.8321 21.2696 13 21.0625 13C20.8554 13 20.6875 12.8321 20.6875 12.625C20.6875 12.4179 20.8554 12.25 21.0625 12.25C21.2696 12.25 21.4375 12.4179 21.4375 12.625ZM17.3125 12.625C17.3125 12.8321 17.1446 13 16.9375 13C16.7304 13 16.5625 12.8321 16.5625 12.625C16.5625 12.4179 16.7304 12.25 16.9375 12.25C17.1446 12.25 17.3125 12.4179 17.3125 12.625Z"
          stroke="#98A2B3"
          stroke-width="1.35"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <rect x="32" y="13" width="34" height="6" rx="3" fill="#D0D5DD" />
      </g>
      <defs>
        <filter
          id="filter0_d_3830_10732"
          x="0"
          y="0"
          width="76"
          height="34"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_3830_10732"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_3830_10732"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
}
