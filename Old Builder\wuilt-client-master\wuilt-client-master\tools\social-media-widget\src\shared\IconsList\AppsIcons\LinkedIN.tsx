import { TIconType } from "../types";

export function LinkedIN({ size = 40, width, height, color }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.4325 12.3522C14.4325 13.6598 13.4482 14.7061 11.8678 14.7061H11.8389C10.3172 14.7061 9.3335 13.6598 9.3335 12.3522C9.3335 11.017 10.3472 10 11.8977 10C13.4482 10 14.403 11.017 14.4325 12.3522ZM14.1339 16.5649V30.183H9.60151V16.5649H14.1339ZM30.4795 30.183L30.4797 22.3748C30.4797 18.1919 28.2437 16.2451 25.2611 16.2451C22.8547 16.2451 21.7773 17.5669 21.1757 18.4941V16.5652H16.6427C16.7025 17.8431 16.6427 30.1833 16.6427 30.1833H21.1757V22.5779C21.1757 22.1709 21.2051 21.7649 21.325 21.4735C21.6526 20.6604 22.3984 19.8185 23.6506 19.8185C25.2913 19.8185 25.9472 21.0673 25.9472 22.8973V30.183H30.4795Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
