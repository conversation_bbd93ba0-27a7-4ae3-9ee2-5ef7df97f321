const { composePlugins, withNx } = require("@nrwl/webpack");
const { withReact } = require("@nrwl/react");

// Nx plugins for webpack.
module.exports = composePlugins(withNx(), withReact(), (config) => {
  // Update the webpack config as needed here.
  // e.g. `config.plugins.push(new MyPlugin())`
  config.entry = {
    ...config.entry,
    app: "./src/app/app.tsx",
    widget: "./src/app/widget.tsx",
  };

  config.output = {
    ...config.output,
    library: "wuilt",
    libraryTarget: "umd",
    umdNamedDefine: true,
    filename: "[name].dev.js",
  };

  return config;
});
