import React from "react";
import { useTheme } from "styled-components";
import { select, text } from "@storybook/addon-knobs";
import { Icon } from "./Icon";
import IconList from "./IconsList";

export default {
  title: "Components/Icon",
  component: Icon,
};

const List = () => {
  const theme = useTheme();
  return (
    <IconList
      size={select(
        "size",
        ["xxxs", "xxs", "xs", "sm", "md", "lg", "xl", "xxl", "xxl"],
        "xxl"
      )}
      customColor={text("color", theme.palette.ink.normal)}
    />
  );
};

export const ListAllIcons = () => {
  return <List />;
};
