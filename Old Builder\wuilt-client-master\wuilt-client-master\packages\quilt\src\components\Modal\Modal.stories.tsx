import React from "react";
import { text } from "@storybook/addon-knobs";
import { Modal } from "./Modal";
import { Stack } from "../Stack";
import { Button } from "../Button";

export default {
  title: "Components/Modals/Modal",
  components: Modal,
};

export const Playground = () => {
  const [openModal, setOpenModal] = React.useState(false);

  // const show = boolean("show", true, "Component");

  return (
    <>
      <Button onClick={() => setOpenModal((prev) => !prev)}>Open Modal</Button>

      <Modal
        onClose={() => setOpenModal(false)}
        show={openModal}
        modalWidth="large"
      >
        <Modal.Header>
          {text("Title", "Arabic version of your product", "Component")}
        </Modal.Header>
        <Modal.Body>
          <p>
            Lorem, ipsum dolor sit amet consectetur adipisicing elit. Qui hic ea
            quibusdam necessitatibus dolorum consequatur, distinctio non
            expedita, ducimus harum fuga nam itaque amet et aperiam unde.
            Necessitatibus, commodi ratione. Debitis molestiae soluta blanditiis
            ipsa inventore nisi impedit? Quis necessitatibus voluptatibus autem
            dolorum excepturi aspernatur enim architecto, cum doloremque
            corporis assumenda, at omnis tenetur modi neque ab totam eius fugit.
            Omnis tenetur nobis adipisci tempore sit ipsa hic? Obcaecati eveniet
            numquam officia ipsam vero doloribus ullam. Pariatur ipsam illo hic
            eos quod, repellat sapiente impedit, nihil ducimus nulla ratione
            enim? Fugiat eligendi eveniet sint praesentium reiciendis ab, optio
            aspernatur? Rem tenetur dolorem quam nihil iure quibusdam et ut
            tempora illo ad animi nam perferendis, harum, repellat deleniti
            omnis qui exercitationem? Blanditiis illo sapiente sunt fugiat
            explicabo perferendis ea natus tempora quasi, et maxime modi optio
            tempore labore, quis, placeat atque nobis! Ratione quod animi
            delectus reiciendis harum sit suscipit. Porro.
          </p>
          <p>
            Lorem ipsum dolor sit amet consectetur, adipisicing elit. Accusamus
            expedita tempora natus blanditiis provident eaque error, quaerat
            nihil tenetur deserunt doloremque sit libero qui possimus doloribus
            quo facere non debitis. Doloremque vitae eius omnis quos eligendi
            voluptatum maiores in atque sint recusandae eveniet iusto quisquam
            magni distinctio, vel consequuntur laborum! Necessitatibus quasi ex
            quidem, tenetur magnam suscipit dolor impedit dolorem? Dolores
            voluptates molestiae soluta voluptate praesentium, tenetur repellat
            in doloribus necessitatibus magnam corrupti cum aperiam a esse quos
            veritatis. Impedit mollitia officiis fugit molestiae, voluptates
            ullam doloremque. Itaque, nam necessitatibus! Voluptate sunt ipsum,
            corporis quo fugit mollitia fugiat ad itaque nostrum? Ad rerum
            consequuntur error quisquam, natus velit vel? Impedit blanditiis
            inventore modi laborum? Officia qui quisquam beatae quae vitae.
            Corporis, libero reiciendis repudiandae neque cumque totam
            architecto rem, nesciunt delectus beatae ducimus qui eum iste?
            Reiciendis ratione tempore quia dolorum enim incidunt atque! Quos
            aut tenetur aspernatur velit inventore!
          </p>
        </Modal.Body>
        <Modal.Footer>
          <Stack direction="row" justify="between">
            <Button>test</Button>
            <Button>test</Button>
          </Stack>
        </Modal.Footer>
      </Modal>
    </>
  );
};
