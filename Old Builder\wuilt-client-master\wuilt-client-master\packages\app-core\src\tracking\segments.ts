import { AnalyticsBrowser } from "@segment/analytics-next";
import { Attributes } from "./types";

export const createSegmentProvider = ({ config: { writeKey }, user }) => {
  const analytics = AnalyticsBrowser.load(
    {
      writeKey,
      // cdnURL: 'https://79473c3dd1c7.ngrok.app/segment-cdn',
    }
    // {
    //   integrations: {
    //     'Segment.io': {
    //       apiHost: '79473c3dd1c7.ngrok.app/segment/v1',
    //       protocol: 'https', // optional
    //     },
    //   },
    // }
  );
  if (user) {
    analytics.identify(user.id, user);
  }
  return {
    setContext: (key: string, context?: Attributes) => {
      //
    },
    pushError: (message: string, context?: Attributes) => {
      //
    },
    pushEvent: (name: string, attributes?: Attributes) => {
      analytics.track(name, attributes);
    },
    view: (name: string, attributes?: Attributes) => {
      analytics.page(undefined, name, attributes);
    },
  };
};
