import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";

export type AuthUser = {
  id: string;
  name: string;
  email: string;
  phone: string;
  decodedId: string;
  locale: string;
  country: { code: string; name: string };
  meta: {
    isPartner: boolean;
    shouldFillQuestionnaire: boolean;
    requireDemo: boolean;
    demoCompleted: boolean;
  };
};

type AuthType = {
  loading: boolean;
  user?: AuthUser;
  logout?: () => Promise<void>;
  updateUser?: React.Dispatch<React.SetStateAction<AuthUser | undefined>>;
  refetchUser?: () => Promise<AuthUser>;
};

const BLOCKED_USERS_IDS = ["552103", "518512"];

export const isBlockedUser = (userId: string) => {
  return BLOCKED_USERS_IDS.includes(userId);
};

const AuthContext = createContext<AuthType>({
  loading: true,
});

export const AuthProvider: React.FC<{
  children: React.ReactNode;
  baseEndpoint: string;
}> = ({ children, baseEndpoint }) => {
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<AuthUser>();

  const logout = useCallback(async () => {
    (async () => {
      await fetch(`${baseEndpoint}/logout`, {
        method: "POST",
        credentials: "include",
      });
      setUser(undefined);
    })();
  }, [baseEndpoint]);

  const fetchUser = useCallback(async () => {
    if (!baseEndpoint) {
      throw new Error("baseEndpoint is required");
    }
    setLoading(true);
    const res = await fetch(`${baseEndpoint}/user-info`, {
      method: "GET",
      credentials: "include",
      headers: { "Content-Type": "application/json" },
    });

    if (res.status === 401) {
      setUser(undefined);
      setLoading(false);
      return;
    }

    const data = await res.json();
    const user = data?.user;
    if (isBlockedUser(user?.id!)) {
      logout();
      window.location.href = "/";
      return;
    }

    setUser({
      id: `User_${user?.id}`,
      name: user.name,
      email: user.email,
      decodedId: user.id,
      phone: user.verifiedPhone || user.validPhone || user.phone,
      country: { code: user.countryCode, name: user.countryName },
      locale: user.locale,
      meta: {
        isPartner: !!user.isPartner,
        shouldFillQuestionnaire: user.questionnaireCompleted === false,
        requireDemo: !!user.requireDemo,
        demoCompleted: !!user.demoCompleted,
      },
    });
    setLoading(false);

    return user;
  }, [baseEndpoint]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  const value: AuthType = React.useMemo(
    () => ({
      loading,
      user,
      logout,
      updateUser: setUser,
      refetchUser: fetchUser,
    }),
    [fetchUser, loading, logout, user]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => useContext(AuthContext);
