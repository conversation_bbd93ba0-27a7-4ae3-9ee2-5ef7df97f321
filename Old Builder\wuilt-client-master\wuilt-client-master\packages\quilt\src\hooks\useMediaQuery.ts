import { useCallback, useEffect, useState } from "react";
import { DefaultTheme, useTheme } from "styled-components";
import { ThemeType } from "../themes";

function getWidth() {
  let xWidth: number | null = null;
  if (window.screen != null) xWidth = window.screen.availWidth;
  if (window.innerWidth != null) xWidth = window.innerWidth;
  if (document.body != null) xWidth = document.body.clientWidth;
  return xWidth;
}

function getDevice(screenWidth: number, theme: ThemeType) {
  return screenWidth <= theme.base.breakpoints.largeMobile
    ? "isMobile"
    : screenWidth <= theme.base.breakpoints.tablet
    ? "isTablet"
    : screenWidth <= theme.base.breakpoints.desktop
    ? "isDesktop"
    : "isLargeDesktop";
}

const DEVICES = ["isMobile", "isTablet", "isDesktop", "isLargeDesktop"];
function isThisDeviceOrBelow(currentDevice: string, device: string) {
  return (
    DEVICES.findIndex((elm) => elm === currentDevice) <=
    DEVICES.findIndex((elm) => elm === device)
  );
}

function useDeviceHook(device: string) {
  const theme = useTheme() as ThemeType;
  const [state, setState] = useState<boolean | null>(null);

  const handler = useCallback(() => {
    const screenWidth = getWidth();
    if (screenWidth && theme) {
      const currentDevice = getDevice(screenWidth, theme);
      setState(isThisDeviceOrBelow(currentDevice, device));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [theme]);

  useEffect(() => {
    handler();
    if (window) window.addEventListener("resize", handler);
    return () => window.removeEventListener("resize", handler);
  }, [handler]);

  return state;
}

const useMediaQuery = {
  isMobile: () => useDeviceHook("isMobile"),
  isTablet: () => useDeviceHook("isTablet"),
  isDesktop: () => useDeviceHook("isDesktop"),
  isLargeDesktop: () => useDeviceHook("isLargeDesktop"),
};
export { useMediaQuery };
