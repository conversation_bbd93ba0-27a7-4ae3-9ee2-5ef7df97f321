import React from "react";
import { Heading } from "../Heading";
import { VirtualizedList as VirtualizedListComponent } from "./VirtualizedList";
import { VirtualizedGrid as VirtualizedGridComponent } from "./VirtualizedGrid";

export default {
  title: "Components/Virtualized",
  component: VirtualizedListComponent,
};

const ITEMS = [
  { title: "item 1", id: 1 },
  { title: "item 2", id: 2 },
  { title: "item 3", id: 3 },
  { title: "item 4", id: 4 },
  { title: "item 5", id: 5 },
  { title: "item 6", id: 6 },
  { title: "item 7", id: 7 },
  { title: "item 8", id: 8 },
  { title: "item 9", id: 9 },
  { title: "item 10", id: 10 },
  { title: "item 11", id: 11 },
  { title: "item 12", id: 12 },
  { title: "item 13", id: 13 },
  { title: "item 14", id: 14 },
  { title: "item 15", id: 15 },
  { title: "item 16", id: 16 },
  { title: "item 17", id: 17 },
  { title: "item 18", id: 18 },
  { title: "item 19", id: 19 },
  { title: "item 20", id: 20 },
  { title: "item 21", id: 21 },
  { title: "item 22", id: 22 },
  { title: "item 23", id: 23 },
  { title: "item 24", id: 24 },
];

export const VirtualizedList = () => {
  return (
    <VirtualizedListComponent
      items={ITEMS}
      itemsTotalCount={ITEMS.length}
      placeholder="Search"
      isLoading
    >
      {({ style, index, data }) => {
        const item = data[index];
        return (
          <div key={item.id} style={{ ...style, margin: "20px" }}>
            <Heading>{item.title}</Heading>
          </div>
        );
      }}
    </VirtualizedListComponent>
  );
};

export const VirtualizedGrid = () => {
  return (
    <div>
      <VirtualizedGridComponent
        items={ITEMS.map((i) => i.title)}
        rowHeight={120}
        columnCount={3}
        columnWidth={300}
        placeholder="Search"
        listWidth={1000}
      >
        {({ style, item }) => {
          return (
            <div key={item} style={style}>
              <div
                style={{ padding: "20px", background: "red", margin: "20px" }}
              >
                <Heading>{item}</Heading>
              </div>
            </div>
          );
        }}
      </VirtualizedGridComponent>
    </div>
  );
};
