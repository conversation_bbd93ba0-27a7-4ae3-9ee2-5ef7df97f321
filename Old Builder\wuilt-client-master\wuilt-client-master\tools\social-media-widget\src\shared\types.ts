export enum AppsEnum {
  Whatsapp = "Whatsapp",
  Email = "Email",
  Slack = "Slack",
  Instagram = "Instagram",
  SnapChat = "SnapChat",
  TikTok = "TikTok",
  Telegram = "Telegram",
  Messenger = "Messenger",
  Discord = "Discord",
  LinkedIn = "LinkedIn",
  SMS = "SMS",
  Pinterest = "Pinterest",
  Skype = "Skype",
  Facebook = "Facebook",
  Twitter = "Twitter",
  Phone = "Phone",
  Map = "Map",
  Custom = "Custom",
}

export enum ButtonIconsEnum {
  MessageChatCircleSolid = "MessageChatCircleSolid",
  MessageChatSquareSolid = "MessageChatSquareSolid",
  MessageCircleSolid = "MessageCircleSolid",
  MessageDotsCircleSolid = "MessageDotsCircleSolid",
  MessageDotsSquareSolid = "MessageDotsSquareSolid",
  MessageSmileCircleSolid = "MessageSmileCircleSolid",
  MessageSmileSquareSolid = "MessageSmileSquareSolid",
  MessageSquareSolid = "MessageSquareSolid",
  MessageTextCircleSolid = "MessageTextCircleSolid",
  MessageTextSquareSolid = "MessageTextSquareSolid",
  SendSolid = "SendSolid",
  MessageChatCircle = "MessageChatCircle",
  MessageChatSquare = "MessageChatSquare",
  MessageCircle = "MessageCircle",
  MessageDotsCircle = "MessageDotsCircle",
  MessageDotsSquare = "MessageDotsSquare",
  MessageSmileCircle = "MessageSmileCircle",
  MessageSmileSquare = "MessageSmileSquare",
  MessageSquare = "MessageSquare",
  MessageTextCircle = "MessageTextCircle",
  MessageTextSquare = "MessageTextSquare",
  Send = "Send",
}

export enum AnimationEnum {
  None = "None",
  RubberBand = "RubberBand",
  Heartbeat = "Heartbeat",
  Bounce = "Bounce",
  HeadShake = "HeadShake",
  Pulse = "Pulse",
  Tada = "Tada",
  Wobble = "Wobble",
  Jello = "Jello",
  Ring = "Ring",
  DoubleRing = "DoubleRing",
  MoveIn = "MoveIn",
}

export enum WidgetPositionEnum {
  Left = "Left",
  Right = "Right",
}

export enum WidgetOrientationEnum {
  Vertical = "Vertical",
  Horizontal = "Horizontal",
}

export enum WidgetPagesEnum {
  AllPages = "AllPages",
  SelectedPages = "SelectedPages",
}

export type TWidgetContent = {
  withText: boolean;
  openText: string;
  closeText: string;
  icon: ButtonIconsEnum;
};

export type TWidgetBorder = {
  color: string;
  thickness: string;
};

export type TWidgetShadow = {
  color: string;
  opacity: number;
};

export type TWidgetBackground = {
  color: string;
  gradient: boolean;
};

export type TWidgetStyle = {
  background: TWidgetBackground;
  size: string;
  radius: string;
  border?: TWidgetBorder;
  shadow?: TWidgetShadow;
  transparent?: boolean;
  animation: AnimationEnum;
};

export type TWidgetPositionShift = {
  vertical: string;
  horizontal: string;
};

export type TWidgetPages = {
  type: WidgetPagesEnum;
  displayOn: string[];
  hideOn: string[];
};

export type TWidgetDisplay = {
  position: WidgetPositionEnum;
  shift: TWidgetPositionShift;
  orientation: WidgetOrientationEnum;
  showOnDesktop: boolean;
  showOnMobile: boolean;
  pages: TWidgetPages;
};

export type TWidgetAppearance = {
  content: TWidgetContent;
  style: TWidgetStyle;
  display: TWidgetDisplay;
};

export type TWhatsAppAgent = {
  name: string;
  position: string;
  message: string;
  image: string;
  phone: string;
};

export type TWhatAppForm = {
  title: string;
  subtitle: string;
};

export type TWhatsAppSettings = {
  agents: TWhatsAppAgent[];
  form: TWhatAppForm;
};

export type TAppSettings = {
  name: AppsEnum;
  value: string;
  onHoverText: string;
  background: TWidgetBackground;
  whatsAppSettings?: TWhatsAppSettings;
};

export type TWidgetSettings = {
  appearance: TWidgetAppearance;
  apps: TAppSettings[];
};
