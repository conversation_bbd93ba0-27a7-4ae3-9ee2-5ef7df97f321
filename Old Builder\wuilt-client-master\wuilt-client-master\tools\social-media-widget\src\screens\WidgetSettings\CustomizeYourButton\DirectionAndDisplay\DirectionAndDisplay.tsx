import React from "react";
import { TWidgetAppearance, TWidgetSettings } from "../../../../shared/types";
import { Stack } from "@wuilt/quilt";
import Direction from "./Direction";
import Display from "./Display";

interface DirectionAndDisplayProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function DirectionAndDisplay({ appearance, update }: DirectionAndDisplayProps) {
  return (
    <Stack
      mt="24px"
      boxShadow="0px 1px 2px 0px #1018280D"
      width="100%"
      border="1px solid #EAECF0"
      borderRadius="10px"
      padding="16px"
      direction="row"
      align="start"
    >
      <Direction appearance={appearance} update={update} />
      <Display appearance={appearance} update={update} />
    </Stack>
  );
}

export default DirectionAndDisplay;
