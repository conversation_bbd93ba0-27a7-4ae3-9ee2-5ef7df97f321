import React, {
  useRef,
  useEffect,
  useMemo,
  forwardRef,
  MutableRefObject,
} from "react";
import styled from "styled-components";
import resolvePopoverPosition from "./helpers/resolvePopoverPosition";
import resolvePopoverHorizontal from "./helpers/resolvePopoverHorizontal";
import calculatePopoverPosition from "./helpers/calculatePopoverPosition";
import calculateVerticalPosition from "./helpers/calculateVerticalPosition";
import calculateHorizontalPosition from "./helpers/calculateHorizontalPosition";
import getScrollableParent from "./helpers/getScrollableParent";
import { useClickOutside } from "../../hooks/useClickOutside";
import { useDimensions } from "./useDimensions";

const StyledPopoverParent = styled.div<{
  shownMobile;
  shown;
  width;
  anchor: any;
  containerLeft: any;
  containerWidth: any;
  popoverWidth: any;
  position: any;
  containerTop: any;
  containerHeight: any;
  popoverHeight: any;
  overlapped: any;
  containerPureTop: any;
  fixed: any;
}>`
  position: ${({ fixed }) => (fixed ? "fixed" : "absolute")};
  height: auto;
  box-sizing: border-box;
  max-height: none;
  z-index: 1000;
  left: auto;
  right: auto;
  bottom: auto;
  width: ${({ width }) => (width ? `${width}` : "auto")};
  opacity: ${({ shown }) => (shown ? "1" : "0")};
  transform: none;

  &:focus {
    outline: 0;
  }

  ${resolvePopoverPosition}
  ${resolvePopoverHorizontal}
`;

const StyledPopoverContent = styled.div``;

const PopoverContentWrapper = forwardRef<any, any>(
  (
    {
      children,
      noFocus,
      onClose,
      width,
      dataTest,
      preferredPosition,
      preferredAlign,
      containerRef,
      overlapped,
      shown,
      fixed,
    },
    ref
  ) => {
    const fallbackPopoverRef = useRef<HTMLDivElement>(null);
    const popover: MutableRefObject<any> =
      (ref as MutableRefObject<any>) || fallbackPopoverRef;
    const content = useRef(null);
    const position = calculatePopoverPosition(
      preferredPosition,
      preferredAlign
    );
    const scrollableParent = useMemo(
      () => getScrollableParent(containerRef.current),
      [containerRef]
    );
    const dimensions = useDimensions({
      containerRef,
      popover,
      content,
      fixed,
      scrollableParent,
      children,
    });
    const verticalPosition = calculateVerticalPosition(position[0], dimensions);
    const horizontalPosition = calculateHorizontalPosition(
      position[1],
      dimensions
    );

    useEffect(() => {
      if (noFocus) {
        return () => {};
      }
      const timer = setTimeout(() => {
        if (popover.current !== null) {
          popover.current?.focus();
        }
      }, 100);
      return () => clearTimeout(timer);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useClickOutside(popover, onClose);

    return (
      <StyledPopoverParent
        shownMobile={shown}
        shown={shown && verticalPosition && horizontalPosition}
        anchor={horizontalPosition}
        position={verticalPosition}
        containerTop={dimensions.containerTop}
        containerLeft={dimensions.containerLeft}
        containerPureTop={dimensions.containerPureTop}
        containerHeight={dimensions.containerHeight}
        containerWidth={dimensions.containerWidth}
        popoverHeight={dimensions.popoverHeight}
        popoverWidth={dimensions.popoverWidth}
        width={width}
        overlapped={overlapped}
        fixed={fixed}
        role="tooltip"
        ref={popover}
        tabIndex={0}
        data-test={dataTest}
      >
        <StyledPopoverContent ref={content}>
          {children}
        </StyledPopoverContent>
      </StyledPopoverParent>
    );
  }
);

export default PopoverContentWrapper;
