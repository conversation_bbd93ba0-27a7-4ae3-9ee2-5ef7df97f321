import React, { useEffect, useState } from "react";
import { Box, Stack } from "@wuilt/quilt";
import { HexAlphaColorPicker, HexColorInput } from "react-colorful";
import { Popover } from "../../ui";
const HASH = `#`;
interface ColorInputProps {
  value: string | undefined;
  onChange: (color: string) => void;
  customStyle?: React.CSSProperties;
}
function ColorInput({ value, onChange, customStyle }: ColorInputProps) {
  const [color, setColor] = useState(value);
  useEffect(() => {
    setColor(value);
  }, [value]);
  const onBlur = () => {
    onChange(color!);
  };
  return (
    <Stack>
      <Popover
        button={
          <Stack
            direction="row"
            align="center"
            border="1px solid #D0D5DD"
            borderRadius="8px"
            padding="0 10px"
            height="35.6px"
            spacing="condensed"
            style={customStyle}
          >
            <Stack>
              <Box
                height="20px"
                border="2px solid #EAECF0"
                borderRadius="4px"
                width="20px"
                style={{ backgroundColor: color }}
              />
            </Stack>
            {HASH}
            <HexColorInput
              style={{ border: "none", outline: "none", width: "100%" }}
              alpha
              color={color}
              onChange={setColor}
              onBlur={onBlur}
              inputMode="numeric"
            />
          </Stack>
        }
        content={
          <HexAlphaColorPicker
            color={color}
            onChange={setColor}
            onBlur={onBlur}
          />
        }
      />
    </Stack>
  );
}

export default ColorInput;
