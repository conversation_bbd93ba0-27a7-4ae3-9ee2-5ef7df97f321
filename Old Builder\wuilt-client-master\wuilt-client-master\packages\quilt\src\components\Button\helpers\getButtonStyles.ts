import { color as getColor } from "../../../utils";

export type ButtonStyles = {
  background: any;
  backgroundHover: any;
  backgroundActive: any;
  backgroundFocus: any;
  foreground: any;
  foregroundHover: any;
  foregroundActive: any;
  borderColor: any;
  borderColorHover: any;
  borderColorActive: any;
  boxShadow: any;
  boxShadowHover: any;
  boxShadowActive: any;
  boxShadowFocus: any;
};

const BGColorMap = {
  primary: ["product", "normal"],
  secondary: ["ink", "normal"],
  info: ["ink", "light"],
  grey: ["cloud", "normal"],
  overlay: ["cloud", "dark"],
  disabled: ["cloud", "darker"],
  success: ["green", "normal"],
  warning: ["orange", "light"],
  orange: ["orange", "normal"],
  gold: ["orange", "gold"],
  danger: ["red", "normal"],
  white: ["white", "normal"],
  black: ["black", "normal"],
  transparent: ["transparent", "normal"],
  darkerGrey: ["cloud", "darkest"],
  lightGreen: ["product", "light"],
  lightDanger: ["red", "light"],
};

const TextColorMap = {
  primary: ["white", "normal"],
  secondary: ["white", "normal"],
  info: ["white", "normal"],
  grey: ["ink", "normal"],
  overlay: ["ink", "normal"],
  disabled: ["ink", "normal"],
  success: ["white", "normal"],
  warning: ["orange", "dark"],
  orange: ["white", "normal"],
  gold: ["white", "normal"],
  danger: ["white", "normal"],
  white: ["ink", "normal"],
  black: ["white", "normal"],
  transparent: ["ink", "normal"],
  darkerGrey: ["ink", "normal"],
  lightGreen: ["product", "normal"],
  lightDanger: ["red", "normal"],
};

const BdrColorMap = {
  primary: ["product", "normal"],
  secondary: ["ink", "normal"],
  info: ["ink", "light"],
  grey: ["ink", "lighter"],
  overlay: ["ink", "lighter"],
  disabled: ["ink", "lighter"],
  success: ["green", "normal"],
  warning: ["orange", "dark"],
  orange: ["orange", "normal"],
  gold: ["orange", "gold"],
  danger: ["red", "normal"],
  white: ["ink", "lighter"],
  black: ["black", "normal"],
  transparent: ["transparent", "normal"],
  darkerGrey: ["cloud", "normal"],
  lightGreen: ["transparent", "normal"],
  lightDanger: ["red", "normal"],
};

const getButtonStyles = ({
  theme,
  plain,
  outlined,
  transparent,
  color,
}): ButtonStyles => {
  const BGMainColor = BGColorMap?.[color]?.[0] || "product";
  const BGVariantColor = BGColorMap?.[color]?.[1] || "normal";
  const TxtMainColor = TextColorMap?.[color]?.[0] || "ink";
  const TxtVariantColor = TextColorMap?.[color]?.[1] || "normal";
  const BdMainColor = BdrColorMap?.[color]?.[0] || "product";
  const BdVariantColor = BdrColorMap?.[color]?.[1] || "normal";

  const defaultStyles = {
    foreground: getColor(TxtMainColor, TxtVariantColor)(theme),
    foregroundHover: getColor(TxtMainColor, `${TxtVariantColor}Hover`)(theme),
    foregroundFocus: getColor(TxtMainColor, `${TxtVariantColor}Hover`)(theme),
    foregroundActive: getColor(TxtMainColor, `${TxtVariantColor}Active`)(theme),
    background: getColor(BGMainColor, `${BGVariantColor}`)(theme),
    backgroundHover: getColor(BGMainColor, `${BGVariantColor}Hover`)(theme),
    backgroundFocus: getColor(BGMainColor, `${BGVariantColor}Hover`)(theme),
    backgroundActive: getColor(BGMainColor, `${BGVariantColor}Active`)(theme),
    borderColor: getColor(BdMainColor, `${BdVariantColor}`)(theme),
    borderColorHover: getColor(BdMainColor, `${BdVariantColor}Hover`)(theme),
    borderColorFocus: getColor(BdMainColor, `${BdVariantColor}Hover`)(theme),
    borderColorActive: getColor(BdMainColor, `${BdVariantColor}Active`)(theme),
    boxShadow: null,
    boxShadowHover: `0 0 0 3px ${theme.base.colors[color]}`,
    boxShadowFocus: `0 0 0 3px ${theme.base.colors[color]}`,
    boxShadowActive: `inset 0 0 6px 3px ${theme.base.colors[color]}`,
  };

  const outlinedStyles = {
    ...defaultStyles,
    foreground: getColor(BGMainColor, `${BGVariantColor}`)(theme),
    foregroundHover: getColor(BGMainColor, `${BGVariantColor}Hover`)(theme),
    foregroundFocus: getColor(BGMainColor, `${BGVariantColor}Hover`)(theme),
    foregroundActive: getColor(BGMainColor, `${BGVariantColor}Active`)(theme),
    background: getColor("white", "normal")(theme),
    backgroundHover: getColor("white", "normalHover")(theme),
    backgroundFocus: getColor("white", "normalHover")(theme),
    backgroundActive: getColor("white", "normalActive")(theme),
    boxShadowHover: `0 0 0 1px ${theme.base.colors[color]}`,
    boxShadowFocus: `0 0 0 1px ${theme.base.colors[color]}`,
    boxShadowActive: `inset 0 0 6px 3px ${theme.base.colors[color]}`,
  };

  const plainStyles = {
    foreground: getColor(BGMainColor, `${BGVariantColor}`)(theme),
    foregroundHover: getColor(BGMainColor, `${BGVariantColor}Hover`)(theme),
    foregroundFocus: getColor(BGMainColor, `${BGVariantColor}Hover`)(theme),
    foregroundActive: getColor(BGMainColor, `${BGVariantColor}Active`)(theme),
    background: "transparent",
    backgroundHover: "transparent",
    backgroundFocus: "transparent",
    backgroundActive: "transparent",
    borderColor: "transparent",
    borderColorHover: "transparent",
    borderColorFocus: "transparent",
    borderColorActive: "transparent",
    boxShadow: null,
    boxShadowHover: null,
    boxShadowFocus: null,
    boxShadowActive: null,
  };

  const styles = outlined
    ? outlinedStyles
    : plain
    ? plainStyles
    : defaultStyles;

  return {
    ...styles,
    background: transparent ? "transparent" : styles.background,
    backgroundHover: transparent ? "transparent" : styles.backgroundHover,
    backgroundFocus: transparent ? "transparent" : styles.backgroundFocus,
    backgroundActive: transparent ? "transparent" : styles.backgroundActive,
  };
};

export default getButtonStyles;
