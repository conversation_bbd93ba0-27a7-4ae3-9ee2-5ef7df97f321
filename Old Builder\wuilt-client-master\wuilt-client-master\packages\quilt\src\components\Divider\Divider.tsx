import React from "react";
import { Box, BoxProps } from "../Box";

export interface DividerProps extends BoxProps {
  vertical?: boolean;
  horizontal?: boolean;
}

const Divider: React.FC<DividerProps> = ({ vertical = false, ...props }) => {
  const width = vertical ? "1px" : "100%";
  const height = vertical ? "100%" : "1px";

  return (
    <Box
      width={width}
      height={height}
      bg={props.color || "disabled"}
      {...props}
    />
  );
};

export { Divider };
