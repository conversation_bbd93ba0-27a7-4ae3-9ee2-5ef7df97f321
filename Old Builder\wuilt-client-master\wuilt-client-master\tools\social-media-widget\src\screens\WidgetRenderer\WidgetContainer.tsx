import React from "react";
import {
  TWidgetSettings,
  WidgetOrientationEnum,
  WidgetPositionEnum,
} from "../../shared/types";
import styled, { css } from "styled-components";
import { LocaleEnum } from "../../main";

// const translations = {
//   en: {
//     poweredBy: "Powered by",
//     wuilt: "Wuilt",
//   },
//   ar: {
//     poweredBy: "مقدّمة من منصة",
//     wuilt: "ويلت",
//   },
// };

const LINK = "https://app.wuilt.com/account/signup?fpr=chat-widget";

interface WidgetContainerProps {
  children: React.ReactNode;
  settings: TWidgetSettings;
  locale: LocaleEnum;
}

const WidgetContainer: React.FC<WidgetContainerProps> = ({
  children,
  settings,
  // locale,
}) => {
  return (
    <StyledWidgetContainer settings={settings}>
      <HStack gap="4px" direction="ltr">
        <Text>Powered by </Text>
        <Text>
          <a
            href={LINK}
            target="_blank"
            style={{
              textDecoration: "none",
              color: "#15B79E",
              fontWeight: "600",
            }}
          >
            Wuilt
          </a>
        </Text>
      </HStack>
      {children}
    </StyledWidgetContainer>
  );
};

export default WidgetContainer;

/**
 * Styles
 */

const WidgetContainerStyles = ({ settings }: { settings: TWidgetSettings }) => {
  const isTransparent = !!settings?.appearance?.style?.transparent;
  const isLeft =
    settings?.appearance?.display?.position === WidgetPositionEnum.Left;
  const isVertical =
    settings?.appearance?.display?.orientation ===
    WidgetOrientationEnum.Vertical;
  return css`
    display: flex;
    align-items: ${isVertical ? "flex-start" : "flex-end"};
    flex-direction: ${isVertical ? "column-reverse" : "row"};
    position: fixed;
    z-index: 9999;
    bottom: ${settings.appearance.display.shift.vertical};
    left: ${isLeft && settings.appearance.display.shift.horizontal};
    right: ${!isLeft && settings.appearance.display.shift.horizontal};
    direction: ${isLeft ? "ltr" : "rtl"};
    opacity: ${isTransparent ? "0.6" : "1"};
    gap: 8px;
  `;
};

const HStack = styled.div<{ gap?: string; direction?: "ltr" | "rtl" }>`
  display: flex;
  align-items: center;
  gap: ${({ gap }) => gap || "0px"};
  direction: ${({ direction }) => direction};
`;

const Text = styled.div<{ fontWeight?: string }>`
  font-size: 12px;
  color: #1d2939;
  font-weight: ${({ fontWeight }) => fontWeight || "normal"};
`;

const StyledWidgetContainer = styled.div<{ settings: TWidgetSettings }>`
  ${WidgetContainerStyles}
`;
