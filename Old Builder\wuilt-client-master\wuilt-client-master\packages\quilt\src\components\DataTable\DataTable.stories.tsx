import React from "react";
import { MemoryRouter } from "react-router-dom";
import { DataTable } from "./DataTable";
import { Stack } from "../Stack";
import { DropMenu } from "../DropMenu/DropMenu";
import { Thumbnail } from "../Thumbnail";

export default {
  title: "Components/DataTable",
  component: DataTable,
  decorators: [
    (Story) => (
      <MemoryRouter>
        <Story />
      </MemoryRouter>
    ),
  ],
};

const productContent = (
  <Stack direction="row" align="center">
    <Thumbnail src="" size="small" />
    <span>Product Name</span>
  </Stack>
);
export const Playground = () => {
  return (
    <DataTable
      header={{
        cells: [
          { content: "ID" },
          { content: "Product" },
          { content: "LANGUAGES" },
          { content: "Collections" },
          { content: "PRICE" },
          { content: "STATUS" },
          { content: "" },
        ],
      }}
      rows={[
        {
          cells: [
            { content: "ID" },
            { content: productContent },
            { content: "language control", linkTo: "Un-Link" },
            { content: "EGP 375.00" },
            { content: "Men Shoes" },
            { content: "Available" },
            { content: <DropMenu />, linkTo: "Un-Link" },
          ],
          linkTo: "xx",
          // bgColor: "#ccc",
        },
      ]}
    />
  );
};
