import { LoadingScreen } from "../components/loading-screen";
import { useAuth } from "./auth-provider";

interface StandaloneSecuredProps {
  children: any;
  authRoute: string;
}
export const StandaloneSecure = ({
  children,
  authRoute,
}: StandaloneSecuredProps) => {
  const { loading, user } = useAuth();
  if (!user && !loading) {
    window.location.href = authRoute;
  }
  if (loading) return <LoadingScreen />;
  if (!user) return null;
  return children;
};
