import React from "react";
import { TIconType } from "../types";

export function MessageChatSquare({
  size = 40,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.33317 12.5001L5.77046 15.0948C5.41299 15.4568 5.23426 15.6377 5.08063 15.6505C4.94735 15.6616 4.81685 15.608 4.7298 15.5064C4.62947 15.3894 4.62947 15.1351 4.62947 14.6264V13.3264C4.62947 12.87 4.25573 12.5398 3.80417 12.4737V12.4737C2.71129 12.3136 1.85298 11.4553 1.6929 10.3624C1.6665 10.1822 1.6665 9.96718 1.6665 9.53712V5.66675C1.6665 4.26662 1.6665 3.56655 1.93899 3.03177C2.17867 2.56137 2.56112 2.17892 3.03153 1.93923C3.56631 1.66675 4.26637 1.66675 5.6665 1.66675H11.8332C13.2333 1.66675 13.9334 1.66675 14.4681 1.93923C14.9386 2.17892 15.321 2.56137 15.5607 3.03177C15.8332 3.56655 15.8332 4.26662 15.8332 5.66675V9.16675M15.8332 18.3334L14.0195 17.0725C13.7645 16.8952 13.6371 16.8066 13.4983 16.7438C13.3752 16.688 13.2457 16.6474 13.1128 16.6229C12.963 16.5953 12.8078 16.5953 12.4973 16.5953H10.9998C10.0664 16.5953 9.59971 16.5953 9.24319 16.4137C8.92958 16.2539 8.67462 15.9989 8.51483 15.6853C8.33317 15.3288 8.33317 14.8621 8.33317 13.9287V11.8334C8.33317 10.9 8.33317 10.4333 8.51483 10.0768C8.67462 9.76316 8.92958 9.50819 9.24319 9.3484C9.59971 9.16675 10.0664 9.16675 10.9998 9.16675H15.6665C16.5999 9.16675 17.0666 9.16675 17.4232 9.3484C17.7368 9.50819 17.9917 9.76316 18.1515 10.0768C18.3332 10.4333 18.3332 10.9 18.3332 11.8334V14.0953C18.3332 14.8719 18.3332 15.2602 18.2063 15.5665C18.0371 15.9748 17.7127 16.2993 17.3043 16.4685C16.998 16.5953 16.6097 16.5953 15.8332 16.5953V18.3334Z"
        stroke={color || "currentColor"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
