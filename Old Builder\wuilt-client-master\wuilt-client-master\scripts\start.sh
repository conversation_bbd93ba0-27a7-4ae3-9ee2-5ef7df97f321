# !/bin/bash
source ./scripts/select.sh

echo "Select the application you need to run:"
echo

APPS=("stores-admin" "app-shell" "account" "billing" "quilt" "social-media-widget")
select_option "${APPS[@]}"
app=$?

case "$app" in
	4)
    echo "Starting Quilt"
		yarn nx run quilt:storybook
    ;;
	5)
    echo "Starting Social Media Widget"
		yarn nx run social-media-widget:serve
    ;;
  *)
    echo "Select the environment"
		echo
		ENVS=("canary" "staging" "production" "shell")
		select_option "${ENVS[@]}"
		env=$?
		echo
		yarn nx run ${APPS[$app]}:serve:${ENVS[$env]}
    ;;

esac
