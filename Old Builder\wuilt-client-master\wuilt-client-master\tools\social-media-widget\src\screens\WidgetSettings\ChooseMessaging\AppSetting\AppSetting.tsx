import React from "react";
import { Box, InputField, Stack, Text } from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import {
  AppsEnum,
  TAppSettings,
  TWhatsAppSettings,
  TWidgetBackground,
} from "../../../../shared/types";
import AppColorSetting from "../AppColorSetting";
import WhatsAppSettings from "../WhatsAppSettings";
import TitleAndSubtitle from "../WhatsAppSettings/TitleAndSubtitle";
interface AppSettingProps {
  handleChangeSettings: (
    appName: AppsEnum,
    objKey: string,
    newValue: string | TWidgetBackground | TWhatsAppSettings
  ) => void;
  updateAgents: (
    apps: TAppSettings[],
    agentIndex: number,
    key: string,
    value: string
  ) => void;
  app: TAppSettings;
}

function AppSetting({
  app,
  handleChangeSettings,
  updateAgents,
}: AppSettingProps) {
  const appName = app.name;
  return (
    <Box padding="10px 16px" width="347px">
      <Text fontWeight="semiBold" color="#1D2939">
        <FormattedMessage defaultMessage="Settings" id="D3idYv" />
      </Text>
      <Box my="10px">
        <AppColorSetting
          appName={appName}
          color={app.background.color}
          handleChangeColor={(color) => {
            handleChangeSettings(appName, "background", {
              ...app.background,
              color: color,
            });
          }}
        />
      </Box>

      <Stack my="10px" direction="row">
        <Stack width="100%" spacing="tight">
          <Text fontWeight="semiBold" color="#1D2939">
            <FormattedMessage defaultMessage="On hover text" id="mz37/9" />
          </Text>
          <InputField
            height="40px"
            borderRadius="8px"
            value={app.onHoverText}
            onBlur={(e: any) => {
              handleChangeSettings(appName, "onHoverText", e.target.value);
            }}
          />
        </Stack>
      </Stack>
      {appName === AppsEnum.Whatsapp && (
        <TitleAndSubtitle
          app={app}
          handleChangeSettings={handleChangeSettings}
          appName={appName}
        />
      )}
      {appName === AppsEnum.Whatsapp && (
        <WhatsAppSettings
          divider
          agent={app?.whatsAppSettings?.agents[0]!}
          agentIndex={0}
          updateAgents={updateAgents}
        />
      )}
    </Box>
  );
}

export default AppSetting;
