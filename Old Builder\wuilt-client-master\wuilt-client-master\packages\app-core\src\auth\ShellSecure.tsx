import { Navigate } from "react-router-dom";
import { LoadingScreen } from "../components/loading-screen";
import { useAuth } from "./auth-provider";
import { getSearchQueries, setSearchQueries } from "./auth-search-query";

interface ShellSecuredProps {
  children: any;
}
export const ShellSecure = ({ children }: ShellSecuredProps) => {
  const { loading, user } = useAuth();

  const { loginAction, fpr } = getSearchQueries();
  setSearchQueries(fpr, window.location.pathname);

  if (!user && !loading && loginAction === "signup") {
    return <Navigate to="/account/signup" replace />;
  }
  if (!user && !loading) {
    return <Navigate to="/account/login" replace />;
  }

  if (loading) return <LoadingScreen />;
  if (!user) return null;
  return children;
};
