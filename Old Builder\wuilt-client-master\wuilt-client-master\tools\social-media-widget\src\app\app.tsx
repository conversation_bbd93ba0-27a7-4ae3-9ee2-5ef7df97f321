import { Quilt<PERSON>rovider } from "@wuilt/quilt";
import { IntlProvider } from "react-intl";
import { WidgetSettings } from "../screens/WidgetSettings";
import { StrictMode, useEffect } from "react";
import * as ReactDOM from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { Widget } from "./widget";
import { WidgetProvider, useWidget } from "../context/widget-provider";
import enMessages from "../translations/en.json";
import arMessages from "../translations/ar.json";
import trMessages from "../translations/tr.json";
import frMessages from "../translations/fr.json";
import { init } from "@amplitude/analytics-browser";
import { LocaleEnum } from "../main";

const MessagesMap = {
  en: enMessages,
  ar: arMessages,
  tr: trMessages,
  fr: frMessages,
};

const AMPLITUDE_API_KEY = "2a2cd80b0f15e8a2d51fac4b815d0d92";

function App({ locale }: { locale: LocaleEnum }) {
  const { settings } = useWidget();

  useEffect(() => {
    init(AMPLITUDE_API_KEY);
  }, []);

  return (
    <>
      <WidgetSettings locale={locale} />
      <Widget alwaysShow settings={settings} locale={locale} />
    </>
  );
}

export function startWidgetApp(locale: LocaleEnum) {
  let div = document.getElementById("widget-settings-root");
  if (!div) {
    div = document.createElement("div");
    div.id = "widget-settings-root";
    document.body.append(div);
  }
  const root = ReactDOM.createRoot(div);
  const langCode: LocaleEnum = locale.substring(0, 2) as LocaleEnum;
  root.render(
    <StrictMode>
      <BrowserRouter>
        <QuiltProvider dir={langCode === "ar" ? "rtl" : "ltr"}>
          <IntlProvider
            locale={locale}
            messages={MessagesMap[langCode] || MessagesMap.en}
          >
            <WidgetProvider>
              <App locale={langCode} />
            </WidgetProvider>
          </IntlProvider>
        </QuiltProvider>
      </BrowserRouter>
    </StrictMode>
  );
}
