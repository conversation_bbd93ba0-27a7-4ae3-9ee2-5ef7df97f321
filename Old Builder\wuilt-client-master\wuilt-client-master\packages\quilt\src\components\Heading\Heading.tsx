import React from "react";
import styled, { css } from "styled-components";
import { layout, LayoutProps } from "styled-system";
import {
  Color,
  ColorProps,
  Typography,
  TypographyProps,
  TextAlignmentCss,
  TextAlignmentProps,
  Space,
  SpaceProps,
  TextAlignmentFn,
} from "../../themes/property-overriding";
import { Global } from "../../common/types";

export interface HeadingStyleProps
  extends Global,
    TextAlignmentProps,
    ColorProps,
    SpaceProps,
    TypographyProps,
    LayoutProps {
  style?: React.CSSProperties;
  wordBreak?: React.CSSProperties["wordBreak"];
  lineHeight?: React.CSSProperties["lineHeight"];
  whiteSpace?: React.CSSProperties["whiteSpace"];
  letterSpacing?: React.CSSProperties["letterSpacing"];
}

export interface HeadingProps extends HeadingStyleProps {
  children: React.ReactNode;
  className?: string;
  variant?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
  onHover?: HeadingStyleProps;
}

export const Heading: React.FC<HeadingProps> = ({
  variant = "h2",
  fontSize = "md",
  fontWeight = "bold",
  textColor = "secondary",
  align = "left",
  ...restProps
}) => (
  <StyledHeader
    Component={variant}
    fontSize={fontSize}
    fontWeight={fontWeight}
    textColor={textColor}
    align={align}
    {...restProps}
  />
);

/**
 *
 *
 * Styles
 *
 *
 */

const onHoverStyles = ({ onHover, theme }) => {
  const cssProps = { ...onHover, theme };
  return css`
    ${Color(cssProps)};
    ${TextAlignmentFn(cssProps)}
    ${Typography(cssProps)}
		${Space(cssProps)}
		${layout(cssProps)}
  `;
};

const StyledHeader = styled(
  ({ children, className, Component, style, dataTest }) => (
    <Component className={className} style={style} data-test={dataTest}>
      {children}
    </Component>
  )
)`
  padding: 0;
  margin: 0;
  ${Color};
  ${TextAlignmentCss}
  ${Typography};
  ${Space}
  ${layout}

	&:hover, &:active {
    ${onHoverStyles}
  }
  ${({ wordBreak }) => wordBreak && `word-break:${wordBreak}`}
  ${({ lineHeight }) => lineHeight && `line-height:${lineHeight}`}
  ${({ whiteSpace }) => whiteSpace && `white-space:${whiteSpace}`}
  ${({ letterSpacing }) => letterSpacing && `letter-spacing:${letterSpacing}`}
`;
