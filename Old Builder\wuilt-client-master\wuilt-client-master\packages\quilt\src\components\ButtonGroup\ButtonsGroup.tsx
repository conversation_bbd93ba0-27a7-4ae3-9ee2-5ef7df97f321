import React from "react";
import styled, { css, DefaultTheme } from "styled-components";

export interface ButtonsGroupProps {
  children: React.ReactNode[] | React.ReactNode;
  className?: string;
}

const ButtonsGroup: React.FC<ButtonsGroupProps> = ({ className, children }) => {
  const mappedChildren = Array.isArray(children) ? children : [children];

  const filteredChildren = mappedChildren.filter(Boolean);

  const buttonsCount: number = filteredChildren?.length;

  return (
    <StyledButtonsWrapper className={className}>
      {filteredChildren?.map((child: React.ReactNode, index: number) => (
        <StyledButtons key={index} index={index} buttonsCount={buttonsCount}>
          {child}
        </StyledButtons>
      ))}
    </StyledButtonsWrapper>
  );
};

ButtonsGroup.displayName = "ButtonsGroup";

export { ButtonsGroup };

// Styles

const StyledButtonsWrapper = styled.div`
  display: flex;
`;

const firstButtonStyles = ({ theme }: { theme: DefaultTheme }) => css`
  ${theme.rtl ? "border-left: none" : "border-right: none"};
  ${theme.rtl
    ? css`
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      `
    : css`
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      `};
`;

const lastButtonStyles = ({ theme }: { theme: DefaultTheme }) => css`
  ${theme.rtl ? "border-right: none" : "border-left: none"};
  ${theme.rtl
    ? css`
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      `
    : css`
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      `};
`;

const otherButtonsStyles = css`
  border-radius: 0px;
`;

const oneButtonsStyles = css`
  border-radius: 6px;
`;

const StyledButtons = styled.div<{
  index: number;
  buttonsCount: number;
}>`
  button {
    border-radius: 6px;
    box-shadow: none;
    ${({ index, buttonsCount }) =>
      buttonsCount === 1
        ? oneButtonsStyles
        : index === 0
        ? firstButtonStyles
        : index === buttonsCount - 1
        ? lastButtonStyles
        : otherButtonsStyles}
    &:hover,
    &:active,
    &:focus {
      box-shadow: none;
      border-color: #eaecf0;
    }
    &:active {
      transform: none;
    }
    &:active {
      svg {
        transform: scale(0.85);
      }
    }
  }
`;
