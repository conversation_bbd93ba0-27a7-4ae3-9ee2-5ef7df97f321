import { Stack } from "@wuilt/quilt";
import React from "react";
import { TWidgetAppearance, TWidgetSettings } from "../../../../shared/types";
import Position from "./Position";
import Shift from "./Shift";
interface PositionAndShiftProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function PositionAndShift({ appearance, update }: PositionAndShiftProps) {
  return (
    <Stack
      boxShadow="0px 1px 2px 0px #1018280D"
      width="100%"
      height="100%"
      border="1px solid #EAECF0"
      borderRadius="10px"
      padding="16px"
      direction="row"
      align="center"
      largeMobile={{ direction: "column" }}
    >
      <Position appearance={appearance} update={update} />
      <Shift appearance={appearance} update={update} />
    </Stack>
  );
}

export default PositionAndShift;
