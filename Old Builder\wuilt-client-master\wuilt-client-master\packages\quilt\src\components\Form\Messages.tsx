import React from "react";

import styled, { css } from "styled-components";

import { FieldId } from "./FieldIdContext";

// color: ${(props) => {
//   if (props.error) {
//     return R400;
//   }
//   if (props.valid) {
//     return G400;
//   }
//   return N200;
// }};
// margin-top: ${multiply(gridSize, 0.5)}px;
const Message = styled.div<{
  error?: boolean;
  valid?: boolean;
  position: "left" | "right";
}>`
  font-weight: ${({ theme }) => theme.base.fontWeight.normal};
  font-size: ${({ theme }) => theme.base.fontSize.sm};
  margin-top: ${({ theme }) => theme.base.space.xxs};
  // position: absolute;
  // width: 100%;

  color: ${({ theme, error, valid }) =>
    error
      ? theme.palette.red.normal
      : valid
      ? theme.palette.green.normal
      : theme.palette.ink.light};

  ${({ theme, position }) =>
    theme.rtl
      ? css`
          text-align: ${position === "left" ? "right" : "left"};
        `
      : css`
          text-align: ${position};
        `}
`;

const IconWrapper = styled.span`
  display: flex;
`;

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  position?: "left" | "right";
}

export const HelperMessage: React.FC<Props> = ({
  children,
  className,
  position = "left",
  ...restProps
}: Props) => (
  <FieldId.Consumer>
    {(fieldId) => (
      <Message
        className={className}
        id={fieldId ? `${fieldId}-helper` : undefined}
        position={position}
        {...restProps}
      >
        {children}
      </Message>
    )}
  </FieldId.Consumer>
);

export const ErrorMessage: React.FC<Props> = ({
  children,
  className,
  position = "left",
  ...restProps
}: Props) => (
  <FieldId.Consumer>
    {(fieldId) => (
      <Message
        data-test="form-error-message"
        className={className}
        error
        id={fieldId ? `${fieldId}-error` : undefined}
        position={position}
        {...restProps}
      >
        <IconWrapper>
          {/* <ErrorIcon size="small" label="error" /> */}
        </IconWrapper>
        {children}
      </Message>
    )}
  </FieldId.Consumer>
);

export const ValidMessage: React.FC<Props> = ({
  children,
  className,
  position = "left",
  ...restProps
}: Props) => (
  <FieldId.Consumer>
    {(fieldId) => (
      <Message
        className={className}
        valid
        id={fieldId ? `${fieldId}-valid` : undefined}
        position={position}
        {...restProps}
      >
        <IconWrapper>
          {/* <SuccessIcon size="small" label="success" /> */}
        </IconWrapper>
        {children}
      </Message>
    )}
  </FieldId.Consumer>
);
