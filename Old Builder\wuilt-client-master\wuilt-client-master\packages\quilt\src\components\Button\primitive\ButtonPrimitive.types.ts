import React from "react";
import * as Common from "../../../common/types";

type InexactString = string | null | undefined;
type FunctionReturningString = () => string;

export interface ButtonCommonProps extends Common.Global, Common.SpaceAfter {
  readonly asComponent?: Common.Component;
  readonly ariaControls?: string;
  readonly ariaExpanded?: boolean;
  readonly children?: React.ReactNode;
  readonly squared?: boolean;
  readonly rounded?: boolean;
  readonly stopOpacity?: boolean;
  readonly disabled?: boolean;
  readonly external?: boolean;
  readonly fullWidth?: boolean;
  readonly href?: string;
  readonly prefixIcon?: React.ReactNode;
  readonly suffixIcon?: React.ReactNode;
  readonly loading?: boolean;
  readonly loadingtext?: React.ReactNode;
  readonly onClick?: Common.Event<React.SyntheticEvent<HTMLButtonElement>>;
  readonly role?: string;
  readonly size?: Common.Size;
  readonly submit?: boolean;
  readonly contentAlign?: "left" | "right" | "center";
  readonly contentWidth?: string | null;
  readonly title?: string | FunctionReturningString;
  readonly tabIndex?: string;
  readonly width?: string;
  readonly height?: string;
  readonly compact?: boolean;
  readonly onlyIcon?: boolean;
  readonly underlined?: boolean;
  readonly to?: string | object;
  readonly cursor?: React.CSSProperties["cursor"];
  readonly download?: string;
  readonly padding?: string;
  readonly borderRadius?: string;
}
/*
  Icon properties used on other getter functions.
 */
export type IconForeground = {
  readonly foreground?: InexactString;
  readonly foregroundHover?: InexactString;
  readonly foregroundActive?: InexactString;
};

export type IconProps = IconForeground & {
  readonly width?: InexactString;
  readonly height?: InexactString;
  readonly leftMargin?: InexactString;
  readonly rightMargin?: InexactString;
};

/*
  Primitive - style props used only on ButtonPrimitive itself or it's common functions.
 */
export type HeightProps = {
  readonly height?: string;
  readonly fontSize?: InexactString;
};

export type Foreground = {
  readonly foreground?: InexactString;
  readonly foregroundHover?: InexactString;
  readonly foregroundActive?: InexactString;
  readonly foregroundFocus?: InexactString;
};

export type Background = {
  readonly background?: InexactString;
  readonly backgroundHover?: InexactString;
  readonly backgroundActive?: InexactString;
  readonly backgroundFocus?: InexactString;
};

export type BorderColor = {
  readonly borderColor?: InexactString;
  readonly borderColorHover?: InexactString;
  readonly borderColorActive?: InexactString;
  readonly borderColorFocus?: InexactString;
};

export type BoxShadow = {
  readonly boxShadow?: InexactString;
  readonly boxShadowHover?: InexactString;
  readonly boxShadowActive?: InexactString;
  readonly boxShadowFocus?: InexactString;
};

export interface PrimitiveTypes
  extends HeightProps,
    Foreground,
    Background,
    BorderColor,
    BoxShadow {
  readonly fontWeight?: InexactString;
  readonly icons?: IconProps;
}

export type ButtonPrimitiveProps = ButtonCommonProps & PrimitiveTypes;
