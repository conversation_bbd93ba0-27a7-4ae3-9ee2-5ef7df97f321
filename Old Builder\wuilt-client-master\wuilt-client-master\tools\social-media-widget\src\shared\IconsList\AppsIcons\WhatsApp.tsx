import { TIconType } from "../types";

export function WhatsApp({ size = 40, width, height, color }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.86841 31.6359L9.5833 25.4087C8.52308 23.5808 7.9669 21.5108 7.97269 19.3947C7.97269 12.7638 13.3955 7.37256 20.0522 7.37256C23.285 7.37256 26.3208 8.62378 28.5977 10.8956C30.8804 13.1674 32.1376 16.1888 32.1318 19.4005C32.1318 26.0314 26.709 31.4226 20.0464 31.4226H20.0406C18.0187 31.4226 16.0315 30.9152 14.2645 29.958L7.86841 31.6359ZM14.5715 27.7843L14.9365 28.0034C16.4776 28.9144 18.2446 29.393 20.0464 29.3987H20.0522C25.5851 29.3987 30.0924 24.9185 30.0924 19.4062C30.0924 16.7366 29.0496 14.2284 27.1551 12.3371C25.2606 10.4458 22.7346 9.40796 20.0522 9.40796C14.5194 9.4022 10.012 13.8824 10.012 19.3947C10.012 21.2802 10.5392 23.1195 11.5473 24.711L11.7848 25.0915L10.771 28.776L14.5715 27.7843Z"
        fill={color || "currentColor"}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.0337 14.3665C16.8077 13.8649 16.5702 13.8533 16.3558 13.8476C16.182 13.8418 15.9792 13.8418 15.7765 13.8418C15.5737 13.8418 15.2493 13.9168 14.9712 14.2166C14.6931 14.5164 13.9167 15.2429 13.9167 16.7248C13.9167 18.2009 15.0001 19.6309 15.1508 19.8327C15.3014 20.0345 17.2422 23.1654 20.307 24.3705C22.8562 25.3738 23.3776 25.172 23.928 25.1201C24.4784 25.0682 25.7124 24.3936 25.9673 23.6902C26.2164 22.9867 26.2164 22.387 26.1411 22.2602C26.0658 22.1333 25.863 22.0584 25.5618 21.9085C25.2605 21.7585 23.7774 21.032 23.4993 20.9282C23.2212 20.8302 23.0184 20.7783 22.8214 21.0781C22.6187 21.378 22.0393 22.0526 21.8655 22.2544C21.6917 22.4562 21.5121 22.4793 21.2108 22.3294C20.9096 22.1795 19.9362 21.8623 18.7833 20.836C17.8853 20.0403 17.277 19.0543 17.1032 18.7544C16.9294 18.4546 17.0858 18.2932 17.2364 18.1432C17.3697 18.0106 17.5377 17.7915 17.6883 17.6185C17.839 17.4456 17.8911 17.3187 17.9896 17.1169C18.0881 16.9151 18.0418 16.7421 17.9664 16.5922C17.8911 16.448 17.3002 14.9604 17.0337 14.3665Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}

<svg
  xmlns="http://www.w3.org/2000/svg"
  width="38"
  height="38"
  viewBox="0 0 38 38"
  fill="none"
>
  <path
    d="M0.269775 37.9338L2.89212 28.4159C1.2692 25.6326 0.41648 22.4661 0.41648 19.2357C0.425649 9.10639 8.7053 0.866089 18.8829 0.866089C23.8251 0.866089 28.4554 2.78244 31.9397 6.25012C35.4239 9.7178 37.3402 14.3353 37.3402 19.2448C37.3402 29.3741 29.0514 37.6144 18.8829 37.6144H18.8738C15.7838 37.6144 12.7488 36.8387 10.0531 35.3786L0.269775 37.9338Z"
    fill="#20B038"
  />
</svg>;

<svg
  xmlns="http://www.w3.org/2000/svg"
  width="39"
  height="39"
  viewBox="0 0 39 39"
  fill="none"
>
  <path
    d="M0.600098 38.6L3.31414 28.7444C1.6362 25.8517 0.755971 22.5756 0.765141 19.2266C0.765141 8.73227 9.34737 0.199951 19.8826 0.199951C24.9989 0.199951 29.8035 2.18018 33.407 5.77562C37.0196 9.37105 39.0092 14.1528 39.0001 19.2357C39.0001 29.73 30.4178 38.2623 19.8734 38.2623H19.8643C16.6643 38.2623 13.5193 37.4593 10.7227 35.9444L0.600098 38.6ZM11.2087 32.5041L11.7863 32.8509C14.2253 34.2927 17.0219 35.0501 19.8734 35.0593H19.8826C28.639 35.0593 35.7726 27.9688 35.7726 19.2448C35.7726 15.0197 34.1221 11.0501 31.1239 8.05699C28.1256 5.06383 24.1279 3.42124 19.8826 3.42124C11.1262 3.41212 3.99265 10.5026 3.99265 19.2266C3.99265 22.2106 4.82703 25.1216 6.42244 27.6403L6.79837 28.2425L5.19379 34.0737L11.2087 32.5041Z"
    fill="white"
  />
  <path
    d="M1.26978 37.9338L3.89212 28.4159C2.2692 25.6326 1.41648 22.4661 1.41648 19.2357C1.42565 9.10639 9.7053 0.866089 19.8829 0.866089C24.8251 0.866089 29.4554 2.78244 32.9397 6.25012C36.4239 9.7178 38.3402 14.3353 38.3402 19.2448C38.3402 29.3741 30.0514 37.6144 19.8829 37.6144H19.8738C16.7838 37.6144 13.7488 36.8387 11.0531 35.3786L1.26978 37.9338Z"
    fill="#20B038"
  />
  <path
    d="M0.600098 38.6L3.31414 28.7444C1.6362 25.8517 0.755971 22.5756 0.765141 19.2266C0.765141 8.73227 9.34737 0.199951 19.8826 0.199951C24.9989 0.199951 29.8035 2.18018 33.407 5.77562C37.0196 9.37105 39.0092 14.1528 39.0001 19.2357C39.0001 29.73 30.4178 38.2623 19.8734 38.2623H19.8643C16.6643 38.2623 13.5193 37.4593 10.7227 35.9444L0.600098 38.6ZM11.2087 32.5041L11.7863 32.8509C14.2253 34.2927 17.0219 35.0501 19.8734 35.0593H19.8826C28.639 35.0593 35.7726 27.9688 35.7726 19.2448C35.7726 15.0197 34.1221 11.0501 31.1239 8.05699C28.1256 5.06383 24.1279 3.42124 19.8826 3.42124C11.1262 3.41212 3.99265 10.5026 3.99265 19.2266C3.99265 22.2106 4.82703 25.1216 6.42244 27.6403L6.79837 28.2425L5.19379 34.0737L11.2087 32.5041Z"
    fill="#F9F9F9"
  />
  <path
    fillRule="evenodd"
    clipRule="evenodd"
    d="M15.1058 11.2693C14.7482 10.4753 14.3723 10.4571 14.033 10.448C13.7579 10.4388 13.437 10.4388 13.1161 10.4388C12.7952 10.4388 12.2817 10.5575 11.8416 11.032C11.4015 11.5065 10.1729 12.6563 10.1729 15.0016C10.1729 17.3377 11.8875 19.6008 12.1259 19.9202C12.3643 20.2396 15.4359 25.1947 20.2863 27.102C24.3207 28.6898 25.1459 28.3704 26.017 28.2883C26.888 28.2061 28.841 27.1385 29.2445 26.0252C29.6387 24.9118 29.6387 23.9628 29.5195 23.762C29.4003 23.5613 29.0794 23.4426 28.6026 23.2054C28.1258 22.9681 25.7786 21.8183 25.3385 21.6541C24.8983 21.4989 24.5774 21.4168 24.2657 21.8913C23.9448 22.3658 23.0279 23.4335 22.7528 23.7529C22.4777 24.0723 22.1935 24.1088 21.7167 23.8715C21.2399 23.6343 19.6995 23.1324 17.8749 21.508C16.4536 20.2487 15.4909 18.6883 15.2158 18.2137C14.9408 17.7392 15.1883 17.4837 15.4267 17.2464C15.6376 17.0366 15.9035 16.6898 16.1419 16.416C16.3803 16.1423 16.4628 15.9415 16.6187 15.6221C16.7746 15.3027 16.7012 15.029 16.582 14.7917C16.4628 14.5636 15.5276 12.2092 15.1058 11.2693Z"
    fill="white"
  />
</svg>;
