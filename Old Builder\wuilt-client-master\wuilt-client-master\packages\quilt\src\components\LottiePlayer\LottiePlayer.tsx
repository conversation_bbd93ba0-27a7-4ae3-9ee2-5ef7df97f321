import React from "react";
import <PERSON><PERSON>, {
  LottieProps,
} from "react-lottie-player/dist/LottiePlayerLight";

interface LottiePlayerProps extends Omit<LottieProps, "animationData"> {
  lottieJson: object;
}

const LottiePlayer = (props: LottiePlayerProps) => {
  const { lottie<PERSON>son, play, loop } = props;

  return (
    <Lottie
      {...props}
      loop={loop || true}
      animationData={lottieJson}
      play={play || true}
    />
  );
};

export { LottiePlayer };
