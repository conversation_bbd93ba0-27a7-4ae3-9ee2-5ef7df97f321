import React from "react";
import { But<PERSON> } from "../Button";
import { PageHeader } from "./PageHeader";
import { AddIcon, SaveIcon } from "../icons";
import { Breadcrumbs } from "../Breadcrumbs";

export default {
  title: "Components/PageHeader",
  component: PageHeader,
};

export const Playground = () => {
  return (
    <PageHeader
      title="Shipping"
      description="Define your shipping regions and how rates are calculated."
      primaryAction={<Button prefixIcon={<AddIcon />}>Add Zone</Button>}
      secondaryActions={[
        <Button
          outlined
          transparent
          prefixIcon={<SaveIcon size="lg" color="primary" />}
        >
          Export
        </Button>,
      ]}
      breadcrumbs={
        <Breadcrumbs
          breadcrumbs={[{ content: "All Products", url: "#" }]}
          urlProp="href"
          linkComponent="a"
          type="back-button"
        />
      }
    />
  );
};
