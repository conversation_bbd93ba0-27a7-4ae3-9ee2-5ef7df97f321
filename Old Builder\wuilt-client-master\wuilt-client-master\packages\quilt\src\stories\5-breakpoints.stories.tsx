import React from "react";
import { base } from "../themes/original";
import { DataTable } from "../components/DataTable";
import { Box } from "../components/Box";

export const Breakpoints = () => {
  return (
    <Box margin="0 auto" maxWidth="1000px">
      <DataTable
        header={{ cells: [{ content: "Breakpoints" }, { content: "Value" }] }}
        rows={Object.keys(base.breakpoints).map((f) => ({
          cells: [{ content: f }, { content: base.breakpoints[f] }],
        }))}
      />
    </Box>
  );
};

Breakpoints.title = "Intro/Guide";

Breakpoints.story = {
  name: "Breakpoints",
};

export default Breakpoints;
