import React from "react";
import styled from "styled-components";
import { StyledBlackLogo } from "./WuiltBlackLogo";
import { StyledColoredLogo } from "./WuiltColoredLogo";

const StyledLogoContainer = styled.div<{
  size: number;
  isLoading?: boolean;
}>`
  position: relative;
  width: ${(props) => props.size}px;
  height: ${(props) => props.size}px;

  ${StyledColoredLogo} {
    position: absolute;
    top: 0;
    left: 0;
  }

  ${StyledBlackLogo} {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
  }
`;

export interface WuiltLogoProps {
  size: number;
  isLoading?: boolean;
}

const WuiltLogo: React.FC<WuiltLogoProps> = ({ isLoading, size }) => (
  <StyledLogoContainer size={size} isLoading={isLoading}>
    <StyledColoredLogo size={size} />
    <StyledBlackLogo size={size} />
  </StyledLogoContainer>
);

export { WuiltLogo };
