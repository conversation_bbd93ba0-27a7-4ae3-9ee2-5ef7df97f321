import {
  system,
  compose,
  border as SystemBorder,
  BorderProps as SystemBorderProps,
} from "styled-system";
import { ColorsType, getColorValue, modifyColorProp } from "./colors";

const borderColor = system({
  borderColor: {
    property: "borderColor",
    transform: getColorValue,
  },
  borderTopColor: {
    property: "borderTopColor",
    transform: getColorValue,
  },
  borderLeftColor: {
    property: "borderLeftColor",
    transform: getColorValue,
  },
  borderRightColor: {
    property: "borderRightColor",
    transform: getColorValue,
  },
  borderBottomColor: {
    property: "borderBottomColor",
    transform: getColorValue,
  },
});

export type BorderColorProps = {
  borderColor?: ColorsType;
  borderTopColor?: ColorsType;
  borderLeftColor?: ColorsType;
  borderRightColor?: ColorsType;
  borderBottomColor?: ColorsType;
};

export type BorderProps = BorderColorProps & SystemBorderProps;
export const Border = (props) => {
  const modifiedColorProps = {
    ...props,
    ...modifyColorProp("borderColor", props.borderColor),
    ...modifyColorProp("borderTopColor", props.borderTopColor),
    ...modifyColorProp("borderLeftColor", props.borderLeftColor),
    ...modifyColorProp("borderRightColor", props.borderRightColor),
    ...modifyColorProp("borderBottomColor", props.borderBottomColor),
  };
  return compose(SystemBorder, borderColor)(modifiedColorProps);
};
