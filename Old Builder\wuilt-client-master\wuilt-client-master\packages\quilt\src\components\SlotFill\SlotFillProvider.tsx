import { FunctionComponent, ReactNode, useMemo, useState } from "react";
import SlotFillContext, { SlotFillContextType } from "./SlotFillContext";

interface SlotFillProviderProps {
  children: React.ReactNode;
}

const SlotFillProvider = ({ children }: SlotFillProviderProps) => {
  const [slots, setSlots] = useState<
    Record<
      string,
      {
        ref: React.RefObject<HTMLDivElement>;
        Wrapper?: FunctionComponent<{ children: ReactNode }>;
      }
    >
  >({});

  const contextValue: SlotFillContextType = useMemo(() => {
    const getSlot = (name: string) => {
      return slots[name] || { ref: { current: null }, Wrapper: undefined };
    };
    const registerSlot = (
      name: string,
      ref: React.RefObject<HTMLDivElement>,
      Wrapper?: FunctionComponent<{ children: ReactNode }>
    ) => {
      setSlots((prevSlots) => ({ ...prevSlots, [name]: { ref, Wrapper } }));
    };
    const unregisterSlot = (name: string) => {
      setSlots((prevSlots) => {
        const newSlots = { ...prevSlots };
        delete newSlots[name];
        return newSlots;
      });
    };

    return {
      registerSlot,
      unregisterSlot,
      getSlot,
    };
  }, [slots]);

  return (
    <SlotFillContext.Provider value={contextValue}>
      {children}
    </SlotFillContext.Provider>
  );
};

export default SlotFillProvider;
