import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { IconProps } from "../Icon";
import { DragHandleIcon as MoveIcon } from "../icons";
import { ButtonIcon, ButtonIconProps } from "../ButtonIcon";

export interface DragHandleProps extends IconProps {
  DragIcon?: React.ReactNode;
  id?: string;
  buttonIconProps?: ButtonIconProps;
  hide?: boolean;
}

export const DragHandle = ({
  DragIcon = <MoveIcon color="info" />,
  id,
  buttonIconProps,
  hide = false,
  ...iconProps
}: DragHandleProps) => {
  const { attributes, listeners, isDragging } = useSortable({ id: id as any });
  const draggingProps = !id ? {} : { ...attributes, ...listeners };
  const DragIconComponent = React.cloneElement(
    DragIcon as React.ReactElement,
    iconProps
  );

  const buttonProps: ButtonIconProps = buttonIconProps || {
    onlyIcon: true,
    transparent: true,
  };
  if (hide) return null;
  return (
    <div {...draggingProps}>
      <ButtonIcon
        cursor={isDragging ? "grabbing" : "grab"}
        dataTest="button-drag-drop"
        {...buttonProps}
      >
        {DragIconComponent}
      </ButtonIcon>
    </div>
  );
};
