import {
  Identify,
  identify,
  track,
  init,
  add,
  setUserId,
} from "@amplitude/analytics-browser";
import { plugin as engagementPlugin } from "@amplitude/engagement-browser";
import { sessionReplayPlugin } from "@amplitude/plugin-session-replay-browser";
import { Attributes } from "./types";
import { urlSearchParamsToObject } from "./utils";

interface CreateAmplitudeProviderParams {
  config: { apiKey: string };
  user?: {
    id: string;
    attributes?: Attributes;
    [key: string]: any;
  };
}

export const createAmplitudeProvider = ({
  config: { apiKey },
  user = undefined,
}: CreateAmplitudeProviderParams) => {
  init(apiKey, user?.id, {
    minIdLength: 3,
    // serverUrl: 'https://79473c3dd1c7.ngrok.app/amplitude',
  });
  add(engagementPlugin());
  add(sessionReplayPlugin());
  if (user) {
    setUserId(user.id);
    const identifyEvent = new Identify();
    const { attributes, ...userProps } = user;
    Object.entries(userProps).forEach(([key, value]) => {
      identifyEvent.set(key, value);
    });
    if (attributes) {
      Object.entries(attributes).forEach(([key, value]) => {
        identifyEvent.set(key, value);
      });
    }
    identify(identifyEvent);
  }
  return {
    setContext: (key: string, context?: Attributes) => {
      // Not implemented
    },
    pushError: (message, context?: Attributes) => {
      // We don't push errors to Amplitude
    },
    pushEvent: (name: string, attributes?: Attributes) => {
      track(name, attributes);
    },
    view: (name: string, attributes?: Attributes) => {
      track("View", {
        name,
        url: window.location.href,
        path: window.location.pathname,
        search: urlSearchParamsToObject(
          new URLSearchParams(window.location.search)
        ),
        ...attributes,
      });
    },
  };
};
