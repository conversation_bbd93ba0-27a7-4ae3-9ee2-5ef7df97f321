import React, { useEffect, useState } from "react";
import styled, { css } from "styled-components";
import { Fill, Slot } from "../SlotFill";
import { BurgerIcon } from "../icons";
import { color } from "../../utils";
import { useTimeout } from "../../hooks";

const SLOT_NAME = "sidePanel";

export interface SidePanelProps {
  children?: React.ReactNode;
  customIcon?: React.ReactNode;
  customActivator?: React.ReactNode;
  open?: boolean;
  position?: "right" | "left";
  style?: React.CSSProperties;
  className?: string;
  closeOnEscape?: boolean;
  onClose?: (value: boolean) => void;
}

const SidePanel: React.FC<SidePanelProps> = ({
  customIcon = <BurgerIcon />,
  customActivator,
  open = false,
  position = "left",
  children,
  className,
  style,
  closeOnEscape,
  onClose,
}) => {
  const [isOpened, setIsOpened] = useState(open);
  const [hideChildren, setHideChildren] = useState(!isOpened);
  const { reset } = useTimeout(() => {
    if (!isOpened) setHideChildren(true);
    if (isOpened) setHideChildren(false);
  }, 200);

  useEffect(() => {
    setIsOpened(open);
  }, [open]);

  useEffect(() => {
    if (!isOpened) reset();
    if (isOpened) setHideChildren(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpened]);

  useEffect(() => {
    function handleKeyupEvent(event) {
      if (event.key === "Escape") {
        if (onClose) onClose(false);
        setIsOpened(false);
      }
    }

    if (closeOnEscape) {
      document.addEventListener("keyup", handleKeyupEvent);
    }

    return () => {
      document.removeEventListener("keyup", handleKeyupEvent);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {customActivator || (
        <StyledBurger
          onClick={() => {
            onClose && onClose(!isOpened);
            setIsOpened((prev) => !prev);
          }}
        >
          {customIcon}
        </StyledBurger>
      )}
      <Fill name={SLOT_NAME}>
        <Backdrop
          onClick={() => {
            onClose && onClose(false);
            setIsOpened(false);
          }}
          isOpened={isOpened}
        />
        <StyledSidePanel
          className={className}
          style={style}
          isOpened={isOpened}
          location={position}
          onClick={() => {
            !onClose && setIsOpened(false);
          }}
        >
          {!hideChildren ? children : null}
        </StyledSidePanel>
      </Fill>
    </>
  );
};

const SidePanelSlot = ({ name = SLOT_NAME }) => {
  return <Slot name={name} />;
};

export { SidePanel, SidePanelSlot };

/**
 *
 * Styles
 *
 */

const Backdrop = styled.div<{ isOpened: boolean }>`
  width: 0;
  height: 0;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 700;
  overflow-x: hidden;
  overflow-y: auto;
  outline: none;
  box-sizing: border-box;
  transition: opacity 0.15s ease-in-out;
  opacity: 0;
  ${({ isOpened }) =>
    isOpened &&
    css`
      opacity: 1;
      width: 100%;
      height: 100vh;
    `}
`;

const StyledBurger = styled.span`
  cursor: pointer;
  border-radius: 4px;
  padding: 4px 6px;
  &:hover,
  &:active {
    background-color: ${color("cloud", "darker")};
  }
`;

const SidePanelPosition = css<{ location: string }>`
  ${({ theme, location }) =>
    theme.rtl
      ? location === "left"
        ? "right:0"
        : "left:0"
      : location === "left"
      ? "left:0"
      : "right:0"};
`;

const StyledSidePanel = styled.div<{ isOpened?: boolean; location: string }>`
  position: fixed;
  top: 0;
  ${SidePanelPosition}
  max-width: 0;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.2), 0px 2px 10px rgba(0, 0, 0, 0.1);
  transition: max-width 0.3s ease-in-out;
  background-color: white;
  z-index: 701;
  ${({ isOpened }) =>
    isOpened &&
    css`
      max-width: 500px;
    `};
`;
