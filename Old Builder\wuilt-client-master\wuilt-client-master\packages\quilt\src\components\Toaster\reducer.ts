import cuid from "cuid";
import { Action, Config, State, Toast } from "./types";

const DEFAULT_TOAST = (config: Config): Omit<Toast, "content"> => ({
  id: cuid(),
  ...config,
});

export function reducer(state: State, action: Action) {
  switch (action.type) {
    case "addToast":
      return state?.items?.some((item) => item.terminateOtherToasts)
        ? state
        : {
            ...state,
            items: [
              { ...DEFAULT_TOAST(state.config), ...action.toast },
              ...(action.toast.terminateOtherToasts ? [] : state.items),
            ],
          };

    case "removeToast":
      return {
        ...state,
        items: state.items.filter((item) => item.id !== action.toastId),
      };

    case "removeAllToasts":
      return { ...state, items: [] };

    case "updateToast":
      return {
        ...state,
        items: state.items.map((item) =>
          item.id === action.toast.id ? action.toast : item
        ),
      };

    case "setDefaultConfig":
      return {
        ...state,
        config: { ...state.config, ...action.config },
      };

    default:
      return state;
  }
}
