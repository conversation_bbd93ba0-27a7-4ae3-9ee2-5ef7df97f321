import React from "react";

import { But<PERSON> } from "../Button";
import { ModalConfirm } from "./ModalConfirm";

export default {
  title: "Components/Modals/ModalConfirm",
  component: ModalConfirm,
};

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export const Playground = () => {
  const [message, setMessage] = React.useState("");
  const [loading, setLoading] = React.useState(false);
  const [openModal, setOpenModal] = React.useState(false);

  async function handleSubmit() {
    setLoading(true);
    await sleep(3000);
    setMessage("CONFIRMED");
    setLoading(false);
  }

  return (
    <>
      <p>{message}</p>

      <p>First method to call a confirmation modal</p>

      <Button color="danger" onClick={() => setOpenModal(true)}>
        Delete
      </Button>

      <ModalConfirm
        show={openModal}
        onClose={() => setOpenModal(false)}
        modalHeader="Are you sure?"
        modalBody={
          <p>&quot;This operation cannot be restored, are you sure?&quot;</p>
        }
        cancelText="Cancel"
        confirmText="Delete"
        loading={loading}
        onCancel={() => setMessage("CANCELED")}
        onConfirm={handleSubmit}
      />

      <br />

      <p>Second method to call a confirmation modal</p>

      <ModalConfirm
        modalHeader="Are you sure?"
        modalBody={
          <p>&quot;This operation cannot be restored, are you sure?&quot;</p>
        }
        cancelText="Cancel"
        confirmText="Delete"
        loading={loading}
        onCancel={() => setMessage("CANCELED")}
        onConfirm={handleSubmit}
      >
        <Button color="danger">Delete</Button>
      </ModalConfirm>
    </>
  );
};
