import { TIconType } from "../types";

export function Messenger({
  size = 40,
  width,
  height,
  color,
  messengerThunderColor,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.3811 27.7012V31.9703L16.3728 29.7009C17.4855 30.0216 18.6683 30.1947 19.8938 30.1947C26.463 30.1947 31.7876 25.2258 31.7876 19.0973C31.7876 12.9684 26.463 8 19.8938 8C13.325 8 8 12.9684 8 19.0973C8 22.5678 9.7074 25.6665 12.3811 27.7012Z"
        fill={color || "currentColor"}
      />
      <path
        d="M18.6237 16.0576L12.2095 22.849L18.047 19.6461L21.097 22.849L27.4751 16.0576L21.7023 19.2044L18.6237 16.0576Z"
        fill={messengerThunderColor}
      />
    </svg>
  );
}
