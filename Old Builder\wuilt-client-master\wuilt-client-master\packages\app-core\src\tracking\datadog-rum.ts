import { datadogRum } from "@datadog/browser-rum";
import { Attributes } from "./types";

export const createDatadogRumProvider = ({
  config: { applicationId, clientToken, site, env, version, service },
  user,
}) => {
  datadogRum.init({
    // proxy: "https://79473c3dd1c7.ngrok.app/datadog",
    applicationId,
    clientToken,
    site,
    service,
    env,
    version,
    sessionSampleRate: 100,
    sessionReplaySampleRate: 100,
    trackResources: true,
    trackLongTasks: true,
    trackUserInteractions: true,
    defaultPrivacyLevel: "allow",
  });

  if (user) {
    datadogRum.setUser({
      id: user.id,
      email: user.email,
      name: user.name,
    });
  }

  return {
    setContext: (key: string, context?: Attributes) => {
      if (!context) {
        datadogRum.removeGlobalContextProperty(key);
        return;
      }
      datadogRum.setGlobalContextProperty(key, context);
    },
    pushEvent: (name: string, attributes?: Attributes) => {
      datadogRum.addAction(name, attributes);
    },
    view: (name: string, attributes?: Attributes) => {
      datadogRum.startView(name);
      datadogRum.addAction(`View ${name}`, attributes);
    },
    pushError: (error: unknown, context?: Attributes) => {
      datadogRum.addError(error, context);
    },
  };
};
