/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useContext } from "react";
import { Context } from "./Provider";
import { Config, Toast } from "./types";

export function useToaster() {
  const dispatch = useContext(Context);
  if (!dispatch) {
    throw new Error("useToaster should be used within toaster provider");
  }

  const addToast = useCallback((toast: Toast) => {
    dispatch({ type: "addToast", toast });
    return toast;
  }, []);
  const removeToast = useCallback((toastId: string) => {
    dispatch({ type: "removeToast", toastId });
  }, []);
  const removeAllToasts = useCallback(() => {
    dispatch({ type: "removeAllToasts" });
  }, []);
  const updateToast = useCallback((toast: Toast) => {
    dispatch({ type: "updateToast", toast });
    return toast;
  }, []);
  const setDefaultConfig = useCallback((config: Config) => {
    dispatch({ type: "setDefaultConfig", config });
  }, []);

  return {
    addToast,
    removeToast,
    removeAllToasts,
    updateToast,
    setDefaultConfig,
  };
}
