import { TIconType } from "../types";

export function MessageDotsSquare({
  size = 40,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.25 8.75H6.25833M10 8.75H10.0083M13.75 8.75H13.7583M5.83333 15V16.9463C5.83333 17.3903 5.83333 17.6123 5.92436 17.7263C6.00352 17.8255 6.12356 17.8832 6.25045 17.8831C6.39636 17.8829 6.56973 17.7442 6.91646 17.4668L8.90434 15.8765C9.31043 15.5517 9.51347 15.3892 9.73957 15.2737C9.94017 15.1712 10.1537 15.0963 10.3743 15.051C10.6231 15 10.8831 15 11.4031 15H13.5C14.9001 15 15.6002 15 16.135 14.7275C16.6054 14.4878 16.9878 14.1054 17.2275 13.635C17.5 13.1002 17.5 12.4001 17.5 11V6.5C17.5 5.09987 17.5 4.3998 17.2275 3.86502C16.9878 3.39462 16.6054 3.01217 16.135 2.77248C15.6002 2.5 14.9001 2.5 13.5 2.5H6.5C5.09987 2.5 4.3998 2.5 3.86502 2.77248C3.39462 3.01217 3.01217 3.39462 2.77248 3.86502C2.5 4.3998 2.5 5.09987 2.5 6.5V11.6667C2.5 12.4416 2.5 12.8291 2.58519 13.147C2.81635 14.0098 3.49022 14.6836 4.35295 14.9148C4.67087 15 5.05836 15 5.83333 15ZM6.66667 8.75C6.66667 8.98012 6.48012 9.16667 6.25 9.16667C6.01988 9.16667 5.83333 8.98012 5.83333 8.75C5.83333 8.51988 6.01988 8.33333 6.25 8.33333C6.48012 8.33333 6.66667 8.51988 6.66667 8.75ZM10.4167 8.75C10.4167 8.98012 10.2301 9.16667 10 9.16667C9.76988 9.16667 9.58333 8.98012 9.58333 8.75C9.58333 8.51988 9.76988 8.33333 10 8.33333C10.2301 8.33333 10.4167 8.51988 10.4167 8.75ZM14.1667 8.75C14.1667 8.98012 13.9801 9.16667 13.75 9.16667C13.5199 9.16667 13.3333 8.98012 13.3333 8.75C13.3333 8.51988 13.5199 8.33333 13.75 8.33333C13.9801 8.33333 14.1667 8.51988 14.1667 8.75Z"
        stroke={color || "currentColor"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
