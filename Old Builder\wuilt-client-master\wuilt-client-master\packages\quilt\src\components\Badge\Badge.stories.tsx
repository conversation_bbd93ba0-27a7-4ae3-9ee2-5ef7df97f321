import React from "react";
import { Stack } from "../Stack/Stack";
import { Badge } from "./Badge";
import { CrownIcon } from "../icons";

export default {
  title: "Components/Badge",
  component: Badge,
};

export const Playground = () => {
  return (
    <Stack direction="row">
      <Badge id="test1" label="Success" type="success" />
      <Badge id="test2" label="Warning" type="warning" />
      <Badge
        id="test3"
        label="Premium"
        type="warning"
        prefixIcon={<CrownIcon size="sm" />}
      />
      <Badge id="test4" label="Failed" type="failed" />
      <Badge id="test5" label="Secondary" type="secondary" />
    </Stack>
  );
};
