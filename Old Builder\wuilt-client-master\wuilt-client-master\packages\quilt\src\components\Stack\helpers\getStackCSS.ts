import { css } from "styled-components";
import { layout, position } from "styled-system";
import {
  Border,
  BoxShadow,
  Color,
  Space,
  Typography,
} from "../../../themes/property-overriding";

const getStackCSS = (cssProps) => {
  return css`
    ${Color(cssProps)};
    ${Typography(cssProps)}
    ${BoxShadow(cssProps)}
		${Space(cssProps)}
		${layout(cssProps)}
		${position(cssProps)}
		${Border(cssProps)}
  `;
};

export default getStackCSS;
