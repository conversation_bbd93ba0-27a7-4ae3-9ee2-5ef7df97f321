import React from "react";
import styled, { css } from "styled-components";
import { useClickOutside } from "../../hooks/useClickOutside";
import { MoreHorizIcon } from "../icons";
import { Button, ButtonProps } from "../Button";
import { Popover } from "../Popover";
import { Stack } from "../Stack";
import { color } from "../../utils";
import { Global } from "../../common/types";
import { CSSObject } from "styled-components";

export interface DropMenuProps extends Global {
  readonly children?: React.ReactNode;
  readonly preferredPosition?: "top" | "bottom" | "";
  readonly preferredAlign?: "start" | "end" | "center";
  readonly buttonProps?: ButtonProps;
  readonly activator?: any;
  readonly applyHoverEffect?: boolean;
  readonly activatorStyles?: CSSObject;
}

const DropMenu: React.FC<DropMenuProps> = (props) => {
  const {
    preferredAlign = "end",
    preferredPosition = "bottom",
    activator: activatorProp,
    children,
    buttonProps = {},
    dataTest,
    applyHoverEffect,
    activatorStyles,
  } = props;
  const targetRef = React.useRef<HTMLElement>(null);
  const popoverRef = React.useRef(null);
  const [opened, setOpened] = React.useState(false);
  const handleClose = React.useCallback(() => {
    setOpened(false);
  }, []);
  const handleOpen = React.useCallback((e) => {
    e.stopPropagation();
    setOpened((prev) => !prev);
  }, []);

  const handleOut = React.useCallback((ev) => {
    if (targetRef.current && !targetRef?.current?.contains(ev.target)) {
      handleClose();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useClickOutside(popoverRef, handleOut);

  const activator = activatorProp || (
    <Button
      dataTest={dataTest}
      plain
      size="small"
      squared
      prefixIcon={buttonProps.suffixIcon ? null : <MoreHorizIcon />}
      {...buttonProps}
    />
  );
  return (
    <>
      <StyledActivatorWrapper
        opened={opened}
        ref={targetRef as any}
        onClick={handleOpen}
        clearStyles={!!activatorProp}
        style={activatorStyles}
      >
        {activator}
      </StyledActivatorWrapper>
      <Popover
        target={targetRef}
        ref={popoverRef}
        opened={opened}
        preferredAlign={preferredAlign}
        preferredPosition={preferredPosition}
      >
        <StyledContentWrapper
          onClick={handleClose}
          isHover={applyHoverEffect || false}
        >
          <Stack spacing="none" align="start">
            {children}
          </Stack>
        </StyledContentWrapper>
      </Popover>
    </>
  );
};

export { DropMenu };

/**
 *
 *
 * Styles
 *
 */

const StyledActivatorWrapper = styled.div<{
  opened: boolean;
  clearStyles: boolean;
  style?: CSSObject;
}>`
  ${({ clearStyles, opened }) =>
    !clearStyles &&
    css`
      cursor: pointer;
      display: inline-block;
      border-radius: 4px;
      padding: 4px 6px;
      ${opened &&
      css`
        background-color: ${color("cloud", "darker")};
      `}

      &:hover,
      &:active {
        background-color: ${color("cloud", "darker")} !important;
      }
    `}
  ${({ style }) => style && style}
`;

const StyledContentWrapper = styled.div<{ isHover: boolean }>`
  overflow: auto;
  max-height: 100%;
  padding: 10px 16px;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 0 1px 0 rgba(9, 30, 66, 0.31),
    0 4px 8px -2px rgba(9, 30, 66, 0.25);

  ${({ isHover }) =>
    isHover &&
    css`
      > div > * {
        &:hover {
          filter: drop-shadow(2px 4px 10px grey);
        }
      }
    `}
`;
