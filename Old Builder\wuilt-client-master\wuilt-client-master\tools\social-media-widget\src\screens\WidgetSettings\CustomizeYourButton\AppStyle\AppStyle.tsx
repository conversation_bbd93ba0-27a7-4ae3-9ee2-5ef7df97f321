import React from "react";
import { Heading, Stack } from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import ButtonSizeAndRadius from "./ButtonSizeAndRadius";
import StyleTabs from "./StyleTabs";
import ButtonWithText from "./ButtonWithText";
import IconAndAnimation from "./IconAndAnimation";
import { TWidgetAppearance, TWidgetSettings } from "../../../../shared/types";
interface AppStyleProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function AppStyle({ appearance, update }: AppStyleProps) {
  const {
    content: { withText },
  } = appearance;
  return (
    <Stack
      boxShadow="0px 1px 2px 0px #1018280D"
      spacing="none"
      width="100%"
      border="1px solid #EAECF0"
      borderRadius="10px"
      padding="16px"
      flex="1.3 1"
    >
      <Heading color="black" fontSize="sm" fontWeight="semiBold">
        <FormattedMessage defaultMessage="Style" id="7mL9QE" />
      </Heading>
      <StyleTabs appearance={appearance} update={update} />
      {withText ? (
        <ButtonWithText update={update} appearance={appearance} />
      ) : null}
      <ButtonSizeAndRadius
        update={update}
        buttonStyle={appearance.style}
        appearance={appearance}
      />
      <IconAndAnimation update={update} appearance={appearance} />
    </Stack>
  );
}

export default AppStyle;
