import { QuestionMarkIcon, Stack, Text, Tooltip } from "@wuilt/quilt";
import React from "react";
const EX = "ex: ";
interface AppInfoProps {
  info: string;
}
function AppInfo({ info }: AppInfoProps) {
  return (
    <Tooltip
      arrowColor="white"
      style={{
        background: "white",
        color: "black",
        padding: "12px",
        borderRadius: "8px",
        boxShadow:
          "0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08)",
      }}
      content={
        <Stack spacing="none" direction="row" align="center">
          <Text style={{ color: "#98A2B3" }} fontSize="xs" fontWeight="medium">
            {EX}
          </Text>
          <Text fontSize="xs" fontWeight="medium" style={{ color: "#475467" }}>
            {info}
          </Text>
        </Stack>
      }
    >
      <Stack cursor="pointer">
        <QuestionMarkIcon customColor="#98A2B3" />
      </Stack>
    </Tooltip>
  );
}

export default AppInfo;
