import React, { useContext } from "react";
import { getIn } from "final-form";
import { Field } from "./Field";
import { FormStateContext } from "./Form";

export interface FieldValueProps<Type = any> {
  children: ({ value }: { value: Type }) => React.ReactNode;
  name: string;
  defaultValue?: Type;
  hideOnFalse?: boolean;
}

function FieldValue<Type = any>({
  children,
  name,
  defaultValue,
  hideOnFalse = true,
}: FieldValueProps<Type>) {
  const { initialValues } = useContext(FormStateContext);
  const initialValue = getIn(initialValues, name);

  if (Object.keys(initialValues).length === 0 && hideOnFalse) {
    return null;
  }
  return (
    <Field<Type>
      name={name}
      margin="0"
      defaultValue={defaultValue || initialValue}
      hideErrorMessage
      isError={false}
    >
      {({ fieldProps: { value } }) => children({ value })}
    </Field>
  );
}

FieldValue.displayName = "FieldValue";
export { FieldValue };
