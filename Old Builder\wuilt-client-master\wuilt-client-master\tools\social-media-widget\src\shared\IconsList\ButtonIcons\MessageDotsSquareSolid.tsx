import { TIconType } from "../types";

export function MessageDotsSquareSolid({
  size = 20,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.5343 1.66675H6.4655C5.7947 1.66674 5.24106 1.66673 4.79006 1.70358C4.32165 1.74185 3.89106 1.82399 3.48662 2.03006C2.85941 2.34964 2.34947 2.85957 2.0299 3.48678C1.82382 3.89123 1.74168 4.32181 1.70341 4.79023C1.66656 5.24122 1.66657 5.79486 1.66658 6.46567L1.66654 11.7814C1.66618 12.4442 1.66592 12.9364 1.78016 13.3628C2.08839 14.5131 2.98688 15.4116 4.13719 15.7198C4.39341 15.7885 4.67341 15.8158 4.99992 15.8266L4.99991 16.9759C4.99988 17.1719 4.99984 17.3718 5.01471 17.5355C5.02894 17.6921 5.06661 17.9878 5.27299 18.2463C5.51047 18.5438 5.87059 18.7169 6.25127 18.7165C6.58209 18.7161 6.83645 18.5609 6.96764 18.4741C7.10481 18.3835 7.26084 18.2586 7.4139 18.1361L9.42484 16.5273C9.857 16.1816 9.9853 16.084 10.1186 16.0159C10.2523 15.9476 10.3947 15.8976 10.5418 15.8674C10.6885 15.8373 10.8496 15.8334 11.403 15.8334H13.5344C14.2052 15.8334 14.7588 15.8334 15.2098 15.7966C15.6782 15.7583 16.1088 15.6762 16.5132 15.4701C17.1404 15.1505 17.6504 14.6406 17.9699 14.0134C18.176 13.6089 18.2582 13.1784 18.2964 12.7099C18.3333 12.2589 18.3333 11.7053 18.3333 11.0345V6.46565C18.3333 5.79485 18.3333 5.24122 18.2964 4.79023C18.2582 4.32181 18.176 3.89123 17.9699 3.48678C17.6504 2.85957 17.1404 2.34964 16.5132 2.03006C16.1088 1.82399 15.6782 1.74185 15.2098 1.70358C14.7588 1.66673 14.2051 1.66674 13.5343 1.66675ZM4.99984 8.75008C4.99984 8.05973 5.55948 7.50008 6.24984 7.50008C6.94019 7.50008 7.49984 8.05973 7.49984 8.75008C7.49984 9.44044 6.94019 10.0001 6.24984 10.0001C5.55948 10.0001 4.99984 9.44044 4.99984 8.75008ZM8.74984 8.75008C8.74984 8.05973 9.30948 7.50008 9.99984 7.50008C10.6902 7.50008 11.2498 8.05973 11.2498 8.75008C11.2498 9.44044 10.6902 10.0001 9.99984 10.0001C9.30948 10.0001 8.74984 9.44044 8.74984 8.75008ZM13.7498 7.50008C13.0595 7.50008 12.4998 8.05973 12.4998 8.75008C12.4998 9.44044 13.0595 10.0001 13.7498 10.0001C14.4402 10.0001 14.9998 9.44044 14.9998 8.75008C14.9998 8.05973 14.4402 7.50008 13.7498 7.50008Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
