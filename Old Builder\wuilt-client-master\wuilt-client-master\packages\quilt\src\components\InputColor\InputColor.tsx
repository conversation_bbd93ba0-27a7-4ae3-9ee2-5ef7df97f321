import React from "react";
import styled, { css } from "styled-components";
import { HexAlphaColorPicker } from "react-colorful";
import { InputField } from "../InputField";
import { Popover } from "../Popover";
import { GeneralInputWrapper } from "../InputField/InputField";
import { borderRadius, rtl } from "../../utils";

const StyledWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  height: 36px;

  ${GeneralInputWrapper} {
    height: 36px;

    input {
      text-transform: uppercase;
    }

    ${({ theme }) =>
      theme.rtl
        ? css`
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
          `
        : css`
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
          `}
  }
`;

const SelectedColorIndicator = styled.div<{ color: string }>`
  width: 34px;
  border-radius: ${borderRadius("4px 0 0 4px")};
  height: 100%;
  border: 1px solid ${({ theme }) => theme.palette.ink.lighter};
  background: ${({ color }) => color};
  ${rtl("border-left: none", "border-right: none")};
`;

export interface InputColorProps {
  onChange: (newValue: string) => void;
  value: string;
  isDisabled?: boolean;
  isError?: boolean;
  onBlur?: React.FocusEventHandler<HTMLDivElement>;
}
const InputColor: React.FC<InputColorProps> = ({
  onChange,
  value,
  isDisabled,
  isError,
  onBlur,
}) => {
  const target = React.useRef<HTMLDivElement>(null);
  const [isOpen, setOpen] = React.useState(false);
  return (
    <>
      <div ref={target}>
        <StyledWrapper>
          <SelectedColorIndicator
            color={value}
            onClick={() => setOpen(!isOpen)}
          />
          <InputField
            label="Check me on!"
            value={value}
            isDisabled={isDisabled || undefined}
            isError={isError}
            onFocus={() => setOpen(true)}
            onChange={({ target: { value } }) => {
              onChange(value);
            }}
          />
        </StyledWrapper>
      </div>
      <Popover
        target={target}
        opened={isOpen}
        onClose={() => setOpen(false)}
        noFocus
      >
        <HexAlphaColorPicker
          color={value}
          onChange={onChange}
          onBlur={onBlur}
        />
      </Popover>
    </>
  );
};

InputColor.displayName = "InputColor";

export { InputColor };
