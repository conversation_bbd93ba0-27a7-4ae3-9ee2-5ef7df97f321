import { useState, useCallback, useRef } from "react";

const calculate = (seconds?: number) => {
  if (!seconds) return { hrs: "00", mins: "00", secs: "00" };
  const hrs = `${parseInt(`${seconds / 3600}`, 10)}`.padStart(2, "0");
  const mins = `${parseInt(`${seconds / 60}`, 10)}`.padStart(2, "0");
  const secs = `${parseInt(`${seconds % 60}`, 10)}`.padStart(2, "0");
  return { hrs, mins, secs };
};

export function useTimer(hookSecs?: number, hookStep?: number) {
  const [countDown, setCountDown] = useState(() => calculate(hookSecs));
  const intervalRef = useRef<NodeJS.Timer>();

  const clear = useCallback(() => {
    if (!intervalRef.current) return;
    clearInterval(intervalRef.current);
    intervalRef.current = undefined;
    setCountDown(calculate(0));
  }, []);

  const start = useCallback(
    (funcSecs?: number, funcStep?: number) => {
      const step = funcStep || hookStep || 1000;
      let seconds = hookSecs || funcSecs;
      if (!seconds) return;

      intervalRef.current = setInterval(() => {
        if (!seconds) return;
        setCountDown(calculate(seconds));
        if (--seconds <= 0) clear();
      }, step);
    },
    [clear, hookSecs, hookStep]
  );

  return [countDown, start, clear] as const;
}
