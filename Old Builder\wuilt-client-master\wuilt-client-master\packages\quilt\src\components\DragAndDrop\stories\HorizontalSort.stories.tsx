import React from "react";
import { ComponentStory, ComponentMeta } from "@storybook/react";
import { HorizontalSort } from "../HorizontalSort";
import { Heading } from "../../Heading";
import { Stack } from "../../Stack";
import { Box } from "../../Box";
import { DragHandle } from "../DragHandle";

// https://storybook.js.org/docs/react/essentials/controls
export default {
  title: "Components/Sorting/Horizontal",
  component: HorizontalSort,
  argTypes: {
    value: { control: false },
    uniqueFieldName: { control: false },
    className: { control: false },
    boxProps: { control: false },
  },
} as ComponentMeta<typeof HorizontalSort>;

const ITEMS = [
  { title: "item 1", id: "1" },
  { title: "item 2", id: "2" },
  { title: "item 3", id: "3" },
  { title: "item 4", id: "4" },
  { title: "item 5", id: "5" },
];

const Template: ComponentStory<typeof HorizontalSort> = ({
  limitToContainerEdges,
  useHandleOnly,
  ...restProps
}) => {
  const [items, setItems] = React.useState(ITEMS);

  const handleChange = (sortedItems) => {
    setItems(sortedItems);
  };

  return (
    <HorizontalSort
      {...restProps}
      limitToContainerEdges={limitToContainerEdges}
      useHandleOnly={useHandleOnly}
      value={items}
      onChange={handleChange}
    >
      {({ item }) => (
        <Box
          mr="10px"
          className="dnd-item"
          border="1px solid"
          borderColor="disabled"
          p="20px"
          bg="warning"
        >
          <Stack direction="row">
            <DragHandle id={item?.id} />
            <Heading>{item.title}</Heading>
          </Stack>
        </Box>
      )}
    </HorizontalSort>
  );
};

export const ListSorting = Template.bind({});
ListSorting.args = {
  useHandleOnly: false,
  limitToContainerEdges: true,
};
