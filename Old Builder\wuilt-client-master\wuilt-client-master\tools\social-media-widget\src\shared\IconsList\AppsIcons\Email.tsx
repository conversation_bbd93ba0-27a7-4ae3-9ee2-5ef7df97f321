import { TIconType } from "../types";

export function Email({ size = 40, width, height, color }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.0655 15.7115C9.82337 15.5406 9.70229 15.4551 9.59906 15.4342C9.37159 15.3882 9.13609 15.5103 9.04248 15.7226C8.99999 15.819 9 15.9656 9 16.2587V22.7413C8.99999 23.5463 8.99998 24.2106 9.04419 24.7518C9.06792 25.0422 9.40639 25.159 9.62227 24.9634L14.8165 20.2581C15.0695 20.0289 15.196 19.9142 15.2364 19.7826C15.2718 19.6673 15.2642 19.543 15.2151 19.4327C15.1591 19.307 15.0197 19.2085 14.7407 19.0116L10.0655 15.7115Z"
        fill={color || "currentColor"}
      />
      <path
        d="M10.6911 26.6938C10.4875 26.8782 10.3857 26.9705 10.3454 27.1023C10.3142 27.2045 10.3235 27.3551 10.3672 27.4527C10.4235 27.5785 10.5163 27.6437 10.7021 27.7741C10.8551 27.8815 11.016 27.9784 11.184 28.064C11.6694 28.3113 12.1861 28.4099 12.7482 28.4558C13.2894 28.5 13.9537 28.5 14.7587 28.5H25.2413C26.0463 28.5 26.7106 28.5 27.2518 28.4558C27.8139 28.4099 28.3306 28.3113 28.816 28.064C28.984 27.9784 29.1449 27.8814 29.2979 27.7741C29.4837 27.6437 29.5765 27.5785 29.6328 27.4527C29.6765 27.3551 29.6859 27.2045 29.6546 27.1023C29.6143 26.9705 29.5125 26.8782 29.3089 26.6938L23.1892 21.15C23.0722 21.0441 23.0138 20.9911 22.9505 20.9601C22.8306 20.9013 22.6922 20.8929 22.5661 20.9368C22.4995 20.9599 22.435 21.0055 22.3062 21.0965C21.7589 21.484 21.2783 21.8243 20.7303 21.9618C20.2509 22.0821 19.7493 22.0821 19.2699 21.9618C18.7218 21.8243 18.2413 21.484 17.694 21.0965C17.5651 21.0054 17.5006 20.9599 17.4339 20.9367C17.3079 20.8929 17.1695 20.9013 17.0497 20.96C16.9864 20.991 16.9279 21.044 16.8109 21.15L10.6911 26.6938Z"
        fill={color || "currentColor"}
      />
      <path
        d="M30.3777 24.9634C30.5936 25.159 30.9321 25.0421 30.9558 24.7518C31 24.2106 31 23.5463 31 22.7413V16.2587C31 15.9657 31 15.8192 30.9576 15.7228C30.864 15.5104 30.6284 15.3883 30.4009 15.4344C30.2977 15.4552 30.1766 15.5407 29.9346 15.7115L25.2594 19.0117C24.9804 19.2086 24.8409 19.307 24.785 19.4328C24.7359 19.543 24.7283 19.6673 24.7637 19.7827C24.804 19.9143 24.9306 20.0289 25.1836 20.2582L30.3777 24.9634Z"
        fill={color || "currentColor"}
      />
      <path
        d="M30.2796 12.2073C30.3888 12.3632 30.4434 12.4411 30.4602 12.5597C30.4733 12.6521 30.4508 12.7809 30.4072 12.8634C30.3513 12.9693 30.2568 13.036 30.0676 13.1695L21.2688 19.3805C20.5361 19.8977 20.3807 19.9875 20.2435 20.0219C20.0837 20.062 19.9165 20.062 19.7567 20.0219C19.6195 19.9875 19.4641 19.8977 18.7314 19.3805L9.93241 13.1695C9.74329 13.036 9.64873 12.9692 9.5928 12.8633C9.54921 12.7808 9.52676 12.652 9.53986 12.5596C9.55667 12.441 9.61126 12.3631 9.72044 12.2072C10.0947 11.6728 10.604 11.2315 11.184 10.936C11.6694 10.6887 12.1861 10.5901 12.7482 10.5442C13.2894 10.5 13.9537 10.5 14.7587 10.5H25.2413C26.0463 10.5 26.7106 10.5 27.2518 10.5442C27.8139 10.5901 28.3306 10.6887 28.816 10.936C29.396 11.2315 29.9053 11.6728 30.2796 12.2073Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
