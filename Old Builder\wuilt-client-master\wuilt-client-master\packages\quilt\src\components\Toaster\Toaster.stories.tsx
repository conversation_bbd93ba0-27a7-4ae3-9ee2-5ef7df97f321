import React from "react";
import { Toaster } from "./index";
import { Button } from "../Button";
import { useToaster } from "./useToaster";
import { RadioButton, RadioGroup } from "../RadioButton";

export default {
  title: "Components/Toaster",
  components: Toaster,
};

export const Playground = () => {
  const [state, setState] = React.useState<any>("info");
  const { addToast } = useToaster();

  const handleAdd = () => {
    addToast({ content: state.toUpperCase(), appearance: state });
  };

  return (
    <>
      <RadioGroup
        onChange={(value) => setState(value)}
        value={state}
        alignment="horizontal"
      >
        <RadioButton value="info" label="Info" />
        <RadioButton value="success" label="Success" />
        <RadioButton value="warning" label="Warning" />
        <RadioButton value="error" label="Error" />
      </RadioGroup>
      <br />
      <br />
      <Button onClick={handleAdd}>Add Toast</Button>
    </>
  );
};
