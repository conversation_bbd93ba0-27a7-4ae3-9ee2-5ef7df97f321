import React, { useEffect, useRef, useState } from "react";
import { FixedSizeGrid, ListOnScrollProps } from "react-window";
import { SearchIcon } from "../icons";
import { InputField } from "../InputField";
import { Box, BoxProps } from "../Box";
import { useTheme } from "styled-components";

export type VirtualizedGridChildrenProps<Type> = {
  style: React.CSSProperties;
  rowIndex: number;
  columnIndex: number;
  isScrolling: boolean;
  item: Type;
};

export interface VirtualizedGridProps<Type = any> {
  items: Type[];
  placeholder?: string;
  listHeight?: number;
  listWidth?: number;
  columnCount: number;
  columnWidth: number;
  rowCount?: number;
  rowHeight: number;
  isLoading?: boolean;
  useIsScrolling?: boolean;
  hideSearchBar?: boolean;
  boxStyle?: BoxProps;
  searchKey?: string;
  onFetchMore?: () => void;
  onFilter?: (searchQuery: any) => void;
  children: (args: VirtualizedGridChildrenProps<Type>) => React.ReactElement;
}

function VirtualizedGrid<Type>({
  children,
  items,
  placeholder,
  listHeight: listHeightProp,
  listWidth: listWidthProp,
  columnCount,
  columnWidth,
  rowCount: rowCountProp,
  rowHeight,
  isLoading,
  hideSearchBar,
  boxStyle,
  useIsScrolling,
  searchKey = "title",
  onFetchMore,
  onFilter,
  ...listProps
}: VirtualizedGridProps<Type>) {
  const theme = useTheme();
  const [filteredItems, setFilteredItems] = useState<Type[]>(items);
  const previousItemsLength = useRef(0);
  const rowCount = rowCountProp || filteredItems.length / columnCount;
  const listHeight = listHeightProp || rowCount * rowHeight;
  const listWidth = listWidthProp || columnCount * columnWidth;

  let threshold: number = (rowHeight * items.length - listHeight) * 0.75;

  useEffect(() => {
    setFilteredItems(items);
  }, [items]);

  const handleFilter = (value: string) => {
    if (onFilter) {
      onFilter(value);
    } else {
      const tempItems = items.filter((elem) => {
        const compareTo = typeof elem === "string" ? elem : elem?.[searchKey];
        return compareTo?.toLowerCase()?.includes(value);
      });
      setFilteredItems(tempItems);
      if (onFetchMore) {
        threshold = (rowHeight * tempItems.length - listHeight) * 0.75;
      }
    }
  };

  const handleScroll = (state: ListOnScrollProps) => {
    if (
      onFetchMore &&
      rowCount &&
      state.scrollOffset > threshold &&
      filteredItems.length < rowCount &&
      previousItemsLength.current !== items.length
    ) {
      previousItemsLength.current = items.length;
      onFetchMore();
    }
  };

  return (
    <Box width="100%">
      {!hideSearchBar && (
        <Box mb="16px">
          <InputField
            type="text"
            debounce={200}
            onChange={({ target: { value } }) =>
              handleFilter(value?.toLocaleLowerCase())
            }
            prefix={<SearchIcon color="secondary" />}
            placeholder={placeholder}
            loading={isLoading}
          />
        </Box>
      )}

      <Box overflow="auto" {...boxStyle}>
        <FixedSizeGrid
          itemData={filteredItems}
          useIsScrolling={useIsScrolling}
          onScroll={handleScroll}
          direction={theme.dir}
          height={listHeight}
          width={listWidth}
          columnCount={columnCount}
          columnWidth={columnWidth}
          rowCount={rowCount}
          rowHeight={rowHeight}
          {...listProps}
        >
          {({ columnIndex, rowIndex, isScrolling, style }) => {
            const item = filteredItems[columnIndex + rowIndex * columnCount];
            if (!item) return null;
            return children({
              style,
              isScrolling,
              columnIndex,
              rowIndex,
              item,
            });
          }}
        </FixedSizeGrid>
      </Box>
    </Box>
  );
}

export { VirtualizedGrid };
