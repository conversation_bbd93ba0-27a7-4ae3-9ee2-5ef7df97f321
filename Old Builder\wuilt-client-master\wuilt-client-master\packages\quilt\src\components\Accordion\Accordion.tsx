import React, { useEffect, useState } from "react";
import styled, { css } from "styled-components";
import { Box, BoxProps } from "../Box";
import { Header, HeaderProps } from "./Header";
import { Body, BodyProps } from "./Body";
import { color } from "../../utils/colors";

import { AccordionContext } from "./AccordionContext";

export interface AccordionProps extends BoxProps {
  initial?: boolean;
  value?: boolean;
  hasCustomStyle?: boolean;
  onChange?: (value: boolean) => void;
}

const Accordion: React.FC<AccordionProps> & {
  Header: React.FC<HeaderProps>;
  Body: React.FC<BodyProps>;
} = ({
  children,
  initial = false,
  hasCustomStyle = false,
  value,
  onChange,
  ...boxProps
}) => {
  const [isOpen, setIsOpen] = useState(initial);

  useEffect(() => {
    if (typeof value !== "undefined") {
      setIsOpen(value);
    }
  }, [value]);

  return (
    <AccordionContext.Provider value={[value ?? isOpen, onChange ?? setIsOpen]}>
      <StyledWrapper
        isOpen={value ?? isOpen}
        hasCustomStyle={hasCustomStyle}
        {...boxProps}
      >
        {children}
      </StyledWrapper>
    </AccordionContext.Provider>
  );
};

Accordion.Header = Header;
Accordion.Body = Body;

Accordion.displayName = "Accordion";
export { Accordion };

/**
 *
 * Styles
 *
 */

const StyledWrapper = styled(Box)<{ isOpen: boolean; hasCustomStyle: boolean }>`
  ${({ hasCustomStyle }) =>
    !hasCustomStyle &&
    css<{ isOpen: boolean }>`
      border: 1px solid ${color("cloud", "darker")};

      &:hover {
        background-color: ${color("cloud", "light")};
      }

      ${({ isOpen }) =>
        isOpen &&
        css`
          background-color: ${color("cloud", "light")};
        `}
    `}
`;
