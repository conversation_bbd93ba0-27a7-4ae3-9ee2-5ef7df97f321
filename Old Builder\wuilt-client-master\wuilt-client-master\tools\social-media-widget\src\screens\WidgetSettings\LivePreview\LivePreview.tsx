import {
  <PERSON>,
  Button,
  <PERSON>,
  ErrorMessage,
  <PERSON>ing,
  InputField,
  Stack,
  Text,
} from "@wuilt/quilt";
import React, { useRef, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import LivePreviewModal from "./LivePreviewModal";
import styled from "styled-components";
import { LocaleEnum } from "../../../main";
interface LivePreviewProps {
  locale: LocaleEnum;
}

const LivePreview: React.FC<LivePreviewProps> = ({ locale }) => {
  const intl = useIntl();
  const inputRef = useRef<HTMLInputElement>(null);
  const [url, setUrl] = useState("");
  const [openModal, setOpenModal] = useState(false);
  const [error, setError] = useState<React.ReactNode>();

  const onClose = () => {
    setOpenModal(false);
    setUrl("");
  };

  const onOpen = () => {
    const input = inputRef.current?.value;
    if (!input) {
      setError(
        <FormattedMessage defaultMessage="Enter your website URL" id="2YDmF6" />
      );
      return;
    }
    if (!input.startsWith("http")) {
      setError(
        <FormattedMessage defaultMessage="Invalid website URL" id="6rOs8d" />
      );
      return;
    }
    setOpenModal(true);
    setUrl(input);
  };

  return (
    <>
      <Card largeMobile={{ display: "none" }} borderRadius="10px">
        <Card.Header>
          <Stack spacing="tight">
            <Heading fontWeight="semiBold">
              <FormattedMessage defaultMessage="Live Preview" id="x5i5IT" />
            </Heading>
            <Text fontSize="sm" fontWeight="medium">
              <FormattedMessage
                defaultMessage="Check how the button will look on your website"
                id="z6AkPo"
              />
            </Text>
          </Stack>
        </Card.Header>
        <Card.Body>
          <Box style={{ direction: "ltr" }}>
            <InputField
              borderRadius=" 8px"
              ref={inputRef}
              placeholder={intl.formatMessage({
                defaultMessage: "ex. https://wuilt.com",
                id: "bBw2rk",
              })}
              suffix={
                <StyledButton size="large" squared onClick={onOpen}>
                  <FormattedMessage defaultMessage="Check" id="RDZVQL" />
                </StyledButton>
              }
              onChange={() => setError(null)}
            />
            {error && <ErrorMessage>{error}</ErrorMessage>}
          </Box>
        </Card.Body>
      </Card>
      <LivePreviewModal
        show={openModal}
        onClose={onClose}
        url={url}
        locale={locale}
      />
    </>
  );
};

export { LivePreview };

/**
 * Styles
 */

const StyledButton = styled(Button)`
  transform: translateX(12px);
  border-start-end-radius: 8px;
  border-end-end-radius: 8px;
  border-start-start-radius: 0;
  border-end-start-radius: 0;
  &:hover {
    box-shadow: none;
  }
`;
