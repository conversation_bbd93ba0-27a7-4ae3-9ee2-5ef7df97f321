// import React from "react";
// import { RadioGroup } from "./index";
// import { Field, Form, FormSubmit } from "../Form";
// import { RadioButton } from "./RadioButton";

// export default {
//   title: "Components/Form/RadioButton",
//   component: RadioGroup,
// };

// export const Playground = () => {
//   return (
//     <Form
//       onSubmit={(data) => {
//         // eslint-disable-next-line no-console
//         console.log(data);
//       }}
//     >
//       {({ formProps }) => (
//         <form {...formProps}>
//           <Field<string | number> name="radio" defaultValue="option1">
//             {({ fieldProps }) => (
//               <RadioGroup {...fieldProps} name="test">
//                 <RadioButton label="Option 1" value="option1" />
//                 <RadioButton label="Option 2" value="option2" />
//                 <RadioButton label="Dynamic Option" value="option3">
//                   {({ isSelected }) =>
//                     isSelected ? <h3>This Option is selected</h3> : null
//                   }
//                 </RadioButton>
//                 <RadioButton
//                   label="Disabled Option"
//                   value="option4"
//                   isDisabled
//                 />
//               </RadioGroup>
//             )}
//           </Field>
//           <FormSubmit>Submit</FormSubmit>
//         </form>
//       )}
//     </Form>
//   );
// };
