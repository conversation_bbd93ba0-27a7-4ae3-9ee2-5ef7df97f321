import React from "react";
import { TIconType } from "../types";

export function MessageTextSquareSolid({
  size = 20,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.5343 1.66675H6.46543C5.79463 1.66674 5.24098 1.66673 4.78999 1.70358C4.32158 1.74185 3.89099 1.82399 3.48654 2.03006C2.85934 2.34964 2.3494 2.85957 2.02982 3.48678C1.82375 3.89123 1.74161 4.32181 1.70334 4.79023C1.66649 5.24122 1.6665 5.79486 1.66651 6.46567L1.66651 16.9759C1.66647 17.1719 1.66643 17.3718 1.68131 17.5355C1.69553 17.6921 1.7332 17.9878 1.93958 18.2463C2.17707 18.5438 2.53719 18.7169 2.91786 18.7165C3.24868 18.7161 3.50305 18.5609 3.63424 18.4741C3.7714 18.3835 3.92742 18.2586 4.08049 18.1361L6.09143 16.5273C6.52359 16.1816 6.65189 16.084 6.78521 16.0159C6.91894 15.9476 7.06129 15.8976 7.20839 15.8674C7.35505 15.8373 7.51621 15.8334 8.06964 15.8334H13.5343C14.2051 15.8334 14.7587 15.8334 15.2097 15.7966C15.6781 15.7583 16.1087 15.6762 16.5131 15.4701C17.1404 15.1505 17.6503 14.6406 17.9699 14.0134C18.1759 13.6089 18.2581 13.1784 18.2964 12.7099C18.3332 12.2589 18.3332 11.7053 18.3332 11.0345V6.46565C18.3332 5.79485 18.3332 5.24122 18.2964 4.79023C18.2581 4.32181 18.1759 3.89123 17.9699 3.48678C17.6503 2.85957 17.1404 2.34964 16.5131 2.03006C16.1087 1.82399 15.6781 1.74185 15.2097 1.70358C14.7587 1.66673 14.2051 1.66674 13.5343 1.66675ZM5.83317 6.25008C5.37293 6.25008 4.99984 6.62318 4.99984 7.08342C4.99984 7.54365 5.37293 7.91675 5.83317 7.91675H9.99984C10.4601 7.91675 10.8332 7.54365 10.8332 7.08342C10.8332 6.62318 10.4601 6.25008 9.99984 6.25008H5.83317ZM5.83317 9.16675C5.37293 9.16675 4.99984 9.53984 4.99984 10.0001C4.99984 10.4603 5.37293 10.8334 5.83317 10.8334H12.4998C12.9601 10.8334 13.3332 10.4603 13.3332 10.0001C13.3332 9.53984 12.9601 9.16675 12.4998 9.16675H5.83317Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
