'use client'

import {
  Box,
  VStack,
  Text,
  IconButton,
  Flex,
  Collapse,
  useColorModeValue,
  useDisclosure
} from '@chakra-ui/react'
import {
  ChevronDownIcon,
  ChevronRightIcon,
  ViewIcon,
  ViewOffIcon,
  LockIcon,
  UnlockIcon
} from '@chakra-ui/icons'
import { useEditorStore } from '../../../lib/stores/editorStore'

export function LayersPanel() {
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const bgColor = useColorModeValue('white', 'gray.800')
  const hoverBg = useColorModeValue('gray.50', 'gray.700')
  const selectedBg = useColorModeValue('blue.50', 'blue.900')
  
  const {
    currentPage,
    selectedElement,
    selectedSection,
    selectElement,
    selectSection
  } = useEditorStore()

  if (!currentPage) {
    return (
      <Box p={4} textAlign="center">
        <Text color="gray.500" fontSize="sm">
          No page loaded
        </Text>
      </Box>
    )
  }

  return (
    <Box h="100%" bg={bgColor}>
      <Box
        p={3}
        borderBottom="1px"
        borderColor={borderColor}
      >
        <Text fontSize="sm" fontWeight="semibold" color="gray.600">
          Layers
        </Text>
      </Box>
      
      <Box h="calc(100% - 50px)" overflowY="auto" p={2}>
        <VStack spacing={1} align="stretch">
          {currentPage.sections.map((section) => (
            <SectionLayer
              key={section.id}
              section={section}
              isSelected={selectedSection?.id === section.id}
              selectedElementId={selectedElement?.id}
              onSelectSection={() => selectSection(section)}
              onSelectElement={selectElement}
            />
          ))}
          
          {currentPage.sections.length === 0 && (
            <Box p={4} textAlign="center">
              <Text color="gray.500" fontSize="sm">
                No sections yet
              </Text>
            </Box>
          )}
        </VStack>
      </Box>
    </Box>
  )
}

interface SectionLayerProps {
  section: any
  isSelected: boolean
  selectedElementId?: string
  onSelectSection: () => void
  onSelectElement: (element: any) => void
}

function SectionLayer({
  section,
  isSelected,
  selectedElementId,
  onSelectSection,
  onSelectElement
}: SectionLayerProps) {
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: isSelected })
  const hoverBg = useColorModeValue('gray.50', 'gray.700')
  const selectedBg = useColorModeValue('blue.50', 'blue.900')

  return (
    <Box>
      {/* Section Header */}
      <Flex
        align="center"
        p={2}
        borderRadius="md"
        cursor="pointer"
        bg={isSelected ? selectedBg : 'transparent'}
        _hover={{ bg: isSelected ? selectedBg : hoverBg }}
        onClick={onSelectSection}
      >
        <IconButton
          aria-label="Toggle section"
          icon={isOpen ? <ChevronDownIcon /> : <ChevronRightIcon />}
          size="xs"
          variant="ghost"
          onClick={(e) => {
            e.stopPropagation()
            onToggle()
          }}
          mr={1}
        />
        
        <Text fontSize="sm" fontWeight="medium" flex="1" noOfLines={1}>
          {section.name}
        </Text>
        
        <Text fontSize="xs" color="gray.500" mr={2}>
          {section.elements.length}
        </Text>
        
        <IconButton
          aria-label="Toggle visibility"
          icon={<ViewIcon />}
          size="xs"
          variant="ghost"
          onClick={(e) => e.stopPropagation()}
        />
      </Flex>
      
      {/* Section Elements */}
      <Collapse in={isOpen}>
        <Box ml={6} mt={1}>
          {section.elements.map((element: any) => (
            <ElementLayer
              key={element.id}
              element={element}
              isSelected={selectedElementId === element.id}
              onSelect={() => onSelectElement(element)}
            />
          ))}
          
          {section.elements.length === 0 && (
            <Text fontSize="xs" color="gray.400" p={2}>
              No elements
            </Text>
          )}
        </Box>
      </Collapse>
    </Box>
  )
}

interface ElementLayerProps {
  element: any
  isSelected: boolean
  onSelect: () => void
}

function ElementLayer({ element, isSelected, onSelect }: ElementLayerProps) {
  const hoverBg = useColorModeValue('gray.50', 'gray.700')
  const selectedBg = useColorModeValue('blue.50', 'blue.900')

  const getElementIcon = (type: string) => {
    switch (type) {
      case 'text':
        return '📝'
      case 'image':
        return '🖼️'
      case 'button':
        return '🔘'
      case 'video':
        return '🎥'
      case 'form':
        return '📋'
      case 'map':
        return '🗺️'
      case 'social':
        return '🔗'
      default:
        return '📦'
    }
  }

  const getElementLabel = (element: any) => {
    switch (element.type) {
      case 'text':
        return element.props.content?.substring(0, 20) + '...' || 'Text'
      case 'image':
        return element.props.alt || 'Image'
      case 'button':
        return element.props.text || 'Button'
      default:
        return element.type.charAt(0).toUpperCase() + element.type.slice(1)
    }
  }

  return (
    <Flex
      align="center"
      p={2}
      borderRadius="md"
      cursor="pointer"
      bg={isSelected ? selectedBg : 'transparent'}
      _hover={{ bg: isSelected ? selectedBg : hoverBg }}
      onClick={onSelect}
      fontSize="sm"
    >
      <Text mr={2}>{getElementIcon(element.type)}</Text>
      
      <Text flex="1" noOfLines={1}>
        {getElementLabel(element)}
      </Text>
      
      <IconButton
        aria-label="Toggle visibility"
        icon={<ViewIcon />}
        size="xs"
        variant="ghost"
        onClick={(e) => e.stopPropagation()}
      />
    </Flex>
  )
}
