import React from "react";
import { text } from "@storybook/addon-knobs";
import { NavigationBar } from "./NavigationBar";
import { Stack } from "../Stack/Stack";
import { Button } from "../Button";

export default {
  title: "Components/NavigationBar",
};

export const Playground = () => {
  const dataTest = text("dataTest", "test");
  return (
    <div style={{ height: "1000px" }}>
      <NavigationBar
        // onMenuOpen={action("onMenuOpen") }
        // onShow={action("onShow")}
        // onHide={action("onHide")}
        dataTest={dataTest}
      >
        <Stack flex align="center" justify="between" spacing="none">
          <Stack direction="row" spacing="tight" shrink>
            <h1>LOGO</h1>
          </Stack>
          {/*
          <LinkList direction="row">
          */}
          {/*
            <TextLink type="secondary">Flights</TextLink>
            <TextLink type="secondary">Flights</TextLink>
            <TextLink type="secondary">Flights</TextLink>
            <TextLink type="secondary">Flights</TextLink>
          */}
          {/* </LinkList> */}
          <Stack direction="row" spacing="tight" justify="end" shrink>
            <Button color="primary">New Website</Button>
            <Button color="secondary">Account</Button>
          </Stack>
        </Stack>
      </NavigationBar>
    </div>
  );
};
