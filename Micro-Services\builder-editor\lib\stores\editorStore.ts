import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// Types based on Old Builder analysis
export interface Element {
  id: string
  type: 'text' | 'image' | 'video' | 'button' | 'form' | 'map' | 'social' | 'custom'
  props: Record<string, any>
  style: Record<string, any>
  children?: Element[]
  parentId?: string
}

export interface Section {
  id: string
  type: 'hero' | 'feature' | 'testimonial' | 'contact' | 'gallery' | 'custom'
  name: string
  elements: Element[]
  style: Record<string, any>
  responsive: {
    desktop: Record<string, any>
    tablet: Record<string, any>
    mobile: Record<string, any>
  }
}

export interface Page {
  id: string
  name: string
  slug: string
  sections: Section[]
  seoSettings: {
    title: string
    description: string
    keywords: string[]
  }
  settings: Record<string, any>
}

export interface EditorAction {
  type: 'add' | 'update' | 'delete' | 'move'
  target: 'section' | 'element'
  data: any
  timestamp: number
}

export type Breakpoint = 'desktop' | 'tablet' | 'mobile'

interface EditorStore {
  // Current state
  currentPage: Page | null
  selectedElement: Element | null
  selectedSection: Section | null
  
  // Editor state
  isPreviewMode: boolean
  currentBreakpoint: Breakpoint
  isDragging: boolean
  
  // History
  undoStack: EditorAction[]
  redoStack: EditorAction[]
  
  // Actions
  setCurrentPage: (page: Page) => void
  selectElement: (element: Element | null) => void
  selectSection: (section: Section | null) => void
  
  // Element operations
  addElement: (element: Element, sectionId: string) => void
  updateElement: (id: string, props: Partial<Element>) => void
  deleteElement: (id: string) => void
  moveElement: (elementId: string, newParentId: string, index: number) => void
  
  // Section operations
  addSection: (section: Section, index?: number) => void
  updateSection: (id: string, props: Partial<Section>) => void
  deleteSection: (id: string) => void
  moveSection: (sectionId: string, newIndex: number) => void
  
  // Editor controls
  setPreviewMode: (isPreview: boolean) => void
  setBreakpoint: (breakpoint: Breakpoint) => void
  setDragging: (isDragging: boolean) => void
  
  // History operations
  undo: () => void
  redo: () => void
  addToHistory: (action: EditorAction) => void
  
  // Utility
  getElementById: (id: string) => Element | null
  getSectionById: (id: string) => Section | null
}

export const useEditorStore = create<EditorStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentPage: null,
      selectedElement: null,
      selectedSection: null,
      isPreviewMode: false,
      currentBreakpoint: 'desktop',
      isDragging: false,
      undoStack: [],
      redoStack: [],

      // Basic setters
      setCurrentPage: (page) => set({ currentPage: page }),
      selectElement: (element) => set({ selectedElement: element }),
      selectSection: (section) => set({ selectedSection: section }),

      // Element operations
      addElement: (element, sectionId) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const section = newPage.sections.find(s => s.id === sectionId)
        if (section) {
          section.elements.push(element)
          set({ currentPage: newPage })
          
          // Add to history
          get().addToHistory({
            type: 'add',
            target: 'element',
            data: { element, sectionId },
            timestamp: Date.now()
          })
        }
      },

      updateElement: (id, props) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const element = get().getElementById(id)
        if (element) {
          Object.assign(element, props)
          set({ currentPage: newPage })
          
          // Add to history
          get().addToHistory({
            type: 'update',
            target: 'element',
            data: { id, props },
            timestamp: Date.now()
          })
        }
      },

      deleteElement: (id) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        for (const section of newPage.sections) {
          const index = section.elements.findIndex(e => e.id === id)
          if (index !== -1) {
            const deletedElement = section.elements.splice(index, 1)[0]
            set({ 
              currentPage: newPage,
              selectedElement: state.selectedElement?.id === id ? null : state.selectedElement
            })
            
            // Add to history
            get().addToHistory({
              type: 'delete',
              target: 'element',
              data: { element: deletedElement, sectionId: section.id },
              timestamp: Date.now()
            })
            break
          }
        }
      },

      moveElement: (elementId, newParentId, index) => {
        // Implementation for moving elements between sections
        const state = get()
        if (!state.currentPage) return
        
        // Add to history
        get().addToHistory({
          type: 'move',
          target: 'element',
          data: { elementId, newParentId, index },
          timestamp: Date.now()
        })
      },

      // Section operations
      addSection: (section, index) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        if (index !== undefined) {
          newPage.sections.splice(index, 0, section)
        } else {
          newPage.sections.push(section)
        }
        
        set({ currentPage: newPage })
        
        // Add to history
        get().addToHistory({
          type: 'add',
          target: 'section',
          data: { section, index },
          timestamp: Date.now()
        })
      },

      updateSection: (id, props) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const section = newPage.sections.find(s => s.id === id)
        if (section) {
          Object.assign(section, props)
          set({ currentPage: newPage })
          
          // Add to history
          get().addToHistory({
            type: 'update',
            target: 'section',
            data: { id, props },
            timestamp: Date.now()
          })
        }
      },

      deleteSection: (id) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const index = newPage.sections.findIndex(s => s.id === id)
        if (index !== -1) {
          const deletedSection = newPage.sections.splice(index, 1)[0]
          set({ 
            currentPage: newPage,
            selectedSection: state.selectedSection?.id === id ? null : state.selectedSection
          })
          
          // Add to history
          get().addToHistory({
            type: 'delete',
            target: 'section',
            data: { section: deletedSection, index },
            timestamp: Date.now()
          })
        }
      },

      moveSection: (sectionId, newIndex) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const currentIndex = newPage.sections.findIndex(s => s.id === sectionId)
        if (currentIndex !== -1) {
          const section = newPage.sections.splice(currentIndex, 1)[0]
          newPage.sections.splice(newIndex, 0, section)
          set({ currentPage: newPage })
          
          // Add to history
          get().addToHistory({
            type: 'move',
            target: 'section',
            data: { sectionId, currentIndex, newIndex },
            timestamp: Date.now()
          })
        }
      },

      // Editor controls
      setPreviewMode: (isPreview) => set({ isPreviewMode: isPreview }),
      setBreakpoint: (breakpoint) => set({ currentBreakpoint: breakpoint }),
      setDragging: (isDragging) => set({ isDragging }),

      // History operations
      undo: () => {
        const state = get()
        if (state.undoStack.length === 0) return

        const action = state.undoStack[state.undoStack.length - 1]
        const newUndoStack = state.undoStack.slice(0, -1)
        const newRedoStack = [...state.redoStack, action]

        // Reverse the action
        // Implementation depends on action type
        
        set({
          undoStack: newUndoStack,
          redoStack: newRedoStack
        })
      },

      redo: () => {
        const state = get()
        if (state.redoStack.length === 0) return

        const action = state.redoStack[state.redoStack.length - 1]
        const newRedoStack = state.redoStack.slice(0, -1)
        const newUndoStack = [...state.undoStack, action]

        // Reapply the action
        // Implementation depends on action type
        
        set({
          undoStack: newUndoStack,
          redoStack: newRedoStack
        })
      },

      addToHistory: (action) => {
        const state = get()
        const newUndoStack = [...state.undoStack, action]
        
        // Limit history size
        if (newUndoStack.length > 50) {
          newUndoStack.shift()
        }
        
        set({
          undoStack: newUndoStack,
          redoStack: [] // Clear redo stack when new action is added
        })
      },

      // Utility functions
      getElementById: (id) => {
        const state = get()
        if (!state.currentPage) return null

        for (const section of state.currentPage.sections) {
          const element = section.elements.find(e => e.id === id)
          if (element) return element
        }
        return null
      },

      getSectionById: (id) => {
        const state = get()
        if (!state.currentPage) return null

        return state.currentPage.sections.find(s => s.id === id) || null
      }
    }),
    {
      name: 'editor-store'
    }
  )
)
