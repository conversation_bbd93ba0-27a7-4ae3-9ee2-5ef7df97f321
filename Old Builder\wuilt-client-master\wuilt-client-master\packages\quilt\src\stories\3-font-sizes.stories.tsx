import React from "react";
import { base } from "../themes/original";
import { DataTable } from "../components/DataTable";
import { Box } from "../components/Box";
import { Heading } from "../components/Heading";

function Display({ size }) {
  return <Heading fontSize={size}>Quilt is design system for Wuilt</Heading>;
}

export const FontSizes = () => {
  return (
    <Box margin="0 auto" maxWidth="1000px">
      <DataTable
        header={{
          cells: [
            { content: "Fontsize" },
            { content: "Value" },
            { content: "Display" },
          ],
        }}
        rows={Object.keys(base.fontSize).map((f) => ({
          cells: [
            { content: f },
            { content: base.fontSize[f] },
            { content: <Display size={f} /> },
          ],
        }))}
      />
    </Box>
  );
};

FontSizes.title = "Intro/Guide";

FontSizes.story = {
  name: "FontSizes",
};

export default FontSizes;
