import { startWidgetApp } from "./app/app";

export enum LocaleEnum {
  en = "en",
  ar = "ar",
  tr = "tr",
}

const currentUrl = window.location.href;

// Create a URL object
const url = new URL(currentUrl);

// Get the pathname
const pathname = url.pathname;

// Check if the pathname contains "en" or "tr"
let locale;
if (pathname.includes("/en/") || pathname.includes("/tr/")) {
  // Split the pathname using '/'
  const parts = pathname.split("/");
  // Get the language code (assuming it's the second part of the pathname)
  locale = parts[1];
} else {
  locale = "ar";
}

startWidgetApp(locale as LocaleEnum);
