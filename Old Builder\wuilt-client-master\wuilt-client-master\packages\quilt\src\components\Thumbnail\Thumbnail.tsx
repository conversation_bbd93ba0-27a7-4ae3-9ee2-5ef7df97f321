import React from "react";
import styled from "styled-components";
import { rtl } from "../../utils";

type SizeType = "xsmall" | "small" | "medium";
const sizes = {
  xsmall: 25,
  small: 40,
  medium: 60,
};

export interface ThumbnailProps {
  children?: React.ReactNode;
  size?: SizeType;
  src?: string | null;
  dataTest?: string;
  padding?: string;
  customBadge?: React.ReactNode | null;
  badgeText?: string | number | null;
}

const Thumbnail: React.FC<ThumbnailProps> = ({
  src,
  dataTest,
  size = "medium",
  padding = "0",
  customBadge,
  badgeText,
  children,
}) => {
  return (
    <Container size={size} padding={padding}>
      {src && <Image data-test={dataTest} src={src} />}
      {customBadge && <StyledBadge>{customBadge}</StyledBadge>}
      {badgeText && <StyledText>{badgeText}</StyledText>}
      {children}
    </Container>
  );
};

Thumbnail.displayName = "Thumbnail";
export { Thumbnail };

/**
 *
 * Styles
 *
 */

const Container = styled.div<{ size: SizeType; padding?: string }>`
  width: ${(props) => sizes[props.size]}px;
  height: ${(props) => sizes[props.size]}px;
  min-width: ${(props) => sizes[props.size]}px;
  min-height: ${(props) => sizes[props.size]}px;
  padding: ${({ padding }) => padding};
  box-sizing: border-box;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  /* overflow: hidden; */
  position: relative;
`;

const Image = styled.img`
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  border: 1px solid rgba(63, 63, 68, 0.15);
  border-radius: 2px;
`;

const StyledBadge = styled.span`
  position: absolute;
  z-index: 10;
  top: 0;
  ${rtl("left:0", "right:0")};
  ${rtl("transform:translate(-50%,-50%)", "transform:translate(50%,-50%)")};
`;

const StyledText = styled(StyledBadge)`
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #5f738c;
  color: #fff;

  height: 16px;
  min-width: 16px;
  width: fit-content;
  padding: 4px;

  font-size: 12px;
  text-align: center;
  border-radius: 50%;
`;
