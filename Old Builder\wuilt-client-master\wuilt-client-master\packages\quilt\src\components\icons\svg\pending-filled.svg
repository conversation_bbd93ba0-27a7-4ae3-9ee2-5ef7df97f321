<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20">
  <defs>
    <circle id="payment-pending-a" cx="10" cy="10" r="7"/>
    <mask id="payment-pending-b" width="14" height="14" x="0" y="0" fill="#fff" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox">
      <use xlink:href="#payment-pending-a"/>
    </mask>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <circle cx="10" cy="10" r="10" fill="#FFC48B"/>
    <use stroke="#C05717" stroke-dasharray="3" stroke-width="4" mask="url(#payment-pending-b)" xlink:href="#payment-pending-a"/>
  </g>
</svg>
