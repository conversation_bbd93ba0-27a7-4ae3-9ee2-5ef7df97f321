import React from "react";
import { TextArea } from "./TextArea";
import { Field, Form } from "../Form";

export default {
  title: "Components/Form/TextArea",
  component: TextArea,
};

export const Playground = () => {
  return (
    <Form onSubmit={() => {}}>
      {({ formProps }) => (
        <form {...formProps}>
          <Field name="textarea">
            {({ fieldProps }) => (
              <TextArea
                {...fieldProps}
                rows={2}
                maxLength={150}
                placeholder="Tell the world what makes your store special"
              >
                This is a textarea
              </TextArea>
            )}
          </Field>
        </form>
      )}
    </Form>
  );
};
