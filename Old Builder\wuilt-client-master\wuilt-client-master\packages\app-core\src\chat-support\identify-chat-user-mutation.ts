import {
  ApolloClient,
  InMemoryCache,
  MutationHookOptions,
  createHttpLink,
  from,
  gql,
} from "@apollo/client";
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};
const defaultOptions = {} as const;
export type IdentifyChatMutationVariables = Exact<{ [key: string]: never }>;

export type IdentifyChatMutation = {
  __typename?: "Mutation";
  identifyChat: {
    __typename?: "ChatIdentificationPayload";
    email: string;
    token: string;
  };
};

export const IdentifyChatDocument = gql`
  mutation IdentifyChat {
    identifyChat {
      email
      token
    }
  }
`;

const httpLink = createHttpLink({
  uri: "https://graphql.wuilt.com",
  credentials: "include",
});

const client = new ApolloClient({
  link: from([httpLink]),
  cache: new InMemoryCache({}),
});

export function identifyChatMutation(
  baseOptions?: MutationHookOptions<
    IdentifyChatMutation,
    IdentifyChatMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return client.mutate<IdentifyChatMutation, IdentifyChatMutationVariables>({
    mutation: IdentifyChatDocument,
    ...options,
  });
}
