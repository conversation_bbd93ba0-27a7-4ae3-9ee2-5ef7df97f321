'use client'

import {
  Box,
  VStack,
  Text,
  Input,
  Textarea,
  FormControl,
  FormLabel,
  Divider,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  useColorModeValue
} from '@chakra-ui/react'
import { Page, useEditorStore } from '../../../lib/stores/editorStore'

interface PagePropertiesProps {
  page: Page
}

export function PageProperties({ page }: PagePropertiesProps) {
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const { setCurrentPage } = useEditorStore()

  const handlePageUpdate = (updates: Partial<Page>) => {
    const updatedPage = { ...page, ...updates }
    setCurrentPage(updatedPage)
  }

  const handleSeoUpdate = (key: string, value: any) => {
    handlePageUpdate({
      seoSettings: {
        ...page.seoSettings,
        [key]: value
      }
    })
  }

  const handleSettingsUpdate = (key: string, value: any) => {
    handlePageUpdate({
      settings: {
        ...page.settings,
        [key]: value
      }
    })
  }

  return (
    <Box p={4}>
      <VStack spacing={4} align="stretch">
        {/* Page Header */}
        <Box>
          <Text fontSize="sm" fontWeight="semibold" color="gray.700" mb={1}>
            Page Settings
          </Text>
          <Text fontSize="xs" color="gray.500">
            Configure page properties and SEO
          </Text>
        </Box>

        <Divider />

        {/* Basic Page Info */}
        <VStack spacing={3} align="stretch">
          <FormControl>
            <FormLabel fontSize="sm">Page Name</FormLabel>
            <Input
              value={page.name}
              onChange={(e) => handlePageUpdate({ name: e.target.value })}
              size="sm"
              placeholder="Page name"
            />
          </FormControl>
          
          <FormControl>
            <FormLabel fontSize="sm">Page Slug</FormLabel>
            <Input
              value={page.slug}
              onChange={(e) => handlePageUpdate({ slug: e.target.value })}
              size="sm"
              placeholder="page-url-slug"
            />
          </FormControl>
        </VStack>

        <Divider />

        {/* Page Properties */}
        <Accordion allowToggle defaultIndex={[0]}>
          <AccordionItem border="none">
            <AccordionButton px={0}>
              <Box flex="1" textAlign="left">
                <Text fontSize="sm" fontWeight="medium">SEO Settings</Text>
              </Box>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel px={0} pb={4}>
              <VStack spacing={3} align="stretch">
                <FormControl>
                  <FormLabel fontSize="sm">Page Title</FormLabel>
                  <Input
                    value={page.seoSettings.title}
                    onChange={(e) => handleSeoUpdate('title', e.target.value)}
                    size="sm"
                    placeholder="Page title for search engines"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Meta Description</FormLabel>
                  <Textarea
                    value={page.seoSettings.description}
                    onChange={(e) => handleSeoUpdate('description', e.target.value)}
                    size="sm"
                    rows={3}
                    placeholder="Brief description of the page content"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Keywords</FormLabel>
                  <Input
                    value={page.seoSettings.keywords.join(', ')}
                    onChange={(e) => handleSeoUpdate('keywords', e.target.value.split(', ').filter(k => k.trim()))}
                    size="sm"
                    placeholder="keyword1, keyword2, keyword3"
                  />
                </FormControl>
              </VStack>
            </AccordionPanel>
          </AccordionItem>

          <AccordionItem border="none">
            <AccordionButton px={0}>
              <Box flex="1" textAlign="left">
                <Text fontSize="sm" fontWeight="medium">Page Settings</Text>
              </Box>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel px={0} pb={4}>
              <VStack spacing={3} align="stretch">
                <FormControl>
                  <FormLabel fontSize="sm">Custom CSS</FormLabel>
                  <Textarea
                    value={page.settings.customCSS || ''}
                    onChange={(e) => handleSettingsUpdate('customCSS', e.target.value)}
                    size="sm"
                    rows={4}
                    placeholder="/* Custom CSS for this page */"
                    fontFamily="monospace"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Custom JavaScript</FormLabel>
                  <Textarea
                    value={page.settings.customJS || ''}
                    onChange={(e) => handleSettingsUpdate('customJS', e.target.value)}
                    size="sm"
                    rows={4}
                    placeholder="// Custom JavaScript for this page"
                    fontFamily="monospace"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Header Code</FormLabel>
                  <Textarea
                    value={page.settings.headerCode || ''}
                    onChange={(e) => handleSettingsUpdate('headerCode', e.target.value)}
                    size="sm"
                    rows={3}
                    placeholder="<!-- Code to inject in <head> -->"
                    fontFamily="monospace"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Footer Code</FormLabel>
                  <Textarea
                    value={page.settings.footerCode || ''}
                    onChange={(e) => handleSettingsUpdate('footerCode', e.target.value)}
                    size="sm"
                    rows={3}
                    placeholder="<!-- Code to inject before </body> -->"
                    fontFamily="monospace"
                  />
                </FormControl>
              </VStack>
            </AccordionPanel>
          </AccordionItem>

          <AccordionItem border="none">
            <AccordionButton px={0}>
              <Box flex="1" textAlign="left">
                <Text fontSize="sm" fontWeight="medium">Analytics & Tracking</Text>
              </Box>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel px={0} pb={4}>
              <VStack spacing={3} align="stretch">
                <FormControl>
                  <FormLabel fontSize="sm">Google Analytics ID</FormLabel>
                  <Input
                    value={page.settings.googleAnalyticsId || ''}
                    onChange={(e) => handleSettingsUpdate('googleAnalyticsId', e.target.value)}
                    size="sm"
                    placeholder="GA-XXXXXXXXX-X"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Facebook Pixel ID</FormLabel>
                  <Input
                    value={page.settings.facebookPixelId || ''}
                    onChange={(e) => handleSettingsUpdate('facebookPixelId', e.target.value)}
                    size="sm"
                    placeholder="Facebook Pixel ID"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Custom Tracking Code</FormLabel>
                  <Textarea
                    value={page.settings.trackingCode || ''}
                    onChange={(e) => handleSettingsUpdate('trackingCode', e.target.value)}
                    size="sm"
                    rows={3}
                    placeholder="<!-- Custom tracking code -->"
                    fontFamily="monospace"
                  />
                </FormControl>
              </VStack>
            </AccordionPanel>
          </AccordionItem>
        </Accordion>

        <Divider />

        {/* Page Stats */}
        <Box>
          <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={2}>
            Page Statistics
          </Text>
          <VStack spacing={1} align="stretch">
            <Text fontSize="xs" color="gray.500">
              Sections: {page.sections.length}
            </Text>
            <Text fontSize="xs" color="gray.500">
              Total Elements: {page.sections.reduce((total, section) => total + section.elements.length, 0)}
            </Text>
            <Text fontSize="xs" color="gray.500">
              Page ID: {page.id}
            </Text>
          </VStack>
        </Box>
      </VStack>
    </Box>
  )
}
