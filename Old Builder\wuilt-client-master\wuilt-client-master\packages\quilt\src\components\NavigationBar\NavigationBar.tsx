import React from "react";
import styled, { css } from "styled-components";

import { EditIcon as <PERSON><PERSON><PERSON><PERSON>burg<PERSON> } from "../icons";
import { Button } from "../Button";
import { useStateWithCallback } from "../../hooks/useStateWithCallback";
import mq from "../../utils/mediaQuery";

const NAVBAR_HEIGHT = { MOBILE: 52, DESKTOP: 64 };

const StyledNavigationBarContent = styled.div`
  display: block;
  width: 100%;
`;

const StyledNavigationBar = styled.nav<{ shown: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: ${NAVBAR_HEIGHT.MOBILE}px; // TODO: create token
  width: 100%;
  display: flex;
  align-items: center;
  background: ${({ theme }) => theme.palette.white.normal};
  padding: ${({ theme }) => theme.base.space.sm};
  box-shadow: ${({ theme }) => theme.base.boxShadow.fixed};
  box-sizing: border-box;
  z-index: 700;
  transform: translate3d(
    0,
    ${({ shown }) => (shown ? "0" : `-${NAVBAR_HEIGHT.MOBILE}px`)},
    0
  );
  ${mq.tablet(css<{ shown }>`
    height: ${NAVBAR_HEIGHT.DESKTOP}px; // TODO: create token
    transform: translate3d(
      0,
      ${
        /* @ts-ignore  */
        ({ shown }) => (shown ? "0" : `-${NAVBAR_HEIGHT.DESKTOP}px`)
      },
      0
    );
  `)};
`;

interface Props {
  readonly children?;
  readonly onMenuOpen?;
  readonly dataTest?;
  readonly onShow?;
  readonly onHide?;
}

const NavigationBar: React.FC<Props> = ({
  onMenuOpen,
  children,
  dataTest,
  onShow,
  onHide,
}) => {
  const resolveCallback = React.useCallback(
    (state) => {
      if (onHide && !state) onHide();
      if (onShow && state) onShow();
    },
    [onHide, onShow]
  );
  const [shown, setShown] = useStateWithCallback<boolean>(
    true,
    resolveCallback
  );

  const [prevScrollPosition, setPrevScrollPosition] = React.useState(0);

  const handleNavigationBarPosition = React.useCallback(() => {
    const currentScrollPosition =
      window.scrollY ||
      window.pageYOffset ||
      (document.documentElement && document.documentElement.scrollTop);

    if (
      prevScrollPosition < currentScrollPosition &&
      currentScrollPosition > NAVBAR_HEIGHT.DESKTOP
    ) {
      setShown(false);
    } else {
      setShown(true);
    }

    setPrevScrollPosition(currentScrollPosition);
  }, [prevScrollPosition, setShown]);

  React.useEffect(() => {
    window.addEventListener("scroll", handleNavigationBarPosition);
    return () => {
      window.removeEventListener("scroll", handleNavigationBarPosition);
    };
  });

  return (
    <StyledNavigationBar data-test={dataTest} shown={shown}>
      <StyledNavigationBarContent>{children}</StyledNavigationBarContent>
      {onMenuOpen && (
        <Button
          color="secondary"
          onClick={onMenuOpen}
          prefixIcon={<MenuHamburger />}
        />
      )}
    </StyledNavigationBar>
  );
};

export { NavigationBar };
