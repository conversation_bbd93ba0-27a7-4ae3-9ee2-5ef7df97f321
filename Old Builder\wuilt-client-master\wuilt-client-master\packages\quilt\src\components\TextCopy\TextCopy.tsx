import React from "react";
import styled, { useTheme } from "styled-components";
import { useCopy, useMediaQuery } from "../../hooks";
import { Button } from "../Button";
import { CopyIcon } from "../icons";
import { Text, TextProps } from "../Text";
import { Box } from "../Box";

export interface TextCopyProps extends Omit<TextProps, "children"> {
  value: string;
  copyText: React.ReactNode;
  copiedText: React.ReactNode;
}

export const TextCopy: React.FC<TextCopyProps> = ({
  value = "Default Text Copy",
  copyText = null,
  copiedText = null,
  ...textProps
}) => {
  const [isCopied, handleCopy] = useCopy();
  // @ts-ignore
  const { rtl: isRTL } = useTheme();
  const isTablet = useMediaQuery.isTablet();
  return (
    <Box
      display="flex"
      alignItems="center"
      justifyContent="space-between"
      overflow="hidden"
      border="1px solid #DDE2E4"
      p={isRTL ? "0 10px 0 0" : "0 0 0 10px"}
      borderRadius="6px"
      width="100%"
    >
      <Box width={!isTablet ? "90%" : "75%"} overflow="hidden">
        <Text fontSize="medium" {...textProps}>
          {value}
        </Text>
      </Box>
      <Box width={!isTablet ? "10%" : "25%"} color="primary">
        <StyledButton
          dataTest="button-copy-link"
          borderLeft={!isRTL ? "1px solid #DDE2E4" : ""}
          borderRight={isRTL ? "1px solid #DDE2E4" : ""}
          onlyIcon
          transparent
          onClick={() => handleCopy(value)}
        >
          {!isCopied ? (
            <div>
              <CopyIcon /> {copyText}
            </div>
          ) : (
            copiedText
          )}
        </StyledButton>
      </Box>
    </Box>
  );
};

const StyledButton = styled(Button)<{
  borderLeft?: string;
  borderRight?: string;
}>`
  width: 100%;
  border-radius: 0;
  border: 0;
  padding: 0 10px !important;
  border-left: ${({ borderLeft }) => borderLeft};
  border-right: ${({ borderRight }) => borderRight};
  color: inherit;
  :hover,
  :focus {
    border-left: ${({ borderLeft }) => borderLeft};
    border-right: ${({ borderRight }) => borderRight};
    box-shadow: none;
    color: inherit;
  }
`;
