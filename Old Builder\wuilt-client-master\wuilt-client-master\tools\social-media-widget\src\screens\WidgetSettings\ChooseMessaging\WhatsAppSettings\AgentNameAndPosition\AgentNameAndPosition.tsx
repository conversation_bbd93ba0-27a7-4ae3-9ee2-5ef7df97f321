import { InputField, Stack, Text } from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";

function AgentNameAndPosition({ agent, updateAgentName, updateAgentPosition }) {
  return (
    <Stack my="10px" direction="row">
      <Stack width="100%" spacing="tight">
        <Text fontWeight="semiBold" color="#1D2939">
          <FormattedMessage defaultMessage="Agent name" id="ctcA0c" />
        </Text>
        <InputField
          borderRadius="8px"
          height="40px"
          value={agent?.name}
          onBlur={updateAgentName}
        />
      </Stack>

      <Stack width="100%" spacing="tight">
        <Text fontWeight="semiBold" color="#1D2939">
          <FormattedMessage defaultMessage="Agent position" id="Hjo1sG" />
        </Text>
        <InputField
          borderRadius="8px"
          height="40px"
          value={agent?.position}
          onBlur={updateAgentPosition}
        />
      </Stack>
    </Stack>
  );
}

export default AgentNameAndPosition;
