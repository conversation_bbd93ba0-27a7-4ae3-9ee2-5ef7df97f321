import React from "react";
import { boolean, select, text } from "@storybook/addon-knobs";
import { DIRECTIONS, ALIGNS, SPACINGS, JUSTIFY } from "./consts";
import { Button } from "../Button";
import { SPACINGS_AFTER } from "../../common/getSpacingToken";
import { Stack, FlexDirection } from "./Stack";
import { EditIcon } from "../icons";

export default {
  title: "Components/Stack",
  component: Stack,
};

export const Default = () => (
  <Stack>
    <Button color="primary" prefixIcon={<EditIcon size="xl" color="warning" />}>
      Button
    </Button>
    <Button color="secondary">Button</Button>
  </Stack>
);

export const Properties = () => {
  const inline = boolean("Inline", false);
  const direction = select<FlexDirection | undefined>(
    "Direction",
    [undefined, ...Object.values(DIRECTIONS)] as FlexDirection[],
    undefined
  );
  const wrap = boolean("Wrap", false);
  const grow = boolean("Grow", true);
  const shrink = boolean("Shrink", false);
  const basis = text("Basis", "auto");
  const align = select(
    "Align",
    [undefined, ...Object.values(ALIGNS)] as any,
    undefined
  );
  const justify = select(
    "Justify",
    [undefined, ...Object.values(JUSTIFY)] as any,
    undefined
  );
  const spacing = select(
    "Spacing",
    [undefined, ...Object.values(SPACINGS)] as any,
    undefined
  );
  const spaceAfter = select(
    "spaceAfter",
    [undefined, ...Object.values(SPACINGS_AFTER)] as any,
    undefined
  );

  return (
    <Stack
      inline={inline}
      direction={direction}
      wrap={wrap}
      shrink={shrink}
      grow={grow}
      basis={basis}
      align={align}
      justify={justify}
      spacing={spacing}
      spaceAfter={spaceAfter}
    >
      <Button color="primary" prefixIcon={<EditIcon />}>
        Button
      </Button>
      <Button color="secondary">Button</Button>
    </Stack>
  );
};
