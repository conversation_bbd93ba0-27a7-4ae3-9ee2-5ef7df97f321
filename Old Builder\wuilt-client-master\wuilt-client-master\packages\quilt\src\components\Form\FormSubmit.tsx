import React from "react";
import { Button, ButtonProps } from "../Button/Button";
import { FormStateContext } from "./Form";

export interface FormSubmitProps extends ButtonProps {
  noDirtyCheck?: boolean;
}

const FormSubmit: React.FC<FormSubmitProps> = ({
  children,
  disabled,
  noDirtyCheck,
  loading,
  ...restProps
}) => {
  const isDisabled = React.useContext(FormStateContext).isDisabled || disabled;
  const { isSubmitting, isDirty } = React.useContext(FormStateContext);
  return (
    <Button
      {...restProps}
      submit
      disabled={isDisabled || (!isDirty && !noDirtyCheck)}
      loading={(isSubmitting && !noDirtyCheck) || loading}
    >
      {children}
    </Button>
  );
};

FormSubmit.displayName = "FormSubmit";
export { FormSubmit };
