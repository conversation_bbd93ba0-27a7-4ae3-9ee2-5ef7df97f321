import React from "react";
import { Stack } from "../Stack";
import { FileUpload as FileUploadComponent } from "./FileUpload";
import { UploadZone } from "./UploadZone";

export default {
  title: "Components/FileUpload ",
  component: FileUploadComponent,
};

export const FileUpload = () => {
  return <FileUploadComponent />;
};

const validImageTypes = ["image/gif", "image/jpeg", "image/png"];

export const DropZone = () => {
  const [files, setFiles] = React.useState<File[]>([]);
  const fileUpload = !files.length && (
    <FileUploadComponent
      actionText="Upload"
      formatsNote="Formats: .jpg and .png"
      note="Or drop file to upload"
    />
  );
  const uploadedFiles = files.length > 0 && (
    <Stack direction="row" wrap>
      {files.map((file, index) => (
        <Stack justify="center" key={index}>
          <img
            style={{ width: 90 }}
            alt="Not found"
            src={
              validImageTypes.indexOf(file.type) > 0
                ? window.URL.createObjectURL(file)
                : "https://cdn.shopify.com/s/files/1/0757/9955/files/New_Post.png?12678548500147524304"
            }
          />

          <div>
            {file.name} {file.size} bytes
          </div>
        </Stack>
      ))}
    </Stack>
  );

  const handleDropZoneDrop = React.useCallback(
    (_dropFiles, acceptedFiles) =>
      setFiles((files) => [...files, ...acceptedFiles]),
    []
  );

  return (
    <UploadZone dropOnPage onDrop={handleDropZoneDrop}>
      {fileUpload}
      {uploadedFiles}
    </UploadZone>
  );
};
