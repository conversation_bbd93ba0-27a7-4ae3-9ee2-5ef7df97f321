import React from "react";
import whiteListProps from "./whiteListProps";
import { Illustration, IllustrationProps } from "./Illustration";

const createIllustration = (
  def: React.ReactNode,
  viewBox: string,
  displayName: string
) => {
  const illustration: React.FC<IllustrationProps> = (
    props: IllustrationProps
  ) => (
    <Illustration viewBox={viewBox} {...whiteListProps(props)}>
      {def}
    </Illustration>
  );
  illustration.displayName = displayName;
  return illustration;
};

export default createIllustration;
