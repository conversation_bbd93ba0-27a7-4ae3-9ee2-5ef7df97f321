import React, { useState } from "react";
import { FlashBar } from "./FlashBar";
import { Button } from "../Button";
import { WuiltLogo } from "../WuiltLogo";
import { Text } from "../Text";

export default {
  title: "Components/FlashBar",
  components: FlashBar,
};

export const Playground = () => {
  const [isVisible, setIsVisible] = useState(true);

  return (
    <>
      <Button onClick={() => setIsVisible((prev) => !prev)}>Toggle</Button>
      <FlashBar
        isVisible={isVisible}
        logo={<WuiltLogo size={45} isLoading={false} />}
        primaryButton={<Button>Save</Button>}
        secondaryButton={<Button color="white">Discard</Button>}
        description={<Text fontSize="medium">Unsaved Changes</Text>}
        onClose={() => {}}
      />
    </>
  );
};
