{"name": "social-media-widget", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "tools/social-media-widget/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/tools/social-media-widget", "index": "tools/social-media-widget/src/index.html", "baseHref": "/", "main": "tools/social-media-widget/src/main.ts", "tsConfig": "tools/social-media-widget/tsconfig.app.json", "assets": ["tools/social-media-widget/src/favicon.ico", "tools/social-media-widget/src/assets"], "styles": [], "scripts": [], "isolatedConfig": true}, "configurations": {"development": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "webpackConfig": "tools/social-media-widget/webpack.dev.config.js"}, "production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "webpackConfig": "tools/social-media-widget/webpack.config.js"}}}, "serve": {"executor": "@nrwl/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "social-media-widget:build", "open": true, "port": 4000, "hmr": true}, "configurations": {"development": {"buildTarget": "social-media-widget:build:development"}, "production": {"buildTarget": "social-media-widget:build:production", "hmr": false}}}, "lint": {"executor": "@nrwl/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["tools/social-media-widget/**/*.{ts,tsx,js,jsx}"]}}, "extract-messages": {"executor": "nx:run-commands", "options": {"commands": ["formatjs extract tools/social-media-widget/src/**/*.{ts,tsx} --format simple --out-file tools/social-media-widget/src/translations/extracted-messages.json --id-interpolation-pattern '[sha512:contenthash:base64:6]'"], "parallel": false}}, "sync-accent": {"executor": "nx:run-commands", "options": {"commands": ["accent sync"], "cwd": "tools/social-media-widget/"}, "dependsOn": ["extract-messages"]}}, "tags": []}