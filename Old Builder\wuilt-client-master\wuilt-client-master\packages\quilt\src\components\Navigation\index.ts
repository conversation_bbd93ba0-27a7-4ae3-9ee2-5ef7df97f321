import { Navigation as NavigationComponent } from "./Navigation";
import { Section, SectionProps } from "./Section";
import { Item, ItemProps } from "./Item";
import { ButtonPrimitiveProps } from "../Button/primitive/ButtonPrimitive.types";

// @ts-ignore
const Navigation: React.FC<{
  children: React.ReactNode;
}> & {
  Section: React.FC<SectionProps>;
  Item: React.FC<ItemProps & ButtonPrimitiveProps>;
} = NavigationComponent;
Navigation.Section = Section;
Navigation.Item = Item;

export { Navigation };
