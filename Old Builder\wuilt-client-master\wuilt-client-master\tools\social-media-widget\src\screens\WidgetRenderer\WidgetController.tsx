import React from "react";
import { AnimationEnum, TWidgetSettings } from "../../shared/types";
import styled from "styled-components";
import { CloseIcon, WidgetAppStyles } from "./utils";
import { ButtonIcon } from "../../shared/IconsList";
import { AnimationMapper, AnimationBasisStyle } from "../../shared/animation";

interface WidgetControllerProps {
  settings: TWidgetSettings;
  isOpen: boolean;
  onToggle: () => void;
}

const WidgetController: React.FC<WidgetControllerProps> = ({
  settings,
  isOpen,
  onToggle,
}) => {
  if (settings.apps.length === 1) return null;
  const withText = settings?.appearance?.content?.withText;
  const closeText = settings?.appearance?.content?.closeText;
  const openText = settings?.appearance?.content?.openText;
  const text = isOpen ? closeText : openText;
  const size = settings?.appearance?.style?.size;
  const color = settings?.appearance?.style?.background?.color;
  const animationName = settings?.appearance?.style?.animation;
  const OpenIcon = ButtonIcon[settings?.appearance?.content?.icon];
  return (
    <StyledWidgetController
      animationName={animationName}
      size={size}
      color={color}
      isOpen={isOpen}
      settings={settings}
      onClick={onToggle}
    >
      <StyledStack>
        <StyledIcon isOpen={isOpen}>
          {isOpen ? (
            <CloseIcon size={`calc(${size} / 2)`} />
          ) : (
            <OpenIcon size={`calc(${size} / 2)`} />
          )}
        </StyledIcon>
        {withText && <StyledText fontSize={size}>{text}</StyledText>}
      </StyledStack>
    </StyledWidgetController>
  );
};

export default WidgetController;

const StyledWidgetController = styled.button<{
  animationName: AnimationEnum;
  size: string;
  color: string;
  isOpen: boolean;
  settings: TWidgetSettings;
}>`
  position: relative;
  ${WidgetAppStyles}
  ${AnimationBasisStyle}
  ${({ isOpen, animationName }) => !isOpen && AnimationMapper[animationName]}
`;

const StyledStack = styled.div`
  display: flex;
  align-items: center;
  direction: ltr;
`;

const StyledIcon = styled.div<{ isOpen: boolean }>`
  color: white;
  transition: all 200ms ease-in-out;
  transform: ${({ isOpen }) => (isOpen ? "rotate(360deg)" : "rotate(0deg)")};
`;

const StyledText = styled.div<{ fontSize: string }>`
  font-size: ${({ fontSize }) => `calc(${fontSize} / 3)`};
  font-weight: 500;
  color: white;
  padding: 0 4px;
  line-height: 1;
`;
