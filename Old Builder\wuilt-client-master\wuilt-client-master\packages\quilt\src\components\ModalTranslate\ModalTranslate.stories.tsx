import React from "react";
import { Button } from "../Button";

import { ElementsType, ModalTranslate } from "./ModalTranslate";

export default {
  title: "Components/Modals/ModalTranslate",
  component: ModalTranslate,
};

const Elements: ElementsType = [
  {
    tagName: "input",
    fieldName: "title",
    fieldLabel: "product title",
    baseLangValue: "This is the bas language text",
    transLangValue: "اي كلام",
  },
  {
    tagName: "textArea",
    fieldName: "description",
    fieldLabel: "product description",
    baseLangValue: "the base language textArea",
    transLangValue: "اي كلام",
  },
];

export const Playground = () => {
  return (
    <ModalTranslate
      modalHeader="Arabic version of your product "
      TranslateFrom="English"
      TranslateTo="Arabic"
      saveText="Translate"
      cancelText="Canccel"
      Elements={Elements}
      // eslint-disable-next-line no-console
      onSave={(data) => console.log(data)}
    >
      <Button>Add Language</Button>
    </ModalTranslate>
  );
};
