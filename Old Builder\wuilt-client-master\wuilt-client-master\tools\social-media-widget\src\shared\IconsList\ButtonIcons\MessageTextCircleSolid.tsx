import { TIconType } from "../types";

export function MessageTextCircleSolid({
  size = 20,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.4168 1.66675C6.04453 1.66675 2.50012 5.21116 2.50012 9.58342C2.50012 10.4667 2.64511 11.3179 2.91325 12.1134C2.96041 12.2534 2.98817 12.3362 3.00621 12.3972L3.01212 12.4178L3.00899 12.4243C2.98757 12.4685 2.95574 12.5278 2.89619 12.638L1.51963 15.1859C1.43431 15.3438 1.34529 15.5085 1.28407 15.6517C1.22321 15.794 1.12464 16.0551 1.17314 16.3647C1.23 16.7276 1.44357 17.0471 1.75714 17.2384C2.02464 17.4015 2.30363 17.4103 2.45842 17.4085C2.61414 17.4067 2.80033 17.3874 2.9788 17.3689L7.27461 16.9248C7.34341 16.9177 7.37938 16.9141 7.40573 16.912L7.40912 16.9118L7.41942 16.9154C7.4518 16.9271 7.49571 16.9439 7.5715 16.9732C8.45562 17.3138 9.41543 17.5001 10.4168 17.5001C14.789 17.5001 18.3335 13.9557 18.3335 9.58342C18.3335 5.21116 14.789 1.66675 10.4168 1.66675ZM6.66675 7.08342C6.20651 7.08342 5.83342 7.45651 5.83342 7.91675C5.83342 8.37699 6.20651 8.75008 6.66675 8.75008H10.0001C10.4603 8.75008 10.8334 8.37699 10.8334 7.91675C10.8334 7.45651 10.4603 7.08342 10.0001 7.08342H6.66675ZM6.66675 10.0001C6.20651 10.0001 5.83342 10.3732 5.83342 10.8334C5.83342 11.2937 6.20651 11.6667 6.66675 11.6667H12.5001C12.9603 11.6667 13.3334 11.2937 13.3334 10.8334C13.3334 10.3732 12.9603 10.0001 12.5001 10.0001H6.66675Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
