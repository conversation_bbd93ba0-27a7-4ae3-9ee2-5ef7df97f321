import React from "react";
import { AddUserOutlineIcon, Stack } from "@wuilt/quilt";
import {
  AppsEnum,
  TAppSettings,
  TWidgetSettings,
} from "../../../../shared/types";

interface AddAgentButtonProps {
  apps: TAppSettings[];
  update: (settings: Partial<TWidgetSettings>) => void;
}

function AddAgentButton({ apps, update }: AddAgentButtonProps) {
  function updatedAppsArray(updatedObject) {
    const appsClone = [...apps];
    const index = appsClone.findIndex((obj) => obj.name === AppsEnum.Whatsapp);

    if (index !== -1) {
      // If the object with the given ID is found, replace it
      appsClone[index] = updatedObject;
      return appsClone;
    }
  }

  const addAgent = () => {
    const findWhatsApp = apps.find((obj) => obj.name === AppsEnum.Whatsapp);

    if (findWhatsApp && findWhatsApp.whatsAppSettings) {
      const agents = findWhatsApp.whatsAppSettings.agents;

      const newAgentName = `Agent ${agents.length + 1}`;

      agents.push({
        name: newAgentName,
        position: "",
        message: "",
        image:
          "https://d2pi0n2fm836iz.cloudfront.net/491017/11262023131908656345cca4b87.jpg",
        phone: "",
      });

      const updatedApps = updatedAppsArray(findWhatsApp);
      update({ apps: updatedApps });
    }
  };
  return (
    <Stack
      boxShadow="0px 1px 2px 0px #1018280D"
      justify="center"
      align="center"
      border="1px solid #bac7d5"
      borderRadius="8px"
      bg="white"
      height="40px"
      width="40px"
      cursor="pointer"
      onClick={() => {
        addAgent();
      }}
    >
      <AddUserOutlineIcon
        size="lg"
        viewBox="0 0 20 20"
        className="test"
        customColor="#667085"
      />
    </Stack>
  );
}

export default AddAgentButton;
