import { TIconType } from "../types";

export function MessageDotsCircleSolid({
  size = 20,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.99984 1.66675C5.39746 1.66675 1.6665 5.39771 1.6665 10.0001C1.6665 11.107 1.88277 12.1655 2.27613 13.1341C2.31166 13.2216 2.3321 13.2721 2.34611 13.3094L2.35036 13.3209L2.34984 13.3248C2.34567 13.3556 2.33877 13.3975 2.32543 13.4775L1.82729 16.4664C1.80494 16.6002 1.77993 16.75 1.77011 16.88C1.75941 17.0219 1.75568 17.2475 1.85794 17.4859C1.98437 17.7807 2.21925 18.0155 2.514 18.142C2.75244 18.2442 2.97805 18.2405 3.11987 18.2298C3.24994 18.22 3.39971 18.195 3.53358 18.1726L6.52238 17.6745C6.60241 17.6611 6.64433 17.6542 6.67508 17.6501L6.67904 17.6496L6.69056 17.6538C6.72785 17.6678 6.77836 17.6883 6.86585 17.7238C7.83442 18.1171 8.8929 18.3334 9.99984 18.3334C14.6022 18.3334 18.3332 14.6025 18.3332 10.0001C18.3332 5.39771 14.6022 1.66675 9.99984 1.66675ZM4.99984 10.0001C4.99984 9.30972 5.55948 8.75008 6.24984 8.75008C6.94019 8.75008 7.49984 9.30972 7.49984 10.0001C7.49984 10.6904 6.94019 11.2501 6.24984 11.2501C5.55948 11.2501 4.99984 10.6904 4.99984 10.0001ZM8.74984 10.0001C8.74984 9.30972 9.30948 8.75008 9.99984 8.75008C10.6902 8.75008 11.2498 9.30972 11.2498 10.0001C11.2498 10.6904 10.6902 11.2501 9.99984 11.2501C9.30948 11.2501 8.74984 10.6904 8.74984 10.0001ZM13.7498 8.75008C13.0595 8.75008 12.4998 9.30972 12.4998 10.0001C12.4998 10.6904 13.0595 11.2501 13.7498 11.2501C14.4402 11.2501 14.9998 10.6904 14.9998 10.0001C14.9998 9.30972 14.4402 8.75008 13.7498 8.75008Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
