import { Heading, Modal } from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import { WidgetRenderer } from "../../WidgetRenderer";
import { useWidget } from "../../../context/widget-provider";
import { LocaleEnum } from "../../../main";
interface LivePreviewModalProps {
  show: boolean;
  url: string;
  onClose: () => void;
  locale: LocaleEnum;
}

const LivePreviewModal: React.FC<LivePreviewModalProps> = ({
  show,
  url,
  onClose,
  locale,
}) => {
  const widget = useWidget();

  return (
    <Modal show={show} onClose={onClose}>
      <Modal.Header>
        <Heading>
          <FormattedMessage defaultMessage="Live Preview" id="x5i5IT" />
        </Heading>
      </Modal.Header>
      <Modal.Body position="relative" padding="0">
        <iframe
          src={url}
          style={{
            width: "100%",
            height: "60vh",
            boxSizing: "border-box",
            borderWidth: 0,
            borderStyle: "solid",
          }}
        />
        <WidgetRenderer
          settings={widget.settings}
          maxHeight="500px"
          locale={locale}
        />
      </Modal.Body>
    </Modal>
  );
};

export default LivePreviewModal;
