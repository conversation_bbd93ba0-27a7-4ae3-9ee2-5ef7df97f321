import React from "react";
import { Breadcrumbs } from "./Breadcrumbs";

export default {
  title: "Components/Breadcrumbs",
  component: Breadcrumbs,
};

export const Playground = () => {
  return (
    <>
      <Breadcrumbs
        breadcrumbs={[
          { content: "All Products", url: "#" },
          { content: "Single Product", url: "#" },
        ]}
        urlProp="href"
        linkComponent="a"
        spaceAfter="largest"
      />
      <Breadcrumbs
        breadcrumbs={[{ content: "All Products", url: "#" }]}
        urlProp="href"
        linkComponent="a"
        type="back-button"
      />
    </>
  );
};
