import React, { useState } from "react";
import styled from "styled-components";
import { Navigation } from "./Navigation";
import { Section } from "./Section";
import { Item } from "./Item";
import {
  EditIcon,
  DropSilhouetteIcon,
  EyeIcon,
  PhotoCameraIcon,
  SettingsIcon,
  FontIcon,
} from "../icons";

export default {
  title: "Components/Navigation",
  component: Navigation,
};

export const Playground = () => {
  const [active, setActive] = useState(0);

  return (
    <Navigation>
      <Section title="My Store">
        <Item
          label="Orders"
          isActive={active === 0}
          onClick={() => setActive(0)}
          icon={<PhotoCameraIcon />}
        />
        <Item
          label="Products"
          isActive={active === 1}
          onClick={() => setActive(1)}
          icon={<DropSilhouetteIcon />}
        >
          <Item
            label="Attributes"
            isActive={active === 2}
            onClick={() => setActive(2)}
            contentAlign="left"
            style={{ padding: "0 34px" }}
          />
        </Item>
        <Item
          label="Customers"
          isActive={active === 3}
          onClick={() => setActive(3)}
          icon={<FontIcon />}
        />
      </Section>
      <Section title="Settings">
        <Item
          label="General"
          isActive={active === 4}
          onClick={() => setActive(4)}
          icon={<SettingsIcon />}
          badge={<StyledDiv>GO LIVE</StyledDiv>}
        />
        <Item
          label="Payment"
          isActive={active === 5}
          onClick={() => setActive(5)}
          icon={<EditIcon />}
        />
        <Item
          label="Shipping"
          isActive={active === 6}
          onClick={() => setActive(6)}
          icon={<EyeIcon />}
        />
      </Section>
    </Navigation>
  );
};

const StyledDiv = styled.div`
  width: 60px;
`;
