import { TIconType } from "../types";

export function Skype({ size = 40, width, height, color }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.0519 27.7325C15.7838 27.7325 13.8338 25.5582 13.8338 23.9635C13.8332 23.5831 13.9894 23.2192 14.2655 22.9575C14.5416 22.6958 14.9133 22.5593 15.2932 22.5803C17.1205 22.5803 16.6425 25.3213 20.0519 25.3213C21.7947 25.3213 22.8184 24.2723 22.8184 23.2867C22.8184 22.6945 22.48 22.0177 21.3294 21.747L17.5224 20.7867C14.4641 20.0084 13.9311 18.3079 13.9311 16.7301C13.9311 13.4561 16.9259 12.2717 19.777 12.2717C22.4038 12.2717 25.5256 13.7226 25.5256 15.6853C25.5256 16.5313 24.8192 16.9839 23.9986 16.9839C22.4377 16.9839 22.6999 14.7928 19.5613 14.7928C18.0004 14.7928 17.1797 15.5246 17.1797 16.5482C17.1797 17.5719 18.3938 17.9188 19.4597 18.1514L22.2685 18.7859C25.3479 19.4797 26.1686 21.2859 26.1686 23.016C26.1686 25.6767 24.1085 27.7325 20.0477 27.7325H20.0519ZM31.8368 22.1023C31.9587 21.4039 32.0196 20.6962 32.0187 19.9873C32.0336 16.426 30.4689 13.0415 27.7462 10.7459C25.0236 8.45037 21.4232 7.48009 17.9158 8.09664C16.8377 7.47593 15.615 7.15056 14.371 7.15334C11.8176 7.16894 9.46395 8.53744 8.18725 10.7488C6.91054 12.9602 6.90229 15.6828 8.16555 17.9019C7.46066 21.775 8.69542 25.748 11.4715 28.5393C14.2477 31.3306 18.2139 32.587 22.0908 31.9033C23.1676 32.5234 24.3888 32.8488 25.6314 32.8466C28.1835 32.8301 30.5357 31.4622 31.8121 29.2522C33.0885 27.0421 33.0979 24.3211 31.8368 22.1023Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
