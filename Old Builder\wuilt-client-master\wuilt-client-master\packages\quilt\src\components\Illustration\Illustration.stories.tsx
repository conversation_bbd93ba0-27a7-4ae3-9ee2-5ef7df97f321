import React from "react";
import { useTheme } from "styled-components";
import { select, text } from "@storybook/addon-knobs";
import { Illustration } from "./Illustration";
import IllustrationList from "./IllustrationsList";

export default {
  title: "Components/Illustration",
  component: Illustration,
};

const List = () => {
  const theme: any = useTheme();
  return (
    <IllustrationList
      size={select("size", ["small", "medium", "large"], "medium")}
      customColor={text("color", theme.palette.ink.normal)}
    />
  );
};

export const ListAllIllustrations = () => {
  return <List />;
};
