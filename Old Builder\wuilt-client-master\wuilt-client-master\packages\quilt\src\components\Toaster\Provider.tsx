import React, { useCallback, useReducer } from "react";
import { reducer } from "./reducer";
import { Action, Config } from "./types";
import { Toaster } from "./Toaster";

export interface ProviderProps {
  children: React.ReactNode;
}

export const Context = React.createContext<React.Dispatch<Action> | undefined>(
  undefined
);

export const Provider: React.FC<ProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, {
    items: [],
    config: {},
  });

  const setDefaultConfig = useCallback((config: Config) => {
    dispatch({ type: "setDefaultConfig", config });
  }, []);

  const removeToast = useCallback((toastId: string) => {
    dispatch({ type: "removeToast", toastId });
  }, []);

  return (
    <>
      <Toaster
        items={state.items}
        setDefaultConfig={setDefaultConfig}
        removeToast={removeToast}
      />
      <Context.Provider value={dispatch}>{children}</Context.Provider>
    </>
  );
};

/* <WrappedComponent dispatch={dispatch}>{children}</WrappedComponent> */
// const WrappedComponent = React.memo(({ children, dispatch }: any) => (
//   <Context.Provider value={dispatch}>{children}</Context.Provider>
// ));

// Provider.displayName = "ToasterProvider";
