import React from "react";
import { TIconType } from "../types";

export function Send({ size = 40, width, height, color }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.5 2.5L8.75 11.25"
        stroke={color || "currentColor"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.84495 8.95361C2.34883 8.76067 2.10077 8.66421 2.02835 8.5252C1.96557 8.4047 1.96548 8.26116 2.02812 8.14059C2.10038 8.0015 2.34833 7.90474 2.84422 7.71122L16.9173 2.21927C17.365 2.04458 17.5888 1.95723 17.7318 2.00501C17.856 2.04651 17.9535 2.14398 17.995 2.26819C18.0428 2.41121 17.9554 2.63504 17.7808 3.08269L12.2888 17.1558C12.0953 17.6517 11.9985 17.8997 11.8594 17.9719C11.7389 18.0345 11.5953 18.0345 11.4748 17.9717C11.3358 17.8993 11.2394 17.6512 11.0464 17.1551L8.85633 11.5234C8.81716 11.4227 8.79758 11.3723 8.76734 11.3299C8.74053 11.2924 8.70766 11.2595 8.67008 11.2327C8.62768 11.2024 8.57733 11.1829 8.47662 11.1437L2.84495 8.95361Z"
        stroke={color || "currentColor"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
