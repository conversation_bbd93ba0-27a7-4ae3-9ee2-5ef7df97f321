type SpacingType =
  | "none"
  | "extraTight"
  | "tight"
  | "condensed"
  | "compact"
  | "natural"
  | "comfy"
  | "loose"
  | "extraLoose";

const spacingMap = {
  none: "none",
  extraTight: "xxxs", // 2px
  tight: "xxs", // 4px
  condensed: "xs", // 8px
  compact: "sm", // 12px
  natural: "md", // 16px
  comfy: "lg", // 24px
  loose: "xl", // 32px
  extraLoose: "xxl", // 40px
};
export const spacing =
  (spacing: SpacingType) =>
  ({ theme }) =>
    theme.base.space[spacingMap[spacing]];
