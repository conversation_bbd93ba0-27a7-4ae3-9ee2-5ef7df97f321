import React, { useState } from "react";
import Cropper, { CropperProps, Area } from "react-easy-crop";
import getCroppedImg from "./cropImage";
import { Box } from "../Box";
import { Stack } from "../Stack";
import { Button } from "../Button";
import { RotateIcon, CropIcon } from "../icons";
import { InputRange } from "../InputRange";
import { Select } from "../Select";

const AspectRatiosOptions = [
  { value: 1 / 1, label: "1/1" },
  { value: 4 / 3, label: "4/3" },
  { value: 16 / 9, label: "16/9" },
  { value: 1 / 2, label: "1/2" },
];

export interface CroppedImageProps {
  croppedArea: Area;
  croppedAreaPixels: Area;
  image: string;
  aspectRatio: number;
  rotation: number;
  zoom: number;
  crop: { x: number; y: number };
}

export const CroppedImage: React.FC<CroppedImageProps> = ({
  image,
  croppedArea,
  croppedAreaPixels,
  aspectRatio,
  rotation,
  zoom,
  crop,
}) => {
  const scale = 100 / croppedArea.width;
  const transform = {
    x: `${-croppedArea.x * scale}%`,
    y: `${-croppedArea.y * scale}%`,
    scale,
    width: "calc(100% + 0.5px)",
    height: "auto",
  };

  // console.log({ scale });

  console.log({ zoom });
  console.log({ crop });
  console.log({ croppedArea });
  console.log({ croppedAreaPixels });
  console.log({ x: transform.x, y: transform.y });

  const imageStyle = {
    transform: `translate(${transform.x}, ${transform.y}) scale(${transform.scale},${transform.scale})`,
    // transform: `translate(${crop.x}px, ${crop.y}px) rotate(${rotation}deg) scale(${zoom})`,
    width: transform.width,
    height: transform.height,
    position: "absolute" as any,
    top: "0",
    left: "0",
    right: "0",
    bottom: "0",
    // transformOrigin: "center",
    transformOrigin: "top left",
  };

  return (
    <div
      style={{
        position: "relative",
        width: "300px",
        // height: "300px",
        height: 300 / aspectRatio,
        overflow: "hidden",
        border: "1px solid ",
        // transform: `rotate(${rotation}deg)`,
      }}
    >
      <img src={image} alt="" style={imageStyle} />
    </div>
  );
};

export type ImageCropperCropValue = { x: number; y: number };

export type ImageCropperValueMeta = {
  zoom: number;
  crop: ImageCropperCropValue;
  aspectRatio: number;
  rotation: number;
};

export type ImageCropperValue = {
  meta?: ImageCropperValueMeta;
  originalSrc: string;
  croppedSrc?: string;
};

export interface ImageCropperProps extends CropperProps {
  value: ImageCropperValue;
  onChange: (v: ImageCropperValue) => void;
}

const ImageCropper: React.FC<ImageCropperProps> = ({
  value,
  onChange,
  ...restProps
}) => {
  const initials: ImageCropperValueMeta = {
    aspectRatio: value?.meta?.aspectRatio || AspectRatiosOptions[0].value,
    zoom: value?.meta?.zoom || 1,
    crop: value?.meta?.crop || { x: 0, y: 0 },
    rotation: value?.meta?.rotation || 0,
  };
  const [zoom, setZoom] = useState(initials.zoom);
  const [crop, setCrop] = useState(initials.crop);
  const [rotation, setRotation] = useState(initials.rotation);
  const [aspectRatio, setAspectRatio] = useState(initials.aspectRatio);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area>();
  const [croppedArea, setCroppedArea] = useState<Area>();

  // const onAspectChange = (e) => {
  //   const value = e.target.value;
  //   const ratio = aspectRatios.find((ratio) => ratio.value == value);
  //   setAspectRatio(ratio);
  // };

  const onCropComplete = (croppedArea: Area, croppedAreaPixels: Area) => {
    setCroppedAreaPixels(croppedAreaPixels);
    setCroppedArea(croppedArea);
  };

  // const onCrop = async () => {
  //   const croppedImageUrl = await getCroppedImg(
  //     value.originalSrc,
  //     croppedAreaPixels,
  //     rotation
  //   );
  //   console.log(croppedImageUrl);
  // };

  const onResetImage = () => {
    // resetImage(id);
  };

  return (
    <Stack>
      <Box
        position="relative"
        // width={300}
        height="50vh"
        // height={300}
        // height={300 / aspectRatio}
        // height="auto"
        // style={{ aspectRatio: String(aspectRatio) }}
        // border="3px solid "
        // overflow="hidden"
      >
        <Cropper
          image={value?.originalSrc}
          // zoomSpeed={5}
          // maxZoom={5}
          // zoom={zoom}
          // crop={crop}
          // aspect={aspectRatio}
          // rotation={rotation}
          // onCropChange={setCrop}
          onZoomChange={setZoom}
          onCropAreaChange={onCropComplete}
          onCropComplete={onCropComplete}
          // onRotationChange={setRotation}
          // disableAutomaticStylesInjection
          // cropSize={{ height: 300, width: 300 }}
          // cropShape='rect'
          // showGrid={false}
          // restrictPosition={false}
          // style={{
          //   mediaStyle: {
          //     // overflow: "hidden",
          //     // position: "relative",
          //     // height: 300,
          //     // width: 300,
          //     // border: "3px solid ",
          //   },
          // }}
          // mediaProps={{
          //   height: 300,
          //   width: 300,
          // }}
          {...restProps}
        />
      </Box>

      <Stack>
        <Select
          options={AspectRatiosOptions}
          value={AspectRatiosOptions.find((i) => i.value === aspectRatio)}
          onChange={(o) => setAspectRatio(((o as any)?.value as number) || 1)}
        />

        <Stack direction="row">
          <CropIcon />
          <InputRange
            value={zoom}
            onChange={setZoom}
            min={1}
            step={0.03}
            max={3}
          />
        </Stack>
        <Stack direction="row">
          <RotateIcon size="xl" />
          <InputRange
            value={rotation}
            onChange={setRotation}
            min={-180}
            max={180}
          />
        </Stack>
      </Stack>

      <Box display="flex">
        {croppedArea && (
          <CroppedImage
            croppedAreaPixels={
              croppedAreaPixels || {
                x: 0,
                y: 0,
                width: 0,
                height: 0,
              }
            }
            croppedArea={croppedArea}
            aspectRatio={aspectRatio}
            image={value?.originalSrc}
            rotation={rotation}
            zoom={zoom}
            crop={crop}
          />
        )}
      </Box>
    </Stack>
  );
};

export { ImageCropper };
