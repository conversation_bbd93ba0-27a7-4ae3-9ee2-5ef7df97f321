import React from "react";
import { TWidgetSettings } from "../../../shared/types";
import {
  Box,
  Button,
  Card,
  CheckIcon,
  CopyIcon,
  Heading,
  Modal,
  SourceCodeIcon,
  Stack,
  Text,
  useCopy,
} from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import { track } from "@amplitude/analytics-browser";

interface GetButtonModalProps {
  show: boolean;
  settings: TWidgetSettings;
  onClose: () => void;
}

const GetButtonModal: React.FC<GetButtonModalProps> = ({
  show,
  settings,
  onClose,
}) => {
  const [copied, copy] = useCopy();

  // eslint-disable-next-line prettier/prettier, prettier/prettier, prettier/prettier
  const code = `<script>function startWuiltWidget(){wuilt.initWidget(${JSON.stringify(
    settings
  )})}</script><script src="https://buttons.wuilt.com/runtime.js" type="module"></script><script src="https://buttons.wuilt.com/widget.js" type="module" onload="startWuiltWidget()"></script>`;

  const onCopy = () => {
    copy(code);
    track("chat widget created");
  };

  return (
    <Modal modalWidth="medium" show={show} onClose={onClose}>
      <Modal.Body>
        <Stack spacing="comfy">
          <Box
            width="48px"
            height="48px"
            padding="12px"
            borderRadius="10px"
            border="1px solid #EAECF0"
            boxShadow="xxxs"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <SourceCodeIcon size="xl" />
          </Box>
          <Stack spacing="tight">
            <Heading fontSize="md2">
              <FormattedMessage defaultMessage="Get your Button" id="en8NdU" />
            </Heading>
            <Text>
              <FormattedMessage
                defaultMessage="you can place this code before the \<\/body> tag on your website."
                id="xqWtnX"
              />
            </Text>
          </Stack>
          <Card style={{ direction: "ltr" }}>
            <Card.Body overflowY="scroll" height="200px">
              <Text wordBreak="break-all">{code}</Text>
            </Card.Body>
          </Card>

          <Button
            fullWidth
            contentAlign="center"
            contentWidth="auto"
            prefixIcon={!copied && <CopyIcon size="xl" />}
            suffixIcon={
              copied && (
                <Stack direction="row" spacing="tight" align="center">
                  <Text color="white" fontSize="md">
                    <FormattedMessage defaultMessage="Copied" id="p556q3" />
                  </Text>
                  <CheckIcon size="xxl" />
                </Stack>
              )
            }
            onClick={onCopy}
          >
            {!copied && (
              <FormattedMessage
                defaultMessage="Copy to clipboard"
                id="aCdAsI"
              />
            )}
          </Button>
        </Stack>
      </Modal.Body>
    </Modal>
  );
};

export default GetButtonModal;
