import without from "lodash/without";
import first from "lodash/first";
import last from "lodash/last";
import { find as findFocusable } from "./focusable";

/**
 * Returns the tab index of the given element. In contrast with the tabIndex
 * property, this normalizes the default (0) to avoid browser inconsistencies,
 * operating under the assumption that this function is only ever called with a
 * focusable node.
 *
 * @see https://bugzilla.mozilla.org/show_bug.cgi?id=1190261
 */
function getTabIndex(element: Element): number {
  const tabIndex = element.getAttribute("tabindex");
  return tabIndex === null ? 0 : parseInt(tabIndex, 10);
}

/**
 * Returns true if the specified element is tabbable, or false otherwise.
 */
export function isTabbableIndex(element: Element): boolean {
  return getTabIndex(element) !== -1;
}

/**
 * Returns a stateful reducer function which constructs a filtered array of
 * tabbable elements, where at most one radio input is selected for a given
 * name, giving priority to checked input, falling back to the first
 * encountered.
 */
// eslint-disable-next-line @typescript-eslint/ban-types
function createStatefulCollapseRadioGroup(): Function {
  const CHOSEN_RADIO_BY_NAME = {};

  return function collapseRadioGroup(result, element) {
    const { nodeName, type, checked, name } = element;

    // For all non-radio tabbables, construct to array by concatenating.
    if (nodeName !== "INPUT" || type !== "radio" || !name) {
      return result.concat(element);
    }

    const hasChosen = Object.prototype.hasOwnProperty.call(
      CHOSEN_RADIO_BY_NAME,
      name
    );

    // Omit by skipping concatenation if the radio element is not chosen.
    const isChosen = checked || !hasChosen;
    if (!isChosen) {
      return result;
    }

    // At this point, if there had been a chosen element, the current
    // element is checked and should take priority. Retroactively remove
    // the element which had previously been considered the chosen one.
    if (hasChosen) {
      const hadChosenElement = CHOSEN_RADIO_BY_NAME[name];
      result = without(result, hadChosenElement);
    }

    CHOSEN_RADIO_BY_NAME[name] = element;

    return result.concat(element);
  };
}

/**
 * An array map callback, returning an object with the element value and its
 * array index location as properties. This is used to emulate a proper stable
 * sort where equal tabIndex should be left in order of their occurrence in the
 * document.
 *
 * @param {Element} element Element.
 * @param {number}  index   Array index of element.
 *
 * @return {Object} Mapped object with element, index.
 */
function mapElementToObjectTabbable(element, index) {
  return { element, index };
}

/**
 * An array map callback, returning an element of the given mapped object's
 * element value.
 *
 * @param {Object} object Mapped object with index.
 *
 * @return {Element} Mapped object element.
 */
function mapObjectTabbableToElement(object) {
  return object.element;
}

/**
 * A sort comparator function used in comparing two objects of mapped elements.
 *
 * @see mapElementToObjectTabbable
 *
 * @param {Object} a First object to compare.
 * @param {Object} b Second object to compare.
 *
 * @return {number} Comparator result.
 */
function compareObjectTabbables(a, b) {
  const aTabIndex = getTabIndex(a.element);
  const bTabIndex = getTabIndex(b.element);

  if (aTabIndex === bTabIndex) {
    return a.index - b.index;
  }

  return aTabIndex - bTabIndex;
}

/**
 * Givin focusable elements, filters out tabbable element.
 *
 * @param {Array} focusables Focusable elements to filter.
 *
 * @return {Array} Tabbable elements.
 */
function filterTabbable(focusables) {
  return focusables
    .filter(isTabbableIndex)
    .map(mapElementToObjectTabbable)
    .sort(compareObjectTabbables)
    .map(mapObjectTabbableToElement)
    .reduce(createStatefulCollapseRadioGroup(), []);
}

export function find(context) {
  return filterTabbable(findFocusable(context));
}

/**
 * Given a focusable element, find the preceding tabbable element.
 */
export function findPrevious(element: Element | null = document.activeElement) {
  const focusables = findFocusable(document.body);
  if (element === null) {
    return;
  }
  const index = focusables.indexOf(element);

  // Remove all focusables after and including `element`.
  focusables.length = index;

  return last(filterTabbable(focusables));
}

/**
 * Given a focusable element, find the next tabbable element.
 *
 * @param {Element} element The focusable element after which to look. Defaults
 *                          to the active element.
 */
export function findNext(element = document.activeElement) {
  const focusables = findFocusable(document.body);
  if (element === null) {
    return;
  }
  const index = focusables.indexOf(element);

  // Remove all focusables before and inside `element`.
  const remaining = focusables
    .slice(index + 1)
    .filter((node) => !element.contains(node));

  return first(filterTabbable(remaining));
}
