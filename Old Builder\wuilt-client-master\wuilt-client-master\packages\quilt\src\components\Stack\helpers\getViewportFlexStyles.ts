import { css } from "styled-components";
import getJustify from "./getJustify";
import getDirection from "./getDirection";
import getWidth from "./getWidth";
import getDisplay from "./getDisplay";
import getShrink from "./getShrink";
import getWrap from "./getWrap";
import getGrow from "./getGrow";
import getAlign from "./getAlign";
import getBasis from "./getBasis";
import getStackCSS from "./getStackCSS";

const getViewportFlexStyles = (viewport) => (props) => {
  const { inline, direction, wrap, grow, shrink, basis, justify, align, flex } =
    props[viewport];

  const cssProps = { ...props[viewport], theme: props.theme };

  return css`
    display: ${getDisplay(inline)};
    flex-direction: ${getDirection(direction)};
    flex-wrap: ${getWrap(wrap)};
    flex-grow: ${getGrow(grow)};
    flex-shrink: ${getShrink(shrink)};
    flex-basis: ${getBasis(basis)};
    justify-content: ${getJustify(justify)};
    align-content: ${getAlign(align)};
    align-items: ${getAlign(align)};
    ${flex && `flex: ${flex};`}
    width: ${getWidth(grow)};
    ${getStackCSS(cssProps)}
  `;
};

export default getViewportFlexStyles;
