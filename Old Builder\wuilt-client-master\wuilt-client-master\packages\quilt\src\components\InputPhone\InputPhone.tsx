import React, { useEffect, useState } from "react";
import styled from "styled-components";
import ReactPhoneInput, {
  isPossiblePhoneNumber,
} from "react-phone-number-input";
import type { CountryCode, E164Number } from "libphonenumber-js/types";
import { FieldMeta } from "../Form";
import * as utils from "../../utils";

import ar from "react-phone-number-input/locale/ar.json";
import tr from "react-phone-number-input/locale/tr.json";
import fr from "react-phone-number-input/locale/fr.json";

const LanguageMapper = {
  // default is "en" so no need to be handled
  ar,
  fr,
  tr,
};

export type TPhoneInputValue =
  | {
      value: E164Number;
      isValid: boolean;
    }
  | undefined;

export interface InputPhoneProps {
  value?: TPhoneInputValue;
  onChange?: (value: TPhoneInputValue) => void;
  meta?: FieldMeta;
  dataTest?: string;
  className?: string;
  language?: string;
}

const InputPhone: React.FC<InputPhoneProps> = ({
  value,
  meta,
  dataTest,
  className,
  onChange,
  language,
}) => {
  const [defaultCountry, setDefaultCountry] = useState<CountryCode | undefined>(
    "EG"
  );

  useEffect(() => {
    if (!defaultCountry) {
      fetch("https://ipinfo.io/json")
        .then((res) => res.json())
        .then((res) => {
          setDefaultCountry(res?.country ?? undefined);
        })
        .catch(() => {
          setDefaultCountry("EG");
        });
    }
  }, [defaultCountry]);

  const labels = LanguageMapper[language!];

  return (
    <StyledReactPhoneInput
      className={`${className} ${
        meta?.error && meta?.touched ? "invalid" : ""
      }`}
      labels={labels}
      data-test={dataTest}
      countryOptionsOrder={["EG", "SA", "AE", "|", "..."]}
      international={true}
      defaultCountry={defaultCountry ?? "EG"}
      value={value?.value}
      placeholder="Enter your phone number"
      onChange={(value: string) => {
        onChange?.({
          value,
          isValid: value ? isPossiblePhoneNumber(value) : false,
        });
      }}
    />
  );
};

export { InputPhone };

const StyledReactPhoneInput = styled(ReactPhoneInput)`
  margin-top: 0.375rem;
  width: 100%;
  position: relative;
  direction: ltr;
  .PhoneInputCountry {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    margin-inline-start: 10px;
  }

  .PhoneInputInput {
    padding-inline-start: 55px;
  }

  .PhoneInputCountrySelectArrow {
    margin-inline-start: 8px;
    color: ${utils.color("product")}90;
    border-bottom-width: 2px;
    border-right-width: 2px;
    width: 0.4em;
    height: 0.4em;
    opacity: 1;
  }

  &.invalid > input {
    border: 2px solid #d21c1c;
  }

  input {
    flex: 1 0 auto;
    background: ${utils.color("white")};
    width: 100%;
    font-size: 16px;
    border: 2px solid ${utils.color("cloud", "darkest")};
    border-radius: 0.25rem;
    outline: none;
    padding: 0.5rem 0.75rem;
    font-weight: 500;
    &::placeholder {
      color: ${utils.color("ink", "light")};
      opacity: 0.5;
    }
    &::-webkit-input-placeholder {
      color: ${utils.color("ink", "light")};
      opacity: 0.5;
    }
    &:-moz-placeholder {
      color: ${utils.color("ink", "light")};
      opacity: 0.5;
    }
    &:-ms-input-placeholder {
      color: ${utils.color("ink", "light")};
      opacity: 0.5;
    }
    &:focus {
      border: 2px solid ${utils.color("product")}90;
    }
    @media (min-width: 768px) {
      font-size: 14px;
    }
  }
`;

/**
 *
 *
 */

export function createDefaultPhoneInput(
  number: string | undefined | null
): TPhoneInputValue {
  if (!number) return undefined;
  return { value: number, isValid: true };
}
