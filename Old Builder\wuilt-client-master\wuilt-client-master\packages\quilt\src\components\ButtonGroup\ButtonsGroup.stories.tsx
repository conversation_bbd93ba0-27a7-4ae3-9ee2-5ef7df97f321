import React from "react";
import { ButtonsGroup } from "./ButtonsGroup";
import { Button } from "../Button/Button";
import {
  TrashIcon,
  EditIcon,
  AddIcon,
  DragHandleIcon,
  SettingsGeneralIcon,
  MoreHorizIcon,
} from "../icons";
import { Stack } from "../Stack";
import { Tooltip } from "../Tooltip";
import { ButtonIcon } from "../ButtonIcon";

export default {
  title: "Components/Buttons/ButtonsGroup",
  component: ButtonsGroup,
};
export const Playground = () => {
  const flag = false;
  return (
    <Stack spacing="extraLoose">
      <ButtonsGroup>
        <Button padding="8px 14px" color="grey">
          <AddIcon size="xl" color="info" />
        </Button>
        <Button padding="8px 14px" color="white">
          <TrashIcon size="xl" color="secondary" />
        </Button>
        <Button padding="8px 14px">
          <EditIcon size="xl" color="white" />
        </Button>
      </ButtonsGroup>
      <ButtonsGroup>
        <Tooltip content="Move column">
          <ButtonIcon color="white" stopOpacity>
            <DragHandleIcon />
          </ButtonIcon>
        </Tooltip>

        <Tooltip content="Column settings">
          <ButtonIcon color="white" stopOpacity>
            <SettingsGeneralIcon />
          </ButtonIcon>
        </Tooltip>

        <Tooltip content="Actions">
          <ButtonIcon color="white" stopOpacity>
            <MoreHorizIcon />
          </ButtonIcon>
        </Tooltip>
      </ButtonsGroup>
      <ButtonsGroup>
        <Tooltip content="Move column">
          <ButtonIcon size="small" color="white" stopOpacity>
            <DragHandleIcon />
          </ButtonIcon>
        </Tooltip>

        <Tooltip content="Column settings">
          <ButtonIcon size="small" color="white" stopOpacity>
            <SettingsGeneralIcon />
          </ButtonIcon>
        </Tooltip>

        <Tooltip content="Actions">
          <ButtonIcon size="small" color="white" stopOpacity>
            <MoreHorizIcon />
          </ButtonIcon>
        </Tooltip>
      </ButtonsGroup>
      {/* One button styles => for conditional rendering */}
      <ButtonsGroup>
        <Tooltip content="Move column">
          <ButtonIcon size="small" color="white" stopOpacity>
            <DragHandleIcon />
          </ButtonIcon>
        </Tooltip>
        {flag && (
          <Tooltip content="Actions">
            <ButtonIcon size="small" color="white" stopOpacity>
              <MoreHorizIcon />
            </ButtonIcon>
          </Tooltip>
        )}
      </ButtonsGroup>
    </Stack>
  );
};
