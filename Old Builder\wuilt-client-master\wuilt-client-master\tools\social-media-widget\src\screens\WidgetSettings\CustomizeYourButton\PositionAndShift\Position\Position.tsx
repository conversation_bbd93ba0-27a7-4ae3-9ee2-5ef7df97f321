import {
  Box,
  PositionLeftIcon,
  PositionRightIcon,
  RadioButton,
  RadioGroup,
  Stack,
  Text,
} from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import {
  TWidgetAppearance,
  TWidgetSettings,
  WidgetPositionEnum,
} from "../../../../../shared/types";
interface PositionProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function Position({ appearance, update }: PositionProps) {
  return (
    <Box width="100%">
      <Text style={{ color: "#1D2939" }} fontSize="sm" fontWeight="medium">
        <FormattedMessage defaultMessage="Position" id="U6qGuO" />
      </Text>
      <Box mt="6px">
        <RadioGroup
          stackProps={{
            inline: false,
            largeMobile: { justify: "between" },
            height: "40px",
            align: "center",
          }}
          defaultValue={appearance.display.position}
          onChange={(value: WidgetPositionEnum) => {
            update({
              appearance: {
                ...appearance,
                display: { ...appearance.display, position: value },
              },
            });
          }}
          alignment="horizontal"
        >
          <Stack width="100%">
            <RadioButton
              label={
                <Stack spacing="condensed" align="center" direction="row">
                  <PositionLeftIcon
                    className="position-and-shift-icon"
                    color="transparent"
                  />
                  <Text
                    style={{ color: "#1D2939" }}
                    fontSize="sm"
                    fontWeight="medium"
                  >
                    <FormattedMessage defaultMessage="Left" id="lJEnpw" />
                  </Text>
                </Stack>
              }
              value={WidgetPositionEnum.Left}
            />
          </Stack>
          <Stack width="100%">
            <RadioButton
              label={
                <Stack spacing="condensed" align="center" direction="row">
                  <PositionRightIcon
                    className="position-and-shift-icon"
                    color="transparent"
                  />
                  <Text
                    style={{ color: "#1D2939" }}
                    fontSize="sm"
                    fontWeight="medium"
                  >
                    <FormattedMessage defaultMessage="Right" id="gAnLDP" />
                  </Text>
                </Stack>
              }
              value={WidgetPositionEnum.Right}
            />
          </Stack>
        </RadioGroup>
      </Box>
    </Box>
  );
}

export default Position;
