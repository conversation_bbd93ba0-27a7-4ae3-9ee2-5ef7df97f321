import React from "react";
import {
  TWidgetAppearance,
  TWidgetSettings,
} from "../../../../../shared/types";
import { Box, Stack, Text, ToggleButton } from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
interface GradientAndTransparentProps {
  appearance: TWidgetAppearance;
  update: (settings: Partial<TWidgetSettings>) => void;
}
function GradientAndTransparent({
  appearance,
  update,
}: GradientAndTransparentProps) {
  const gradient = appearance?.style?.background?.gradient;
  const transparent = appearance?.style?.transparent;
  return (
    <Box mt="16px">
      <Stack spacing="condensed" align="Start" justify="start" direction="row">
        <Box>
          <ToggleButton
            activeColor="#0E9384"
            disableColor="#F2F4F7"
            sliderBoxShadow="0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A"
            value={gradient}
            onChange={(value) => {
              update({
                appearance: {
                  ...appearance,
                  style: {
                    ...appearance.style,
                    background: {
                      ...appearance.style.background,
                      gradient: value,
                    },
                  },
                },
              });
            }}
            hideIcons
          />
        </Box>
        <Stack spacing="none">
          <Text fontSize="sm" fontWeight="medium" style={{ color: "#1D2939" }}>
            <FormattedMessage defaultMessage="Gradient" id="y/FCma" />
          </Text>
          <Text>
            <FormattedMessage
              defaultMessage="Apply gradient effect on all buttons"
              id="PRCFwI"
            />
          </Text>
        </Stack>
      </Stack>
      <Stack
        mt="16px"
        spacing="condensed"
        align="Start"
        justify="start"
        direction="row"
      >
        <Box>
          <ToggleButton
            activeColor="#0E9384"
            disableColor="#F2F4F7"
            sliderBoxShadow="0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A"
            hideIcons
            value={transparent}
            onChange={(value) => {
              update({
                appearance: {
                  ...appearance,
                  style: {
                    ...appearance.style,
                    transparent: value,
                  },
                },
              });
            }}
          />
        </Box>
        <Stack spacing="none">
          <Text fontSize="sm" fontWeight="medium" style={{ color: "#1D2939" }}>
            <FormattedMessage defaultMessage="Transparent" id="dF3WgL" />
          </Text>
          <Text>
            <FormattedMessage
              defaultMessage="Reduce button transparency to 60%"
              id="Tx6E4F"
            />
          </Text>
        </Stack>
      </Stack>
    </Box>
  );
}

export default GradientAndTransparent;
