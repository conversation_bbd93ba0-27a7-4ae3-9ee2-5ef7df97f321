{"extends": ["../../.eslintrc.js"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {}}, {"files": ["*.ts", "*.tsx"], "rules": {}}, {"files": ["*.js", "*.jsx"], "rules": {}}, {"files": ["./src/screens/WidgetRenderer/*"], "rules": {"no-restricted-imports": ["error", {"paths": [{"name": "@wuilt/quilt", "message": "Cannot use Quilt in the widget renderer"}, {"name": "react-intl", "message": "Cannot use react-intl in the widget renderer"}, {"name": "react-router-dom", "message": "Cannot use react-router-dom in the widget renderer"}]}]}}]}