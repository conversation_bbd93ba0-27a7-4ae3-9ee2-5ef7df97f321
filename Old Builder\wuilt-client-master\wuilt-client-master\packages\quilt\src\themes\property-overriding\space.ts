import {
  system,
  SpaceProps as SystemProps,
  space as SystemSpace,
  compose,
} from "styled-system";
import { theme } from "../theme";

const SpaceAfterValues = {
  none: 0,
  smallest: theme.base.space.xxs, // 4px
  small: theme.base.space.xs, // 8px
  normal: theme.base.space.sm, // 10px
  medium: theme.base.space.md, // 16px
  large: theme.base.space.lg, // 24px
  largest: theme.base.space.xl, // 32px
};

const OverrideSpace = system({
  spaceAfter: {
    property: "marginBottom",
    transform: (value) => SpaceAfterValues[value],
  },
});

export const Space = compose(SystemSpace, OverrideSpace);

export type SpaceProps = SystemProps & {
  spaceAfter?: keyof typeof SpaceAfterValues;
};
