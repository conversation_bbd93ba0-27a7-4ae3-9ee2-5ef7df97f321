import React from "react";
import { text, object } from "@storybook/addon-knobs";
import { InputGroup } from "./InputGroup";
import { InputField } from "../InputField/InputField";
import { Form, Field } from "../Form";
import { Select } from "../Select";

export default {
  title: "Components/Form/InputGroup",
  component: InputGroup,
};

export const Playground = () => {
  return (
    <Form onSubmit={() => {}}>
      {({ formProps }) => (
        <form {...formProps}>
          <InputGroup ignoreOnMobile>
            <Field
              label="Input Field"
              name="field1"
              defaultValue="This is The First Field"
            >
              {({ fieldProps }) => <InputField {...fieldProps} />}
            </Field>

            <Field
              label="Input Field"
              name="field2"
              defaultValue="This is The Second Field"
            >
              {({ fieldProps }) => <InputField {...fieldProps} />}
            </Field>

            <Field
              label="Select Field"
              name="field2"
              defaultValue="This is The Second Field"
            >
              {() => (
                <Select
                  id={text("id", "Availability", "Component")}
                  // label={text("label", "Availability", "Component")}
                  options={object(
                    "options",
                    [
                      { value: "0", label: "Available" },
                      { value: "1", label: "Not Available" },
                      { value: "2", label: "Disabled", disabled: true },
                    ],
                    "Component"
                  )}
                  value={object(
                    "selectedItem",
                    { value: "1", label: "Not Available" },
                    "Component"
                  )}
                />
              )}
            </Field>

            <Field
              label="Select Field"
              name="field2"
              defaultValue="This is The Second Field"
            >
              {() => (
                <Select
                  id={text("id", "Availability", "Component")}
                  // label={text("label", "Availability", "Component")}
                  options={object(
                    "options",
                    [
                      { value: "0", label: "Available" },
                      { value: "1", label: "Not Available" },
                      { value: "2", label: "Disabled", disabled: true },
                    ],
                    "Component"
                  )}
                  value={object(
                    "selectedItem",
                    { value: "1", label: "Not Available" },
                    "Component"
                  )}
                />
              )}
            </Field>

            {/* <Field
              label="Input Field"
              name="field2"
              defaultValue="This is The Second Field"
            >
              {({ fieldProps }) => <InputField {...fieldProps} />}
            </Field> */}

            {/* <Field
              label="Input Field"
              name="field2"
              defaultValue="This is The Second Field"
            >
              {({ fieldProps }) => <InputField {...fieldProps} />}
            </Field> */}
          </InputGroup>
        </form>
      )}
    </Form>
  );
};
