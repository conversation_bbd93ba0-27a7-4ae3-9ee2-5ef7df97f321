import React, { Component, ReactNode } from "react";

import FormSectionWrapper, {
  FormSectionDescription,
  FormSectionTitle,
} from "./styled/FormSection";

interface Props {
  /** Section Title */
  title?: ReactNode;
  /** Content or child components to be rendered after description */
  children?: ReactNode;
  /** Sub title or description of this section */
  description?: ReactNode;
}

export class FormSection extends Component<Props> {
  render() {
    const { title, description, children } = this.props;

    return (
      <FormSectionWrapper>
        {title && <FormSectionTitle>{title}</FormSectionTitle>}
        {description && (
          <FormSectionDescription>{description}</FormSectionDescription>
        )}
        {children}
      </FormSectionWrapper>
    );
  }
}
