import React from "react";
import {
  Enable,
  HandleComponent,
  HandleStyles,
  Resizable as ResizableComponent,
  ResizableProps as ResizableComponentProps,
} from "re-resizable";
import { ResizableHandle } from "./ResizableHandle";

export type ResizableValue = { width: number; height: number };

export interface ResizableProps extends ResizableComponentProps {
  className?: string;
  children?: React.ReactNode;
  top?: boolean;
  right?: boolean;
  bottom?: boolean;
  left?: boolean;
  topRight?: boolean;
  bottomRight?: boolean;
  bottomLeft?: boolean;
  topLeft?: boolean;
  all?: boolean;
  hideResizableHandle?: boolean;
  value?: ResizableValue;
  onChange?: (v: ResizableValue) => void;
}

const Resizable = React.forwardRef<any, ResizableProps>((props, ref) => {
  const {
    children,
    top,
    right,
    bottom,
    left,
    topRight,
    bottomRight,
    bottomLeft,
    topLeft,
    all,
    value,
    onChange,
    ...restProps
  } = props;

  return (
    <ResizableComponent
      ref={ref}
      handleComponent={handleComponent(props)}
      handleStyles={handleStyles(props)}
      enable={enable(props)}
      size={value}
      onResizeStop={(event, direction, elementRef, delta) => {
        if (value?.height && value?.width)
          onChange?.({
            height: value.height + delta?.height,
            width: value.width + delta?.width,
          });
      }}
      {...restProps}
    >
      {children}
    </ResizableComponent>
  );
});

export { Resizable };

/**
 * Helpers
 */

function enable(props: ResizableProps): Enable {
  const {
    top,
    right,
    bottom,
    left,
    topRight,
    bottomRight,
    bottomLeft,
    topLeft,
    all,
  } = props;

  return {
    top: all || !!top,
    right: all || !!right,
    bottom: all || !!bottom,
    left: all || !!left,
    topRight: all || !!topRight,
    bottomRight: all || !!bottomRight,
    bottomLeft: all || !!bottomLeft,
    topLeft: all || !!topLeft,
  };
}

function handleComponent(props: ResizableProps): HandleComponent | undefined {
  const { hideResizableHandle } = props;
  if (hideResizableHandle) return undefined;

  return {
    top: <ResizableHandle top />,
    right: <ResizableHandle right />,
    bottom: <ResizableHandle bottom />,
    left: <ResizableHandle left />,
    topRight: <ResizableHandle topRight />,
    bottomRight: <ResizableHandle bottomRight />,
    bottomLeft: <ResizableHandle bottomLeft />,
    topLeft: <ResizableHandle topLeft />,
  };
}

function handleStyles(props: ResizableProps): HandleStyles | undefined {
  const { hideResizableHandle } = props;
  if (hideResizableHandle) return undefined;

  const shared: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  };
  return {
    top: {
      height: "unset",
      width: "100%",
      top: 0,
      bottom: "unset",
      right: "unset",
      left: 0,
      transform: "translate(0,-50%)",
      ...shared,
    },
    right: {
      height: "100%",
      width: "unset",
      top: 0,
      bottom: "unset",
      right: 0,
      left: "unset",
      transform: "translate(50%,0)",
      ...shared,
    },
    bottom: {
      height: "unset",
      width: "100%",
      top: "unset",
      bottom: 0,
      right: "unset",
      left: 0,
      transform: "translate(0,50%)",
      ...shared,
    },
    left: {
      height: "100%",
      width: "unset",
      top: 0,
      bottom: "unset",
      right: "unset",
      left: 0,
      transform: "translate(-50%,0)",
      ...shared,
    },
    topRight: {
      height: "unset",
      width: "unset",
      top: 0,
      bottom: "unset",
      right: 0,
      left: "unset",
      transform: "translate(50%,-50%)",
      ...shared,
    },
    bottomRight: {
      height: "unset",
      width: "unset",
      top: "unset",
      bottom: 0,
      right: 0,
      left: "unset",
      transform: "translate(50%,50%)",
      ...shared,
    },
    bottomLeft: {
      height: "unset",
      width: "unset",
      top: "unset",
      bottom: 0,
      right: "unset",
      left: 0,
      transform: "translate(-50%,50%)",
      ...shared,
    },
    topLeft: {
      height: "unset",
      width: "unset",
      top: 0,
      bottom: "unset",
      right: "unset",
      left: 0,
      transform: "translate(-50%,-50%)",
      ...shared,
    },
  };
}
