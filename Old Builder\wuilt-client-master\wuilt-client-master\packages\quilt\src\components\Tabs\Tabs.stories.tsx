import React from "react";
import { Tabs } from "./Tabs";
import { Card } from "../Card";

export default {
  title: "Components/Tabs",
  component: Tabs,
};

export const Playground = () => {
  const [selected, setSelected] = React.useState({
    id: "all-customers",
    content: "All",
    accessibilityLabel: "All customers",
    panelID: "all-customers-content",
  });

  const handleTabChange = React.useCallback(
    (selectedTabIndex) => setSelected(selectedTabIndex),
    []
  );

  const tabs = [
    {
      id: "all-customers",
      content: "All",
      accessibilityLabel: "All customers",
      panelID: "all-customers-content",
    },
    {
      id: "accepts-marketing",
      content: "Accepts marketing",
      panelID: "accepts-marketing-content",
    },
    {
      id: "repeat-customers",
      content: "Repeat customers",
      panelID: "repeat-customers-content",
    },
    {
      id: "prospects",
      content: "Prospects",
      panelID: "prospects-content",
    },
  ];

  return (
    <Card>
      <Tabs tabs={tabs} selected={selected} onSelect={handleTabChange}>
        <Card.Body>
          <p>Tab &quot;{selected.content}&quot; selected</p>
        </Card.Body>
      </Tabs>
    </Card>
  );
};
