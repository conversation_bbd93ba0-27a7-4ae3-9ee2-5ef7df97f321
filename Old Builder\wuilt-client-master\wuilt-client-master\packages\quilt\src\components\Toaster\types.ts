import React from "react";

export type Toast = {
  id?: string;
  content: React.ReactNode;
  autoDismissTimeout?: number;
  autoDismiss?: boolean;
  appearance?: "success" | "error" | "warning" | "info";
  terminateOtherToasts?: boolean;
  // onDismiss?: (id?: string) => void;
};

export type Action =
  | { type: "addToast"; toast: Toast }
  | { type: "removeToast"; toastId: string }
  | { type: "removeAllToasts" }
  | { type: "updateToast"; toast: Toast }
  | { type: "setDefaultConfig"; config: Config };

export type State = {
  items: Toast[];
  config: Config;
};

export type Config = {
  autoDismissTimeout?: number;
  autoDismiss?: boolean;
  appearance?: "success" | "error" | "warning" | "info";
};
