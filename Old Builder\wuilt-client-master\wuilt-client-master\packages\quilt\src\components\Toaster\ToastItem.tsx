import React, { useEffect } from "react";
import styled from "styled-components";
import { Text } from "../Text";
import { Stack } from "../Stack";
import { ButtonIcon } from "../ButtonIcon";
import { Toast } from "./types";
import {
  CheckFilledIcon,
  CloseIcon,
  DangerIcon,
  QuestionMarkIcon,
  WarningIcon,
} from "../icons";
import { color } from "../../utils";

interface ToastItemProps {
  toast: Toast;
  removeToast: (id: string) => void;
}

const ToastItem: React.FC<ToastItemProps> = ({ toast, removeToast }) => {
  useEffect(() => {
    const handleRemove = () => {
      if (toast.id) removeToast(toast.id);
    };
    if (toast.autoDismiss) {
      setTimeout(handleRemove, toast.autoDismissTimeout);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [toast.autoDismissTimeout, toast.autoDismiss]);
  const appearance = Appearance[toast.appearance || "info"];
  return (
    <StyledToastItem
      data-test={`toast-${toast.appearance}`}
      bgColor={appearance.bgColor}
    >
      <Stack direction="row" align="center" justify="between">
        <Stack direction="row" align="center" inline basis="80%">
          {appearance.icon}
          <Text color="secondary" fontWeight="semiBold">
            {toast.content}
          </Text>
        </Stack>
        <ButtonIcon
          onClick={() => {
            if (toast.id) removeToast(toast.id);
          }}
          onlyIcon
          transparent
        >
          <CloseIcon size="lg" />
        </ButtonIcon>
      </Stack>
    </StyledToastItem>
  );
};

export default ToastItem;

/**
 *
 * Styles
 *
 */

const Appearance = {
  success: {
    icon: <CheckFilledIcon color="primary" size="lg" />,
    bgColor: ["product", "lightHover"],
  },
  warning: {
    icon: <WarningIcon customColor="#eec200" size="xl" />,
    bgColor: ["orange", "lightHover"],
  },
  error: {
    icon: <DangerIcon size="lg" />,
    bgColor: ["red", "lightHover"],
  },
  info: {
    icon: <QuestionMarkIcon color="secondary" size="lg" />,
    bgColor: ["cloud", "darker"],
  },
};

const StyledToastItem = styled.div<{ bgColor: any[] }>`
  width: 300px;
  padding: 16px;
  margin-bottom: 10px;
  background-color: ${({ bgColor }) => color(bgColor[0], bgColor[1])};
  border-radius: 4px;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.2), 0px 2px 10px rgba(0, 0, 0, 0.1);
`;
