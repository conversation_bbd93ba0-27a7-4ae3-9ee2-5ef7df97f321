import React from "react";
import { Box } from "./Box";

export default {
  title: "Components/Box",
  component: Box,
};

export const Playground = () => {
  return (
    <Box
      fontSize="xxl"
      fontWeight="bold"
      textColor="primary"
      bgColor="warning"
      padding="14px 16px"
      align="center"
      boxShadow="lg"
      borderRadius="6px"
      borderTop="1px solid"
      borderBottom="1px solid"
      borderTopColor="danger"
      borderBottomColor="info"
      maxWidth="400px"
      cursor="pointer"
      style={{ textDecoration: "underline" }}
      largeMobile={{
        bgColor: "danger",
        textColor: "grey",
        style: { textDecoration: "none" },
      }}
    >
      Box Component
    </Box>
  );
};
