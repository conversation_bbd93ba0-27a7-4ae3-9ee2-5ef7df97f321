import React from "react";
import { Stack } from "../Stack";
import { Thumbnail } from "./Thumbnail";
import { CrownIcon } from "../icons";

export default {
  title: "Components/Thumbnail",
  component: Thumbnail,
};

export const Playground = () => {
  return (
    <Stack>
      <Thumbnail
        size="medium"
        src="https://images.unsplash.com/photo-1570118990526-e3c274f55a25?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=3934&q=80"
      />

      <Thumbnail size="medium">
        <CrownIcon size="xxxl" title="Taager Integration" />
      </Thumbnail>

      <Thumbnail
        size="medium"
        src="https://images.unsplash.com/photo-1570118990526-e3c274f55a25?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=3934&q=80"
        customBadge={<CrownIcon />}
      />

      <Thumbnail
        size="medium"
        src="https://images.unsplash.com/photo-1570118990526-e3c274f55a25?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=3934&q=80"
        badgeText={6}
      />
    </Stack>
  );
};
