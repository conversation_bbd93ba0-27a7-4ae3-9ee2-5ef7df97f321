import React from "react";
import { <PERSON><PERSON><PERSON><PERSON> } from "./WuiltLogo";

export default {
  title: "Components/WuiltLogo",
  component: Wuilt<PERSON>ogo,
};

export const playground = () => {
  return (
    <>
      <WuiltLogo size={20} isLoading={false} />
      <WuiltLogo size={40} isLoading={false} />
      {/* <WuiltLogo size={50} isLoading={false} /> */}
      <WuiltLogo size={80} isLoading={false} />
      <WuiltLogo size={100} isLoading={false} />
    </>
  );
};
