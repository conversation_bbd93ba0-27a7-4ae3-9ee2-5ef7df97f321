import React, { cloneElement, forwardRef, useRef, useState } from "react";

import {
  FloatingArrow,
  FloatingPortal,
  Placement,
  arrow,
  autoUpdate,
  flip,
  offset,
  useFloating,
  useHover,
  useInteractions,
  useMergeRefs,
  useRole,
} from "@floating-ui/react";

const tooltipStyles: React.CSSProperties = {
  width: "max-content",
  backgroundColor: "#000",
  color: "white",
  fontSize: "lg",
  padding: "4px 8px",
  borderRadius: "4px",
  zIndex: 99999,
};

export interface TooltipProps {
  children: React.ReactElement;
  content: React.ReactNode;
  className?: string;
  maxWidth?: number | string;
  style?: React.CSSProperties;
  placement?: Placement;
  disabled?: boolean;
}

const Tooltip: React.FC<TooltipProps> = forwardRef(
  ({ children, ...props }, refProp) => {
    const [isOpen, setIsOpen] = useState(false);
    const arrowRef = useRef(null);

    const { refs, floatingStyles, context } = useFloating({
      open: isOpen,
      onOpenChange: setIsOpen,
      placement: props.placement || "top",
      whileElementsMounted: autoUpdate,
      middleware: [flip(), offset(5), arrow({ element: arrowRef })],
    });

    const hover = useHover(context, { move: false });
    const role = useRole(context, { role: "tooltip" });

    const { getReferenceProps, getFloatingProps } = useInteractions([
      hover,
      role,
    ]);

    const ref = useMergeRefs([
      refProp,
      refs.setReference,
      (children as any).ref,
    ]);

    const activator = cloneElement(children, { ...getReferenceProps(), ref });
    if (!children) return null;
    if (!props.content || props.disabled) return activator;

    return (
      <>
        {activator}
        <FloatingPortal id="tooltips">
          {isOpen && (
            <div
              className={props.className}
              ref={refs.setFloating}
              style={{
                ...floatingStyles,
                ...tooltipStyles,
                maxWidth: props.maxWidth,
                ...props.style,
              }}
              {...getFloatingProps()}
            >
              <FloatingArrow ref={arrowRef} context={context} />
              {props.content}
            </div>
          )}
        </FloatingPortal>
      </>
    );
  }
);

Tooltip.displayName = "Tooltip";
export { Tooltip };
