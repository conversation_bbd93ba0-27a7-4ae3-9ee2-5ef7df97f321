{"version": 2, "name": "new-builder", "builds": [{"src": "Micro-Services/builder-editor/next.config.js", "use": "@vercel/next"}], "routes": [{"src": "/(.*)", "dest": "Micro-Services/builder-editor/$1"}], "env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key"}, "installCommand": "npm install && cd Micro-Services/builder-editor && npm install", "buildCommand": "cd Micro-Services/builder-editor && npm run build", "outputDirectory": "Micro-Services/builder-editor/.next"}