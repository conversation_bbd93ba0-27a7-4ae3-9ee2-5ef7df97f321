import {
  Box,
  GearIcon,
  <PERSON>ing,
  InputField,
  Stack,
  TrashDeleteBinIcon,
} from "@wuilt/quilt";
import React from "react";
import { Popover } from "../../ui";
import {
  AppsEnum,
  TAppSettings,
  TWhatsAppAgent,
  TWidgetSettings,
} from "../../../../shared/types";
import AgentSettings from "../AgentSettings";
import { AppsInfo, AppsPlaceholder } from "../../../../shared/IconsList";
import AppInfo from "../SortApps/AppInfo";

interface SingleAgent {
  agent: TWhatsAppAgent;
  update: (settings: Partial<TWidgetSettings>) => void;
  agentIndex: number;
  apps: TAppSettings[];
  updateAgents: (
    apps: TAppSettings[],
    agentIndex: number,
    key: string,
    value: string
  ) => void;
  locale: string;
}
function SingleAgent({
  agent,
  agentIndex,
  apps,
  locale,
  update,
  updateAgents,
}: SingleAgent) {
  const removeAgent = (apps: TAppSettings[], agentIndex: number) => {
    const whatsApp = apps.find((app) => app.name === AppsEnum.Whatsapp);
    if (
      agentIndex !== -1 &&
      whatsApp &&
      whatsApp.whatsAppSettings &&
      whatsApp.whatsAppSettings.agents
    ) {
      const whatsAppAgents = whatsApp?.whatsAppSettings.agents;

      whatsAppAgents.splice(agentIndex, 1);
    }
    update({ apps: apps });
  };

  return (
    <Stack
      tablet={{ direction: "column" }}
      direction="row"
      justify="between"
      width="100%"
    >
      <Stack width="180px" direction="row" align="center" justify="between">
        <Stack direction="row" tablet={{ align: "start" }} align="center">
          <Box
            width="40px"
            height="40px"
            color="white"
            borderRadius="100px"
            style={{ background: "transparent" }}
            tablet={{ display: "none" }}
          ></Box>
          <Heading fontWeight="medium">{agent.name}</Heading>
        </Stack>
      </Stack>
      <Stack width="100%" direction="row" align="center">
        <InputField
          borderRadius="8px"
          height="40px"
          type="text"
          value={agent?.phone}
          suffix={<AppInfo info={AppsInfo["Whatsapp"]} />}
          placeholder={AppsPlaceholder["Whatsapp"]}
          onBlur={(e: any) => {
            updateAgents(apps, agentIndex, "phone", e.target.value);
          }}
        />

        <Popover
          closeButton
          translateX={locale.substring(0, 2) === "ar" ? 30 : 80}
          button={
            <Stack
              justify="center"
              align="center"
              border="1px solid #bac7d5"
              borderRadius="8px"
              bg="white"
              height="40px"
              width="40px"
              cursor="pointer"
            >
              <GearIcon viewBox="0 0 20 20" size="md" customColor="#667085" />
            </Stack>
          }
          content={
            <AgentSettings
              agent={agent}
              updateAgents={updateAgents}
              agentIndex={agentIndex}
            />
          }
        />
        <Box>
          <Stack
            justify="center"
            align="center"
            border="1px solid #FDA29B"
            borderRadius="8px"
            bg="white"
            height="40px"
            width="40px"
            cursor="pointer"
            onClick={() => {
              removeAgent(apps, agentIndex);
            }}
          >
            <TrashDeleteBinIcon
              viewBox="0 0 19 19"
              size="md"
              customColor="#D92D20"
            />
          </Stack>
        </Box>
      </Stack>
    </Stack>
  );
}

export default SingleAgent;
