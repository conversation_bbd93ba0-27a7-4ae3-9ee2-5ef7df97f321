import { InputField, Stack, Text } from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import {
  AppsEnum,
  TAppSettings,
  TWhatsAppSettings,
  TWidgetBackground,
} from "../../../../../shared/types";

interface TitleAndSubtitleProps {
  app: TAppSettings;
  appName: AppsEnum;
  handleChangeSettings: (
    appName: AppsEnum,
    objKey: string,
    newValue: string | TWidgetBackground | TWhatsAppSettings
  ) => void;
}

function TitleAndSubtitle({
  app,
  handleChangeSettings,
  appName,
}: TitleAndSubtitleProps) {
  return (
    <Stack mt="10px" direction="row">
      <Stack width="100%" spacing="tight">
        <Text fontWeight="semiBold" color="#1D2939">
          <FormattedMessage defaultMessage="Form title" id="1fbLTL" />
        </Text>
        <InputField
          borderRadius="8px"
          height="40px"
          value={app?.whatsAppSettings?.form.title}
          onBlur={(e: any) => {
            handleChangeSettings(appName, "whatsAppSettings", {
              ...app.whatsAppSettings,
              // @ts-ignore
              form: {
                ...app.whatsAppSettings?.form,
                title: e.target.value,
              },
            });
          }}
        />
      </Stack>
      <Stack width="100%" spacing="tight">
        <Text fontWeight="semiBold" color="#1D2939">
          <FormattedMessage defaultMessage="Popup subtitle" id="fq6fxw" />
        </Text>
        <InputField
          borderRadius="8px"
          height="40px"
          value={app?.whatsAppSettings?.form.subtitle}
          onBlur={(e: any) => {
            handleChangeSettings(app.name, "whatsAppSettings", {
              ...app.whatsAppSettings,
              // @ts-ignore
              form: {
                ...app.whatsAppSettings?.form,
                subtitle: e.target.value,
              },
            });
          }}
        />
      </Stack>
    </Stack>
  );
}

export default TitleAndSubtitle;
