import React from "react";
import { createGlobalStyle, css } from "styled-components";
import { ThemeContext } from "../../contexts";
import { theme as original, ThemeType } from "../../themes/theme";

import { PopoverSlot } from "../Popover";
import { ModalSlot } from "../Modal";
import { FlashBarSlot } from "../FlashBar";
import { ToasterProvider, ToasterSlot } from "../Toaster";
import { SidePanelSlot } from "../SidePanel";
import {
  UniqueIdFactory,
  UniqueIdFactoryContext,
  globalIdGeneratorFactory,
} from "../../utils/unique-id";
import SlotFillProvider from "../SlotFill/SlotFillProvider";

export const baseStyle = css`
  direction: ${(props) => (props.theme.dir === "rtl" ? "rtl" : "ltr")};
  font-family: ${(props) => props.theme.base.fontFamily};
  font-weight: ${(props) => props.theme.base.fontWeight.normal};
  font-size: ${({ theme }) => theme.base.fontSize.sm};
  color: ${(props) => props.theme.palette.ink.normal};
  box-sizing: border-box;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  margin: 0;
`;

const GlobalStyle = createGlobalStyle`
	html {
		scroll-behavior: smooth;
	}

  body {
    ${baseStyle}
  }

  * {
    font-family: ${(props) => props.theme.base.fontFamily};
		box-sizing:border-box;
  }
	p {
		line-height: 1.5;
	}

	h1, h2, h3, h4, h5, h6, p {
		white-space: pre-wrap;
	}
`;

export interface QuiltProviderProps {
  readonly children?: React.ReactNode;
  readonly theme?;
  readonly dir?: "rtl" | "ltr";
  readonly noPortalsRoots?: boolean;
}

const QuiltProvider: React.FC<QuiltProviderProps> = ({
  theme: providedTheme,
  dir = "ltr",
  children,
  noPortalsRoots,
}) => {
  const theme: ThemeType = React.useMemo(() => {
    let nextTheme = providedTheme || original;
    nextTheme = { ...nextTheme, dir, rtl: dir === "rtl" };
    return nextTheme;
  }, [providedTheme, dir]);

  const uniqueIdFactory = React.useMemo(
    () => new UniqueIdFactory(globalIdGeneratorFactory),
    []
  );

  return (
    <ThemeContext.Provider value={theme}>
      <UniqueIdFactoryContext.Provider value={uniqueIdFactory}>
        <SlotFillProvider>
          <ToasterProvider>
            <GlobalStyle />
            {!noPortalsRoots && (
              <div className="ui">
                <FlashBarSlot />
                <ToasterSlot />
                <SidePanelSlot />
                <ModalSlot />
                <PopoverSlot />
                <div id="popups" />
                <div id="tooltips" />
              </div>
            )}

            {children}
          </ToasterProvider>
        </SlotFillProvider>
      </UniqueIdFactoryContext.Provider>
    </ThemeContext.Provider>
  );
};

export { QuiltProvider };
