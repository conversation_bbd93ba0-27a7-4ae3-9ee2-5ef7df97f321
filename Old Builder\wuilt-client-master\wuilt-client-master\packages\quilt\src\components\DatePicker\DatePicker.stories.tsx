import React from "react";
import { DatePicker } from "./DatePicker";
import { Stack } from "../Stack";

export default {
  title: "Components/DatePicker",
  component: DatePicker,
};

export const Playground = () => {
  const [startDate, setStartDate] = React.useState(new Date());
  const [startDateV2, setStartDateV2] = React.useState(new Date());
  const [endDate, setEndDate] = React.useState(null);
  const onChange = (dates) => {
    const [start, end] = dates;
    setStartDate(start);
    setEndDate(end);
  };

  return (
    <Stack spacing="extraLoose">
      <DatePicker
        selected={startDate}
        onChange={onChange}
        dateFormat="MMMM d, yyyy"
        startDate={startDate}
        showPopperArrow={false}
        endDate={endDate}
        showBorder
        selectsRange
        enableIcon
        monthsShown={2}
        disabledKeyboardNavigation
        maxDate={new Date()}
        popperModifiers={[
          {
            name: "flip",
            enabled: false,
          },
        ]}
      />

      <DatePicker
        selected={startDateV2}
        onChange={(date) => {
          setStartDateV2(date);
        }}
        dateFormat="MMMM d, yyyy"
        startDate={startDate}
        showBorder
        showPopperArrow={false}
        disabledKeyboardNavigation
        showTimeInput
        popperModifiers={[
          {
            name: "flip",
            enabled: false,
          },
        ]}
      />
    </Stack>
  );
};
