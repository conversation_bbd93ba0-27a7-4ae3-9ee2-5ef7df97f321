import { TIconType } from "../types";

export function Slack({ size = 40, width, height, gray }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.2197 22.6434C14.2197 23.8058 13.2802 24.7453 12.1178 24.7453C10.9554 24.7453 10.0159 23.8058 10.0159 22.6434C10.0159 21.481 10.9554 20.5415 12.1178 20.5415H14.2197V22.6434Z"
        fill={gray ? "currentColor" : "#E01E5A"}
      />
      <path
        d="M15.271 22.6434C15.271 21.481 16.2105 20.5415 17.3729 20.5415C18.5353 20.5415 19.4748 21.481 19.4748 22.6434V27.8982C19.4748 29.0606 18.5353 30.0001 17.3729 30.0001C16.2105 30.0001 15.271 29.0606 15.271 27.8982V22.6434Z"
        fill={gray ? "currentColor" : "#E01E5A"}
      />
      <path
        d="M17.3729 14.2038C16.2105 14.2038 15.271 13.2643 15.271 12.1019C15.271 10.9395 16.2105 10 17.3729 10C18.5353 10 19.4748 10.9395 19.4748 12.1019V14.2038H17.3729Z"
        fill={gray ? "currentColor" : "#36C5F0"}
      />
      <path
        d="M17.3726 15.2705C18.535 15.2705 19.4745 16.21 19.4745 17.3724C19.4745 18.5348 18.535 19.4743 17.3726 19.4743H12.1019C10.9395 19.4743 10 18.5348 10 17.3724C10 16.21 10.9395 15.2705 12.1019 15.2705H17.3726Z"
        fill={gray ? "currentColor" : "#36C5F0"}
      />
      <path
        d="M25.7964 17.3724C25.7964 16.21 26.7359 15.2705 27.8983 15.2705C29.0607 15.2705 30.0002 16.21 30.0002 17.3724C30.0002 18.5348 29.0607 19.4743 27.8983 19.4743H25.7964V17.3724Z"
        fill={gray ? "currentColor" : "#2EB67D"}
      />
      <path
        d="M24.7453 17.3726C24.7453 18.535 23.8058 19.4745 22.6434 19.4745C21.481 19.4745 20.5415 18.535 20.5415 17.3726V12.1019C20.5415 10.9395 21.481 10 22.6434 10C23.8058 10 24.7453 10.9395 24.7453 12.1019V17.3726Z"
        fill={gray ? "currentColor" : "#2EB67D"}
      />
      <path
        d="M22.6434 25.7964C23.8058 25.7964 24.7453 26.7359 24.7453 27.8983C24.7453 29.0607 23.8058 30.0002 22.6434 30.0002C21.481 30.0002 20.5415 29.0607 20.5415 27.8983V25.7964H22.6434Z"
        fill={gray ? "currentColor" : "#ECB22E"}
      />
      <path
        d="M22.6434 24.7453C21.481 24.7453 20.5415 23.8058 20.5415 22.6434C20.5415 21.481 21.481 20.5415 22.6434 20.5415H27.9141C29.0765 20.5415 30.016 21.481 30.016 22.6434C30.016 23.8058 29.0765 24.7453 27.9141 24.7453H22.6434Z"
        fill={gray ? "currentColor" : "#ECB22E"}
      />
    </svg>
  );
}
