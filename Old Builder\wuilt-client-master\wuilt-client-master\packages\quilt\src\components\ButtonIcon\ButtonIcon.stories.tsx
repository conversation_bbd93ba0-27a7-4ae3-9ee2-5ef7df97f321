import React from "react";
import { ComponentStory, ComponentMeta } from "@storybook/react";
import { ButtonIcon } from "./ButtonIcon";
import * as Icons from "../icons";
import { Box } from "../Box";

// https://storybook.js.org/docs/react/essentials/controls
export default {
  title: "Components/Buttons/ButtonIcon",
  component: ButtonIcon,

  argTypes: {
    children: {
      options: Object.keys(Icons),
      control: { type: "select" },
    },
    className: { control: false },
    download: { control: false },
    to: { control: false },
    cursor: { control: false },
    dataTest: { control: false },
    tabIndex: { control: false },
    plain: { control: false },
    outlined: { control: false },
    ariaControls: { control: false },
    ariaExpanded: { control: false },
    href: { control: false },
    external: { control: false },
    onClick: { control: false },
    role: { control: false },
    prefixIcon: { control: false },
    suffixIcon: { control: false },
  },
} as ComponentMeta<typeof ButtonIcon>;

const Template: ComponentStory<typeof ButtonIcon> = ({
  children,
  ...restProps
}) => {
  const Icon = Icons[children as string];
  return (
    <Box>
      <ButtonIcon {...restProps}>
        <Icon />
      </ButtonIcon>
    </Box>
  );
};

export const ButtonStory = Template.bind({});
ButtonStory.args = {
  children: "TrashIcon",
};
