import { Box, Stack, Text, TextArea } from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import { TAppSettings, TWhatsAppAgent } from "../../../../shared/types";
import { useWidget } from "../../../../context/widget-provider";
import AgentNameAndPosition from "./AgentNameAndPosition";
import { avatarImages } from "../../../../shared/IconsList";
interface WhatsAppSettingsProps {
  agent: TWhatsAppAgent;
  agentIndex: number;
  divider?: boolean;
  updateAgents: (
    apps: TAppSettings[],
    agentIndex: number,
    key: string,
    value: string
  ) => void;
}

function WhatsAppSettings({
  agent,
  divider = false,
  updateAgents,
  agentIndex,
}: WhatsAppSettingsProps) {
  const { settings } = useWidget();

  return (
    <Box>
      <Stack mt="16px" spacing="tight">
        <Text fontWeight="semiBold" color="#1D2939">
          <FormattedMessage defaultMessage="Pre-filled message" id="/9OfuJ" />
        </Text>
        <TextArea
          className="text-area"
          defaultValue={agent?.message}
          onBlur={(e) => {
            updateAgents(settings.apps, agentIndex, "message", e.target.value);
          }}
        />
      </Stack>

      {divider ? (
        <Box
          my="16px"
          borderRadius=""
          style={{ background: "#EAECF0" }}
          height="2px"
          width="100%"
        ></Box>
      ) : null}

      <AgentNameAndPosition
        updateAgentName={(e: any) => {
          updateAgents(settings.apps, agentIndex, "name", e.target.value);
        }}
        updateAgentPosition={(e: any) => {
          updateAgents(settings.apps, agentIndex, "position", e.target.value);
        }}
        agent={agent}
      />
      <Stack spacing="tight">
        <Text fontWeight="semiBold" color="#1D2939">
          <FormattedMessage defaultMessage="Agent Avatar" id="V2/o5Y" />
        </Text>
        <Stack direction="row">
          {avatarImages.map((image) => {
            return (
              <Box
                key={image}
                height="40px"
                width="40px"
                cursor="pointer"
                borderRadius="50%"
                style={{
                  outline:
                    agent?.image === image
                      ? "1px solid #0E9384"
                      : "1px solid #D0D5DD",
                }}
              >
                <img
                  height="100%"
                  width="100%"
                  src={image}
                  onClick={() => {
                    updateAgents(settings.apps, agentIndex, "image", image);
                  }}
                />
              </Box>
            );
          })}
        </Stack>
      </Stack>
    </Box>
  );
}

export default WhatsAppSettings;
