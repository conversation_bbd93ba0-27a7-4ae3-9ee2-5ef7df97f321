import React from "react";
import styled from "styled-components";
import { Text } from "../Text";
import { Stack } from "../Stack";

export interface CircularProgressProps {
  count: number;
  completed: number;
}

const CircularProgress: React.FC<CircularProgressProps> = ({
  completed = 100,
  count = 50,
}) => {
  const progress = (count / completed) * 100 + "%";
  return (
    <StyledProgressContainer progress={progress}>
      <Stack direction="row" spacing="extraTight" align="center">
        <Text fontWeight="medium" fontSize="md" color="secondary">
          {count}
        </Text>
        <Text fontWeight="medium" fontSize="md">
          /
        </Text>
        <Text fontWeight="medium" fontSize="md" color="secondary">
          {completed}
        </Text>
      </Stack>
    </StyledProgressContainer>
  );
};

const StyledProgressContainer = styled.div<{
  progress: string;
}>`
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 48px;
  aspect-ratio: 1;
  border-radius: 50%;
  background: radial-gradient(closest-side, white 92%, transparent 93% 100%),
    conic-gradient(
      ${({ theme }) => theme.base.colors.primary} ${({ progress }) => progress},
      ${({ theme }) => theme.palette.gray[200]} 0
    );
`;

CircularProgress.displayName = "CircularProgress";
export { CircularProgress };
