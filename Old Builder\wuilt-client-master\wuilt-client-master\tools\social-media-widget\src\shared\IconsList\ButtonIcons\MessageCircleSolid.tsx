import { TIconType } from "../types";

export function MessageCircleSolid({
  size = 40,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.6665 10.0001C1.6665 5.39771 5.39746 1.66675 9.99984 1.66675C14.6022 1.66675 18.3332 5.39771 18.3332 10.0001C18.3332 14.6025 14.6022 18.3334 9.99984 18.3334C8.8929 18.3334 7.83442 18.1171 6.86585 17.7238C6.77836 17.6883 6.72785 17.6678 6.69056 17.6538C6.68615 17.6522 6.68232 17.6507 6.67905 17.6496L6.67509 17.6501C6.64433 17.6543 6.60241 17.6611 6.52238 17.6745L3.53358 18.1726C3.39971 18.195 3.24994 18.22 3.11987 18.2298C2.97805 18.2405 2.75244 18.2442 2.514 18.142C2.21925 18.0156 1.98437 17.7807 1.85794 17.4859C1.75568 17.2475 1.75941 17.0219 1.77011 16.88C1.77993 16.75 1.80494 16.6002 1.82729 16.4664C1.82863 16.4583 1.82996 16.4504 1.83128 16.4424L2.32543 13.4775C2.33877 13.3975 2.34567 13.3556 2.34984 13.3248L2.35036 13.3209C2.34917 13.3176 2.34776 13.3138 2.34611 13.3094C2.3321 13.2721 2.31166 13.2216 2.27613 13.1341C1.88277 12.1655 1.6665 11.107 1.6665 10.0001Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
