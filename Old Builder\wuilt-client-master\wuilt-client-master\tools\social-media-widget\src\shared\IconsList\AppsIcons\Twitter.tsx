import React from "react";
import { TIconType } from "../types";

export function Twitter({ size = 40, width, height, color }: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M26.8263 9.40381H30.1998L22.8297 17.8273L31.5 29.2898H24.7112L19.394 22.3378L13.3099 29.2898H9.93443L17.8174 20.2799L9.5 9.40381H16.4611L21.2674 15.7581L26.8263 9.40381ZM25.6423 27.2706H27.5116L15.4454 11.3169H13.4395L25.6423 27.2706Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
