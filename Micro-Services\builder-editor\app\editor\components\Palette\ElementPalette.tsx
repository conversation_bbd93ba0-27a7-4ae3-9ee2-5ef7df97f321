'use client'

import {
  Box,
  VStack,
  Text,
  Icon,
  useColorModeValue,
  SimpleGrid,
  Tooltip
} from '@chakra-ui/react'
import {
  EditIcon,
  ViewIcon,
  LinkIcon,
  PhoneIcon,
  EmailIcon,
  CalendarIcon,
  StarIcon,
  AttachmentIcon
} from '@chakra-ui/icons'
import { motion } from 'framer-motion'

interface ElementTemplate {
  id: string
  type: 'text' | 'image' | 'video' | 'button' | 'form' | 'map' | 'social' | 'custom'
  name: string
  description: string
  icon: any
  defaultProps: Record<string, any>
  defaultStyle: Record<string, any>
}

// Element templates based on Old Builder analysis
const elementTemplates: ElementTemplate[] = [
  {
    id: 'text-heading',
    type: 'text',
    name: 'Heading',
    description: 'Large heading text',
    icon: EditIcon,
    defaultProps: {
      content: 'Your Heading Here',
      fontSize: '32px',
      fontWeight: 'bold'
    },
    defaultStyle: {
      marginBottom: '16px'
    }
  },
  {
    id: 'text-paragraph',
    type: 'text',
    name: 'Paragraph',
    description: 'Body text paragraph',
    icon: EditIcon,
    defaultProps: {
      content: 'Your paragraph text goes here. You can edit this content by clicking on it.',
      fontSize: '16px',
      lineHeight: '1.6'
    },
    defaultStyle: {
      marginBottom: '16px'
    }
  },
  {
    id: 'image-basic',
    type: 'image',
    name: 'Image',
    description: 'Responsive image',
    icon: ViewIcon,
    defaultProps: {
      src: '/placeholder-image.jpg',
      alt: 'Image description',
      width: '100%',
      height: 'auto'
    },
    defaultStyle: {
      borderRadius: '8px'
    }
  },
  {
    id: 'button-primary',
    type: 'button',
    name: 'Button',
    description: 'Call-to-action button',
    icon: LinkIcon,
    defaultProps: {
      text: 'Click Me',
      colorScheme: 'blue',
      size: 'md'
    },
    defaultStyle: {}
  },
  {
    id: 'video-embed',
    type: 'video',
    name: 'Video',
    description: 'Video player',
    icon: AttachmentIcon,
    defaultProps: {
      src: '/placeholder-video.mp4',
      controls: true,
      width: '100%'
    },
    defaultStyle: {
      borderRadius: '8px'
    }
  },
  {
    id: 'form-contact',
    type: 'form',
    name: 'Contact Form',
    description: 'Contact form with fields',
    icon: EmailIcon,
    defaultProps: {
      formType: 'contact',
      fields: ['name', 'email', 'message'],
      submitText: 'Send Message'
    },
    defaultStyle: {}
  },
  {
    id: 'map-location',
    type: 'map',
    name: 'Map',
    description: 'Interactive map',
    icon: CalendarIcon,
    defaultProps: {
      location: 'New York, NY',
      zoom: 12,
      height: '300px'
    },
    defaultStyle: {
      borderRadius: '8px'
    }
  },
  {
    id: 'social-links',
    type: 'social',
    name: 'Social Links',
    description: 'Social media buttons',
    icon: StarIcon,
    defaultProps: {
      platforms: ['facebook', 'twitter', 'instagram', 'linkedin'],
      style: 'buttons'
    },
    defaultStyle: {
      display: 'flex',
      gap: '12px'
    }
  }
]

export function ElementPalette() {
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const hoverBg = useColorModeValue('gray.50', 'gray.700')
  const iconColor = useColorModeValue('gray.600', 'gray.400')

  const handleDragStart = (e: React.DragEvent, template: ElementTemplate) => {
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'element',
      elementType: template.type,
      name: template.name,
      defaultProps: template.defaultProps,
      defaultStyle: template.defaultStyle
    }))
  }

  return (
    <VStack spacing={0} align="stretch" p={3}>
      <Text fontSize="sm" fontWeight="semibold" color="gray.600" mb={3}>
        Drag elements to sections
      </Text>
      
      <SimpleGrid columns={2} spacing={2}>
        {elementTemplates.map((template) => (
          <Tooltip
            key={template.id}
            label={template.description}
            placement="top"
            hasArrow
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Box
                p={3}
                border="1px"
                borderColor={borderColor}
                borderRadius="md"
                cursor="grab"
                _hover={{ bg: hoverBg }}
                _active={{ cursor: 'grabbing' }}
                textAlign="center"
                draggable
                onDragStart={(e) => handleDragStart(e, template)}
                minH="80px"
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                gap={2}
              >
                <Icon
                  as={template.icon}
                  boxSize={5}
                  color={iconColor}
                />
                <Text fontSize="xs" fontWeight="medium" noOfLines={2}>
                  {template.name}
                </Text>
              </Box>
            </motion.div>
          </Tooltip>
        ))}
      </SimpleGrid>
    </VStack>
  )
}
