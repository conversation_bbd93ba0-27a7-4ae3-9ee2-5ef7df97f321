import styled from "styled-components";
import { GeneralInputWrapper } from "../../InputField/InputField";
import { Box } from "../../Box";

const FieldWrapper = styled(Box)<{ isError?: boolean }>`
  ${GeneralInputWrapper} {
    border-color: ${({ isError, theme }) =>
      isError && theme.palette.red.normal};
  }

  input {
    border-color: ${({ isError, theme }) =>
      isError && theme.palette.red.normal} !important;
  }
`;

export default FieldWrapper;
