import React from "react";
import styled, { css } from "styled-components";
import { Fill, Slot } from "../SlotFill";
import { Stack } from "../Stack";
import { ButtonIcon } from "../ButtonIcon";
import { CloseIcon } from "../icons";
import theme from "../../themes/original";

const SLOT_NAME = "flashBar";

type Color = keyof typeof theme.base.colors;

export interface FlashBarProps {
  children?: React.ReactNode;
  isVisible: boolean;
  logo?: React.ReactNode;
  primaryButton?: React.ReactNode;
  secondaryButton?: React.ReactNode;
  description?: React.ReactNode;
  backgroundColor?: Color;
  className?: string;
  style?: React.CSSProperties;
  onClose?: () => void;
}

const FlashBar: React.FC<FlashBarProps> = ({
  isVisible,
  logo,
  primaryButton,
  secondaryButton,
  description,
  children,
  backgroundColor = "white",
  className,
  style,
  onClose,
}) => {
  return (
    <Fill name={SLOT_NAME}>
      <StyledWrapper
        className={className}
        style={style}
        isVisible={isVisible}
        backgroundColor={backgroundColor}
      >
        {children || (
          <Stack direction="row" justify="between">
            <Stack direction="row" inline align="center">
              {logo && logo}
              {description && description}
            </Stack>
            <Stack direction="row" inline align="center">
              {secondaryButton && secondaryButton}
              {primaryButton && primaryButton}
              {onClose && (
                <ButtonIcon onClick={onClose} onlyIcon transparent>
                  <CloseIcon size="lg" />
                </ButtonIcon>
              )}
            </Stack>
          </Stack>
        )}
      </StyledWrapper>
    </Fill>
  );
};

const FlashBarSlot = ({ name = SLOT_NAME }) => {
  return <Slot name={name} />;
};

FlashBar.displayName = "FlashBar";
export { FlashBar, FlashBarSlot };

/**
 *
 * Styles
 *
 */

const StyledWrapper = styled.div<{
  isVisible?: boolean;
  backgroundColor: string;
}>`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 64px;
  overflow: hidden;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.2), 0px 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.1s ease-in-out;
  background-color: ${({ backgroundColor, theme }) =>
    theme.base.colors[backgroundColor]};
  padding: 10px 22px;
  z-index: 701;
  ${({ isVisible }) =>
    !isVisible &&
    css`
      height: 0;
      opacity: 0;
    `};
`;
