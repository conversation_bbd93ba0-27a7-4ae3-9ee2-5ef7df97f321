import React from "react";
import styled from "styled-components";

const WuiltColoredLogo = ({ className = "" }) => (
  <svg className={className} viewBox="0 0 160 127">
    <defs>
      <linearGradient
        x1="60.9098343%"
        y1="100%"
        x2="50%"
        y2="-24.1128666%"
        id="cLinearGradient-1"
      >
        <stop stopColor="#FF1C85" offset="0%" />
        <stop stopColor="#FDAE05" offset="100%" />
      </linearGradient>
      <path
        d="M46.0470117,114.917461 C40.1136459,123.255676 30.9071955,127.627371 22.7489415,125.858861 C16.3446653,124.471124 12.2188135,119.663082 12.0200528,119.406403 C7.42763658,113.486085 7.01337762,106.221852 6.33968375,106.344975 C5.85010498,106.434708 6.45894012,110.259856 5.92960923,110.382978 C5.13875121,110.566619 1.588259,102.559477 0.418709712,93.0477354 C-0.384701606,86.5222383 0.121614902,80.9024226 0.659314665,75.1845263 C1.57361348,65.4599288 3.36664343,58.2374326 4.91906843,52.5237099 C5.18268776,51.5554238 7.16192502,44.4414425 9.7102453,37.8804694 C15.608205,22.7009153 27.2451158,3.60439344 37.5806676,0.588933482 C40.9888891,-0.406481327 45.5520143,-0.0308530971 47.9936315,0.90404383 C49.1087832,1.32975582 49.7887537,1.87441676 50.2427648,2.50255063 C50.9227353,3.44579485 51.1717091,4.69580213 50.9917784,6.25465928 C50.6758537,8.98422442 47.1379148,16.3507114 43.5518549,23.9989195 C38.2857447,35.2281167 37.1078266,37.4151078 33.0049891,48.7444725 C28.9021516,60.0759241 25.849607,70.4140477 23.8536321,79.7484092 C21.8513804,89.0869444 21.0626146,96.6955029 21.4873347,102.567824 C21.684003,105.282782 23.3745144,110.36837 26.851779,111.38048 C30.3006954,112.384762 35.1843158,109.089964 38.6747248,102.870595 L46.0470116,114.917461 Z"
        id="path-2"
      />
      <linearGradient
        x1="9.57205654%"
        y1="75.4841045%"
        x2="90.4442076%"
        y2="15.6134804%"
        id="cLinearGradient-4"
      >
        <stop stopColor="#FFFFFF" stopOpacity="0.64917346" offset="0%" />
        <stop stopColor="#D8D8D8" stopOpacity="0" offset="100%" />
      </linearGradient>
      <linearGradient
        x1="7.54041028%"
        y1="80.325712%"
        x2="89.2481291%"
        y2="57.6147367%"
        id="cLinearGradient-5"
      >
        <stop stopColor="#FF2C77" offset="0%" />
        <stop stopColor="#6723A5" offset="100%" />
      </linearGradient>
      <path
        d="M16.1650992,39.1231175 C16.1650992,35.5567362 16.529145,32.3555489 17.2697898,29.5237294 C18.0041579,26.6898231 19.0544509,24.4381406 20.424853,22.7561608 C21.7910707,21.0804415 23.3142046,20.2394517 25.0005315,20.2394517 C26.6826739,20.2394517 27.5739584,21.2369533 27.6827537,23.2277829 C27.7873645,25.224873 27.3668289,27.743669 26.4211468,30.782084 C25.4733725,33.8267595 24.1573681,37.0780305 22.4773178,40.5400707 C20.7888987,44.0021109 19.1088485,47.2032981 17.4267061,50.1394588 C16.5835426,46.3623082 16.1650992,42.6936725 16.1650992,39.1231175 Z M77.5083712,49.7470934 C77.3620143,49.7742525 77.2157358,49.8000042 77.0695354,49.8243484 C74.1195094,55.7029302 70.7573167,60.8949471 66.970404,65.4045727 C63.1834913,69.9183719 59.2396623,73.6976092 55.1368248,76.7360242 C52.1993522,78.9125812 48.5149581,81.6024966 42.9873208,83.6601046 C39.8573642,84.8266389 36.5181859,86.0349098 31.9425074,86.0223888 C29.990469,86.0161283 25.2620586,85.9430895 20.1298504,83.1217042 C15.5897395,80.6279501 12.9849294,77.2180805 11.746337,75.3190711 C11.7376752,75.3313187 11.7290064,75.3435578 11.7203305,75.3557884 L4.14525703,62.3434594 C1.42634826,56.6476005 0.0696740237,50.2721898 0.0696740237,43.2153784 C0.0696740237,35.8718465 1.17436459,29.0020235 3.38374571,22.5996491 C5.59312684,16.2014482 8.74819004,10.8508328 12.8510275,6.54571588 C16.953865,2.24894629 21.736673,0.0953444444 27.2099126,0.0953444444 C33.9405746,0.0953444444 38.7275671,2.35120065 41.5687977,6.86082623 C43.152606,9.37753537 45.5900388,14.3754776 44.8807772,23.5428933 C43.8765131,36.5250223 37.5496489,46.1118894 34.1205053,51.1828705 C29.894227,57.4287332 25.458727,61.9529666 22.1593008,64.9329505 C22.5735598,66.1203531 23.655236,68.6850592 26.2621383,70.7551881 C29.6055011,73.4075408 33.2898952,73.4701455 34.5201187,73.4743191 C39.6941713,73.4972742 43.3890265,70.6007632 47.4060831,67.4517465 C51.0005119,64.632448 53.1931552,61.9654875 55.7686743,58.7956026 C58.1014963,55.9220467 61.04943,51.921606 63.9910871,46.7754993 C63.8809383,46.6812331 63.7685421,46.5841938 63.6540369,46.4843638 L77.5083712,49.7470934 Z"
        id="path-6"
      />
      <linearGradient
        x1="59.9228311%"
        y1="0%"
        x2="76.1089087%"
        y2="52.7177593%"
        id="cLinearGradient-8"
      >
        <stop stopColor="#FFFFFF" stopOpacity="0.668449955" offset="0%" />
        <stop stopColor="#D8D8D8" stopOpacity="0" offset="100%" />
      </linearGradient>
      <linearGradient
        x1="64.2473335%"
        y1="8.11984287%"
        x2="23.323738%"
        y2="96.1358413%"
        id="cLinearGradient-9"
      >
        <stop stopColor="#00F490" offset="0%" />
        <stop stopColor="#6723A5" offset="100%" />
      </linearGradient>
      <path
        d="M25.4608236,59.795636 C23.9858106,64.5181175 22.6216851,67.930074 21.3579861,70.0252447 C18.4100524,66.8783149 16.6253913,62.7380571 15.9935418,57.5919503 C15.3637844,52.4541909 15.4663031,47.4708564 16.3094665,42.6419468 C17.1463533,37.8172109 18.6213663,33.7270368 20.7261366,30.367251 C22.8267224,27.0116388 25.2495097,25.6468562 27.9840372,26.2749901 C29.4548658,26.6986153 30.1934184,28.7437023 30.1934184,32.412338 C30.1934184,36.0872342 29.7707905,40.3902642 28.9318115,45.3172545 C28.0886481,50.2505053 26.9295599,55.0752412 25.4608236,59.795636 Z M29.7490579,86.3362169 C29.7926565,86.2499903 29.8361658,86.1636151 29.8795858,86.0770911 C33.6644063,85.4468704 37.5015323,83.8734055 41.3972403,81.3566963 C45.2866716,78.8379004 49.127982,75.6408868 52.9148947,71.7573083 C56.7018074,67.8758165 60.275314,63.5227027 63.6437834,58.6937932 C68.0897444,52.3185474 70.9267907,48.6519985 72.4792157,43.2721675 C73.9228454,38.2700516 73.0671286,36.8781403 72.4331869,36.3334794 C71.0711536,35.1690319 68.5144645,35.8263813 67.1147713,36.5045989 C65.7318159,37.1765561 64.7861338,38.1970128 62.4428508,41.8739958 C60.612161,44.7475517 59.3714763,46.978366 59.0681048,47.5209401 C56.7520206,51.6528507 54.5593773,54.4971911 52.4420537,57.4354386 C50.0213586,60.7952244 47.3935341,63.8878968 44.5523035,66.7197163 C41.713165,69.5536226 38.9221476,71.7072245 36.1897122,73.1721746 C45.4456801,52.6107027 50.0736641,36.0329768 50.0736641,23.4431706 C50.0736641,10.8533645 46.4938808,3.40549138 39.3447754,1.09537777 C35.5599549,-0.162976797 31.5052384,-0.213060561 27.1973637,0.93886601 C21.2742974,2.52067822 17.3325606,5.502749 14.8888512,7.54783603 C9.64784767,11.9322522 6.43629461,17.1639187 6.77104933,17.4602476 C7.01583871,17.6772773 9.12270121,15.198131 9.68341536,15.5737592 C10.6939562,16.2540637 5.14330452,24.1067805 2.42551467,33.0425587 C-3.42850843,52.2830714 6.00529791,69.6245746 6.59111867,70.6658996 C9.9721413,76.6634303 14.0749788,80.6951733 16.8011375,83.028242 C16.7714187,83.0802318 16.7416993,83.1321046 16.7119798,83.1838605 L29.7490586,86.336217 Z"
        id="path-10"
      />
      <linearGradient
        x1="57.9025752%"
        y1="0%"
        x2="24.3792969%"
        y2="64.3239581%"
        id="cLinearGradient-12"
      >
        <stop stopColor="#FFFFFF" stopOpacity="0.690415534" offset="0%" />
        <stop stopColor="#D8D8D8" stopOpacity="0" offset="100%" />
      </linearGradient>
      <linearGradient
        x1="0%"
        y1="0%"
        x2="100%"
        y2="86.0887156%"
        id="cLinearGradient-13"
      >
        <stop stopColor="#000000" stopOpacity="0" offset="0%" />
        <stop stopColor="#000000" offset="100%" />
      </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g id="Artboard">
        <g id="Group-2">
          <g id="Group-3">
            <g id="logo">
              <g id="w" transform="translate(0.077326, 0.042658)">
                <g id="first">
                  <mask id="mask-3" fill="white">
                    <use xlinkHref="#path-2" />
                  </mask>
                  <use
                    id="Mask"
                    fill="url(#cLinearGradient-1)"
                    xlinkHref="#path-2"
                  />
                  <use
                    id="Mask"
                    fill="url(#cLinearGradient-1)"
                    xlinkHref="#path-2"
                  />
                  <path
                    d="M11.8830756,73.9823821 C15.9836823,22.1732752 52.681434,-2.28573553 41.2822069,-6.50565336 C31.892044,-9.98182901 17.0167588,13.0737079 8.85560692,34.3987394 C-0.918342248,59.9379985 -1.77429721,83.1501941 -1.63535189,85.1032704 C-0.759293162,97.4175361 2.66582937,123.200081 16.6537808,124.064615 C30.6417322,124.929149 7.78246888,125.791489 11.8830756,73.9823821 Z"
                    id="Path-Copy-7"
                    fill="url(#cLinearGradient-4)"
                    opacity="0.400000006"
                    mask="url(#mask-3)"
                  />
                </g>
                <g id="mid" transform="translate(33.789923, 40.329089)">
                  <mask id="mask-7" fill="white">
                    <use xlinkHref="#path-6" />
                    <use xlinkHref="#path-6" />
                  </mask>
                  <use
                    id="Mask"
                    fill="url(#cLinearGradient-5)"
                    xlinkHref="#path-6"
                  />
                  <use
                    id="Mask"
                    fill="url(#cLinearGradient-5)"
                    xlinkHref="#path-6"
                  />
                  <path
                    d="M32.3549989,45.3597674 C35.7922658,37.6560369 37.7863951,30.4323893 38.329264,23.6904248 C38.8734866,16.9500607 37.9217739,11.2818817 35.4781871,6.67628628 C33.0346003,2.07709195 28.9136706,-0.225705775 23.1208131,-0.225705775 C18.4109857,-0.225705775 14.2954711,1.97147371 10.7647929,6.35623101 C7.23411457,10.7441889 4.51841647,16.2011313 2.61634477,22.7286587 C0.715626861,29.2593867 -0.234732095,36.266997 -0.234732095,43.7546902 C-0.234732095,47.533747 5.86024309,66.7929593 13.6503432,67.1915132 C21.3084747,67.5833154 30.6490337,49.1795209 32.3549989,45.3597674 Z"
                    id="Path-Copy-6"
                    fill="url(#cLinearGradient-8)"
                    opacity="0.5"
                    mask="url(#mask-7)"
                  />
                </g>
                <g id="last" transform="translate(80.968237, 4.044646)">
                  <mask id="mask-11" fill="white">
                    <use xlinkHref="#path-10" />
                    <use xlinkHref="#path-10" />
                  </mask>
                  <use
                    id="Mask"
                    fill="url(#cLinearGradient-9)"
                    xlinkHref="#path-10"
                  />
                  <use
                    id="Mask"
                    fill="url(#cLinearGradient-9)"
                    xlinkHref="#path-10"
                  />
                  <path
                    d="M37.1617474,54.4274915 C41.189572,45.4001812 43.5263131,36.9354369 44.1624524,29.0351339 C44.8001781,21.1367062 43.6849514,14.494676 40.8215314,9.09779211 C37.9581113,3.70840907 33.1291637,1.00996711 26.3410339,1.00996711 C20.82201,1.00996711 15.9994078,3.58464453 11.8621228,8.72274805 C7.72483787,13.864602 4.54255501,20.2591031 2.31368791,27.9081266 C0.0864071915,35.5609005 -1.02723317,43.77249 -1.02723317,52.5466456 C-1.02723317,56.9749831 6.11492119,79.5431254 15.2434403,80.0101549 C24.2173173,80.4692727 35.16268,58.9035178 37.1617474,54.4274915 Z"
                    id="Path-Copy-8"
                    fill="url(#cLinearGradient-12)"
                    opacity="0.632144487"
                    mask="url(#mask-11)"
                  />
                  <path
                    d="M78.7521584,28.4806457 C78.6659284,29.5610086 78.4441939,30.8818422 77.6813218,32.2404139 C77.3408011,32.8459789 76.9958808,33.4594424 76.2937218,33.9868983 C74.5831989,35.2726267 72.4327268,34.7864195 72.0165347,34.6898802 C71.649617,34.60475 70.2426592,34.2422887 69.0873523,33.0074628 C68.725714,32.6213055 67.9892389,31.7735146 67.9241264,30.718603 C67.8361365,29.2749011 69.0723941,28.2103355 70.6896477,26.7534692 C76.1810947,21.8089 76.586728,20.8803671 77.4173523,21.1840272 C79.2800976,21.8668236 78.859506,27.1352384 78.7521584,28.4806457"
                    id="Fill-3"
                    fill="#17C595"
                  />
                </g>
              </g>
              <g
                id="shadows"
                transform="translate(38.404177, 87.316767)"
                fill="url(#cLinearGradient-13)"
                opacity="0.100000001"
              >
                <path
                  d="M0,16.1785834 C1.36131082,18.6182877 2.63349887,20.8388776 3.81656415,22.8403531 C4.99962943,24.8418286 6.13124796,26.6362267 7.21141974,28.2235472 C6.69696489,28.874403 6.16168451,29.4988618 5.60557859,30.0969236 C5.04947268,30.6949855 4.53264628,31.2501329 4.05509941,31.762366 L0,16.1785834 Z"
                  id="Path-2"
                />
                <path
                  d="M59.2760119,0 C60.3096647,0.317227126 61.7806321,0.713761034 63.6889142,1.18960172 C65.5971963,1.66544241 68.4596194,2.22058988 72.2761835,2.85504414 L70.1293662,7.13761034 L59.2760119,0 Z"
                  id="Path-2-Copy"
                />
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
);

const StyledColoredLogo = styled(WuiltColoredLogo)<{ size }>`
  width: ${(props) => props.size}px;
  height: ${(props) => props.size}px;
`;

export { WuiltColoredLogo, StyledColoredLogo };
