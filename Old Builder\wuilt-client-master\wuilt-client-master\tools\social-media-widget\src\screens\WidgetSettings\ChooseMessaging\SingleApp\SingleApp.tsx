import React from "react";
import { Box, CloseIcon, Stack } from "@wuilt/quilt";
import { useWidget } from "../../../../context/widget-provider";
import { AppsEnum, TAppSettings } from "../../../../shared/types";
import { AppsDefaultColor, AvatarImages } from "../../../../shared/IconsList";

interface SingleAppProps {
  app: { name: AppsEnum; Icon: any };
}
export default function SingleApp({ app }: SingleAppProps) {
  const { settings, update } = useWidget();
  const { Icon, name } = app;

  const isSelected = settings.apps.some((app) => app.name === name);

  const addOrRemoveApp = (array: TAppSettings[], newItem: TAppSettings) => {
    const index = array.findIndex((item) => item.name === newItem.name);

    if (index !== -1) {
      // If the app already exists, remove it
      array.splice(index, 1);
    } else {
      // If the app doesn't exist, add it
      array.push(newItem);
    }

    update({ apps: array });
  };

  return (
    <Box
      key={name}
      cursor="pointer"
      position="relative"
      color="white"
      borderRadius="100px"
      width="40px"
      height="40px"
      style={{ background: isSelected ? AppsDefaultColor[name] : "#D0D5DD" }}
      onClick={() => {
        addOrRemoveApp(settings?.apps, {
          name,
          value: "",
          onHoverText: app.name,
          background: { color: AppsDefaultColor[name], gradient: false },
          whatsAppSettings:
            app.name === AppsEnum.Whatsapp
              ? {
                  agents: [
                    {
                      name: "Agent 1",
                      position: "",
                      message: "",
                      image: AvatarImages.male,
                      phone: "",
                    },
                  ],
                  form: {
                    title: "Start a Conversation",
                    subtitle: "Click one of our members below to chat",
                  },
                }
              : undefined,
        });
      }}
    >
      <Icon
        gray={!isSelected}
        messengerThunderColor={isSelected ? AppsDefaultColor[name] : "#D0D5DD"}
      />
      {isSelected && (
        <Stack
          justify="center"
          align="center"
          height="15px"
          width="15px"
          top="-5px"
          border="2px solid white"
          right="0"
          borderRadius="50%"
          position="absolute"
          style={{ background: "#F04438" }}
        >
          <CloseIcon size="xs" color="white" />
        </Stack>
      )}
    </Box>
  );
}
