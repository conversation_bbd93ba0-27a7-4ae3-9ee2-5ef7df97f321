import React, { InputHTMLAttributes } from "react";
import styled, { css } from "styled-components";
import { color, rtl } from "../../utils";
import { Global } from "../../common/types";

export interface InputRangeProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, "onChange">,
    Global {
  isDisabled?: boolean;
  isError?: boolean;
  onChange?: (value: number) => void;
}

const InputRange = React.forwardRef<HTMLInputElement, InputRangeProps>(
  (
    {
      min = 0,
      max = 100,
      value = 0,
      isDisabled,
      isError,
      step = 1,
      onChange,
      ...restProps
    },
    ref
  ) => {
    const size = ((+value - +min) * 100) / (+max - +min);
    const backgroundSize = `${size}% 100%`;
    return (
      <StyledInput
        ref={ref}
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        disabled={isDisabled}
        isError={isError || false}
        onChange={(e) => onChange?.(+e.target.value)}
        {...restProps}
        style={{ backgroundSize, ...restProps?.style }}
      />
    );
  }
);

InputRange.displayName = "InputRange";
export { InputRange };

/**
 * Styles
 */

const StyledTrack = css`
  -webkit-appearance: none;
  box-shadow: none;
  border: none;
  background-color: transparent;
`;

const StyledThump = css<{ disabled?: boolean; isError?: boolean }>`
  -webkit-appearance: none; /* Override default look */
  appearance: none;
  width: 24px;
  height: 24px;
  border: 3px solid ${color("product")};
  border-radius: 100%;
  background: ${color("white")};
  cursor: ew-resize;

  &:hover,
  &:focus,
  &:active {
    box-shadow: 0px 0px 0px 6px rgba(0, 169, 145, 0.16);
  }
  ${({ disabled }) =>
    disabled &&
    css`
      cursor: not-allowed;
    `}

  ${({ isError }) =>
    isError &&
    css`
      border-color: ${color("red")};
    `}
`;

const StyledInput = styled.input<{ isError: boolean }>`
  -webkit-appearance: none; /* Override default CSS styles */
  appearance: none;
  width: 100%;
  height: 8px;
  background-color: ${color("cloud", "darkest")};
  background-position: ${rtl("right", "left")};
  background-image: linear-gradient(${color("product")}, ${color("product")});

  background-repeat: no-repeat;
  outline: none;
  border-radius: 6px;
  opacity: 0.7;
  -webkit-transition: 0.2s;
  transition: opacity 0.2s;
  cursor: pointer;

  &:hover,
  &:active,
  &:focus {
    opacity: 1;
  }

  ${({ disabled }) =>
    disabled &&
    css`
      opacity: 0.5;
      cursor: not-allowed;
    `}

  ${({ isError }) =>
    isError &&
    css`
      border: 1px solid ${color("red")};
    `}

  &::-webkit-slider-thumb {
    ${StyledThump}
  }
  &::-moz-range-thumb {
    ${StyledThump}
  }
  &::-ms-thumb {
    ${StyledThump}
  }

  &::-webkit-slider-runnable-track {
    ${StyledTrack}
  }

  &::-moz-range-track {
    ${StyledTrack}
  }

  &::-ms-track {
    ${StyledTrack}
  }
`;
