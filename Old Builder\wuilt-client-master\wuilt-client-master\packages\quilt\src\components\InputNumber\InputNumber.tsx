import React, { useCallback } from "react";
import { InputFieldProps, InputField } from "../InputField/InputField";

export interface InputNumberProps
  extends Omit<InputFieldProps, "onChange" | "value"> {
  value?: number | string;
  onChange?: (value: any) => void;
}

const InputNumber: React.FC<InputNumberProps> = ({
  width,
  isDisabled,
  value,
  maxValue,
  minValue,
  onChange,
  ...restProps
}) => {
  const handleAdd = useCallback((input: React.ReactText) => {
    handleChange(+input + 1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleDecrease = useCallback((input: React.ReactText) => {
    handleChange(+input - 1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleChange = (input: any) => {
    if (!isDisabled) {
      const numberValue = getNumberValue(input, minValue, maxValue);
      if (numberValue !== false) {
        if (onChange) {
          return onChange(numberValue);
        }
      }
    }
  };

  return (
    <div style={{ width: width || "100%" }}>
      <InputField
        type="number"
        handleArrowUpClick={handleAdd}
        handleArrowDownClick={handleDecrease}
        isDisabled={isDisabled}
        value={value}
        onChange={handleChange}
        {...restProps}
      />
    </div>
  );
};

InputNumber.displayName = "InputNumber";
export { InputNumber };

/**
 *
 *
 * Functions
 *
 *
 */

function isEvent(event: any) {
  return Boolean(event && event.target);
}

function getNumberValue(value: any, minValue?: number, maxValue?: number) {
  if (value == undefined) {
    return "";
  }

  const pureValue = isEvent(value) ? value.target.value : value?.toString();

  if (pureValue.trim() === "") {
    return "";
  }

  const numberValue = Number(pureValue);

  if (maxValue != undefined && minValue != undefined) {
    return numberValue <= maxValue && numberValue >= minValue
      ? numberValue
      : false;
  }

  if (maxValue != undefined) {
    return numberValue <= maxValue ? numberValue : false;
  }

  if (minValue != undefined) {
    return numberValue >= minValue ? numberValue : false;
  }

  return numberValue;
}
