import React, { useRef, useContext } from "react";
import styled from "styled-components";
import { color } from "../../utils";
import { SelectTabsContext } from "./SelectTabs";
import { Global } from "../../common/types";

export interface SelectTabProps extends Global {
  value: string | number;
  label: React.ReactNode;
  isDisabled?: boolean;
}

const SelectTab: React.FC<SelectTabProps> = ({
  label,
  value,
  dataTest,
  isDisabled,
}) => {
  const wrapperRef = useRef<HTMLDivElement>(null);

  const {
    name,
    value: selectedValue,
    onChange,
  } = useContext(SelectTabsContext);

  const isSelected = value === selectedValue;

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    event.stopPropagation();
    if (isDisabled) {
      return;
    }
    if (wrapperRef.current) wrapperRef.current.focus();
    onChange(value);
  };

  return (
    <StyledContainer
      ref={wrapperRef}
      onClick={handleClick}
      data-test={dataTest}
      isDisabled={isDisabled || false}
      isSelected={isSelected}
    >
      <StyledInput
        type="radio"
        name={name}
        value={value}
        id={value.toString()}
      />
      <StyledLabel htmlFor={value.toString()} isSelected={isSelected}>
        {label}
      </StyledLabel>
    </StyledContainer>
  );
};

export { SelectTab };

// Styles

const StyledContainer = styled.div<{
  isSelected: boolean;
  isDisabled: boolean;
}>`
  display: flex;
  padding: 10px 15px;
  outline: none;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-right: 2px solid ${color("cloud", "darkest")};
  flex: 1;
  color: ${({ isSelected, isDisabled }) =>
    isDisabled
      ? color("ink", "lighter")
      : isSelected
      ? "white"
      : color("ink", "lightActive")} !important;
  background-color: ${({ isSelected }) =>
    isSelected ? color("product", "normal") : ""} !important;
`;

const StyledInput = styled.input`
  outline: none;
  appearance: none;
  visibility: hidden;
  -moz-appearance: none;
  -webkit-appearance: none;
  margin: 0;
`;

const StyledLabel = styled.label<{
  isSelected: boolean;
}>`
  font-weight: 700;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  white-space: nowrap;
  padding: 0px !important;
  margin: 0px !important;
  color: ${({ isSelected }) => (isSelected ? color("white") : color("ink"))};
`;
