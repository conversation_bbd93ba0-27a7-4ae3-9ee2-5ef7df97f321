import { useState, useEffect } from "react";

import { boundingClientRect } from "../../utils/boundingClientRect";

export const useDimensions = ({
  containerRef,
  popover,
  content,
  fixed,
  scrollableParent,
  children,
}) => {
  const [positions, setPositions] = useState({
    containerTop: 0,
    containerPureTop: 0,
    containerLeft: 0,
    containerHeight: 0,
    containerWidth: 0,
    popoverHeight: 0,
    popoverWidth: 0,
    windowScrollTop: 0,
    windowWidth: 0,
    windowHeight: 0,
    contentHeight: 0,
    documentHeight: 0,
  });

  useEffect(() => {
    const calculate = () => {
      const containerFrame =
        containerRef?.current?.ownerDocument?.defaultView?.frameElement;
      const offsets = { top: 0, left: 0 };
      // TODO: revert iframe portals
      // if (containerFrame) {
      //   const frameDimensions = boundingClientRect({ current: containerFrame });
      //   offsets.top = frameDimensions?.top;
      //   offsets.left = frameDimensions?.left;
      // }
      const containerDimensions = boundingClientRect(containerRef);
      const popoverDimensions = boundingClientRect(popover);
      const contentDimensions = boundingClientRect(content);

      if (
        containerDimensions &&
        popoverDimensions &&
        contentDimensions &&
        typeof window !== "undefined"
      ) {
        const documentDimensions = window.document.body.getBoundingClientRect();

        setPositions({
          containerTop: offsets.top + containerDimensions.top,
          containerLeft: offsets.left + containerDimensions.left,
          containerHeight: containerDimensions.height,
          containerWidth: containerDimensions.width,
          containerPureTop: offsets.top + containerDimensions.pureTop,
          popoverHeight: popoverDimensions.height,
          popoverWidth: popoverDimensions.width,
          windowScrollTop: window.scrollY || window.pageYOffset,
          windowWidth: window.innerWidth,
          windowHeight: window.innerHeight,
          contentHeight: contentDimensions.height,
          documentHeight: documentDimensions.height,
        });
      }
    };

    calculate();

    window.addEventListener("resize", calculate);
    if (fixed) window.addEventListener("scroll", calculate);
    if (scrollableParent !== document.body && scrollableParent) {
      scrollableParent.addEventListener("scroll", calculate);
    }

    return () => {
      window.removeEventListener("resize", calculate);
      if (fixed) window.removeEventListener("scroll", calculate);
      if (scrollableParent !== document.body && scrollableParent) {
        scrollableParent.removeEventListener("scroll", calculate);
      }
    };
  }, [containerRef, content, popover, fixed, scrollableParent, children]);

  return positions;
};
