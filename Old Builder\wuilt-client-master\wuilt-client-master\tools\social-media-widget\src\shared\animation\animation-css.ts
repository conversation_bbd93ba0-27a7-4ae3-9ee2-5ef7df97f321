import { css, keyframes } from "styled-components";

/**
 * HeartBeat
 */

const heartBeatAnimation = keyframes`
	0% {
		transform: scale(1);
	}

	14% {
		transform: scale(1.3);
	}

	28% {
		transform: scale(1);
	}

	42% {
		transform: scale(1.3);
	}

	70% {
		transform: scale(1);
	}
`;

export const heartBeat = css`
  animation-name: ${heartBeatAnimation};
  animation-timing-function: ease-in-out;
`;

/**
 * Wobble
 */

const wobbleAnimation = keyframes`
	from {
		transform: translate3d(0, 0, 0);
	}

	15% {
		transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
	}

	30% {
		transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
	}

	45% {
		transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
	}

	60% {
		transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
	}

	75% {
		transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
	}

	to {
		transform: translate3d(0, 0, 0);
	}
`;

export const wobble = css`
  animation-name: ${wobbleAnimation};
`;

/**
 * RubberBand
 */

const rubberBandAnimation = keyframes`
	from {
		transform: scale3d(1, 1, 1);
	}

	30% {
		transform: scale3d(1.25, 0.75, 1);
	}

	40% {
		transform: scale3d(0.75, 1.25, 1);
	}

	50% {
		transform: scale3d(1.15, 0.85, 1);
	}

	65% {
		transform: scale3d(0.95, 1.05, 1);
	}

	75% {
		transform: scale3d(1.05, 0.95, 1);
	}

	to {
		transform: scale3d(1, 1, 1);
	}
`;

export const rubberBand = css`
  animation-name: ${rubberBandAnimation};
`;

/**
 * Bounce
 */

const bounceAnimation = keyframes`
	from,
	20%,
	53%,
	to {
		animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		transform: translate3d(0, 0, 0);
	}

	40%,
	43% {
		animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transform: translate3d(0, -30px, 0) scaleY(1.1);
	}

	70% {
		animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transform: translate3d(0, -15px, 0) scaleY(1.05);
	}

	80% {
		transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		transform: translate3d(0, 0, 0) scaleY(0.95);
	}

	90% {
		transform: translate3d(0, -4px, 0) scaleY(1.02);
	}
`;

export const bounce = css`
  animation-name: ${bounceAnimation};
  transform-origin: center bottom;
`;

/**
 * Pulse
 */

const pulseAnimation = keyframes`
	from {
		transform: scale3d(1, 1, 1);
	}

	50% {
		transform: scale3d(1.1, 1.1, 1.1);
	}

	to {
		transform: scale3d(1, 1, 1);
	}
`;

export const pulse = css`
  animation-name: ${pulseAnimation};
  animation-timing-function: ease-in-out;
`;

/**
 * HeadShake
 */

const headShakeAnimation = keyframes`
	0% {
		transform: translateX(0);
	}

	6.5% {
		transform: translateX(-6px) rotateY(-9deg);
	}

	18.5% {
		transform: translateX(5px) rotateY(7deg);
	}

	31.5% {
		transform: translateX(-3px) rotateY(-5deg);
	}

	43.5% {
		transform: translateX(2px) rotateY(3deg);
	}

	50% {
		transform: translateX(0);
	}
`;

export const headShake = css`
  animation-name: ${headShakeAnimation};
  animation-timing-function: ease-in-out;
`;

/**
 * Tada
 */

const tadaAnimation = keyframes`
	from {
		transform: scale3d(1, 1, 1);
	}

	10%,
	20% {
		transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
	}

	30%,
	50%,
	70%,
	90% {
		transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
	}

	40%,
	60%,
	80% {
		transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
	}

	to {
		transform: scale3d(1, 1, 1);
	}
`;

export const tada = css`
  animation-name: ${tadaAnimation};
  animation-timing-function: ease-in-out;
`;

/**
 * Jello
 */

const jelloAnimation = keyframes`
	from,
	11.1%,
	to {
		transform: translate3d(0, 0, 0);
	}

	22.2% {
		transform: skewX(-12.5deg) skewY(-12.5deg);
	}

	33.3% {
		transform: skewX(6.25deg) skewY(6.25deg);
	}

	44.4% {
		transform: skewX(-3.125deg) skewY(-3.125deg);
	}

	55.5% {
		transform: skewX(1.5625deg) skewY(1.5625deg);
	}

	66.6% {
		transform: skewX(-0.78125deg) skewY(-0.78125deg);
	}

	77.7% {
		transform: skewX(0.390625deg) skewY(0.390625deg);
	}

	88.8% {
		transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
	}
`;

export const jello = css`
  animation-name: ${jelloAnimation};
  transform-origin: center;
`;

/**
 * Ring
 */

const ringAnimation = keyframes`
	0% {
		width: 60px;
		height: 60px;
		opacity: 1;
	}
	100% {
		width: 120px;
		height: 120px;
		opacity: 0;
	}
`;

export const ring = css<{ size: string; color: string }>`
  ::after {
    content: "";
    width: ${({ size }) => size};
    height: ${({ size }) => size};
    border-radius: 100%;
    border: 6px solid ${({ color }) => color};
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: ${ringAnimation} 1.5s infinite;
  }
`;

/**
 * DoubleRing
 */

const doubleRingAnimation = keyframes`
	0% {
		width: 60px;
		height: 60px;
		opacity: 1;
	}
	100% {
		width: 120px;
		height: 120px;
		opacity: 0;
	}
`;

export const doubleRing = css<{ size: string; color: string }>`
  ::after {
    content: "";
    width: ${({ size }) => size};
    height: ${({ size }) => size};
    border-radius: 100%;
    border: 6px solid ${({ color }) => color};
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: ${doubleRingAnimation} 1.5s infinite;
  }

  ::before {
    content: "";
    width: 30px;
    height: 30px;
    border-radius: 100%;
    border: 6px solid ${({ color }) => color};
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: ${doubleRingAnimation} 3s infinite;
  }
`;

/**
 * MoveIn
 */

const moveInAnimation = keyframes`
	0% {
		opacity: 0;
		transform: scaleX(1) scaleY(1);
	}

	50% {
		opacity: 1;
		transform: scaleX(1.4) scaleY(1.4);
	}

	100% {
		opacity: 0;
		transform: scaleX(1) scaleY(1);
	}
`;

export const moveIn = css<{ color: string }>`
  ::after {
    content: "";
    animation: ${moveInAnimation} 3s infinite;
    opacity: 0;
    background-color: ${({ color }) => color};
    display: inline-block;
    height: 100%;
    width: 100%;
    border-radius: 100px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
  }
`;
