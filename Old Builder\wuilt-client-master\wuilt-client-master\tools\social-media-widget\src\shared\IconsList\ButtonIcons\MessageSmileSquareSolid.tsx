import { TIconType } from "../types";

export function MessageSmileSquareSolid({
  size = 20,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.5343 1.66675H6.4655C5.7947 1.66674 5.24106 1.66673 4.79006 1.70358C4.32165 1.74185 3.89106 1.82399 3.48662 2.03006C2.85941 2.34964 2.34947 2.85957 2.0299 3.48678C1.82382 3.89123 1.74168 4.32181 1.70341 4.79023C1.66656 5.24122 1.66657 5.79486 1.66658 6.46567L1.66654 11.7814C1.66618 12.4442 1.66592 12.9364 1.78016 13.3628C2.08839 14.5131 2.98688 15.4116 4.13719 15.7198C4.39341 15.7885 4.67341 15.8158 4.99992 15.8266L4.99991 16.9759C4.99988 17.1719 4.99984 17.3718 5.01471 17.5355C5.02894 17.6921 5.06661 17.9878 5.27299 18.2463C5.51047 18.5438 5.87059 18.7169 6.25127 18.7165C6.58209 18.7161 6.83645 18.5609 6.96764 18.4741C7.10481 18.3835 7.26084 18.2586 7.4139 18.1361L9.42484 16.5273C9.857 16.1816 9.9853 16.084 10.1186 16.0159C10.2523 15.9476 10.3947 15.8976 10.5418 15.8674C10.6885 15.8373 10.8496 15.8334 11.403 15.8334H13.5344C14.2052 15.8334 14.7588 15.8334 15.2098 15.7966C15.6782 15.7583 16.1088 15.6762 16.5132 15.4701C17.1404 15.1505 17.6504 14.6406 17.9699 14.0134C18.176 13.6089 18.2582 13.1784 18.2964 12.7099C18.3333 12.2589 18.3333 11.7053 18.3333 11.0345V6.46565C18.3333 5.79485 18.3333 5.24122 18.2964 4.79023C18.2582 4.32181 18.176 3.89123 17.9699 3.48678C17.6504 2.85957 17.1404 2.34964 16.5132 2.03006C16.1088 1.82399 15.6782 1.74185 15.2098 1.70358C14.7588 1.66673 14.2051 1.66674 13.5343 1.66675ZM11.2437 6.25008C11.2437 5.55973 11.8034 5.00008 12.4937 5.00008C13.1841 5.00008 13.7437 5.55973 13.7437 6.25008C13.7437 6.94044 13.1841 7.50008 12.4937 7.50008C11.8034 7.50008 11.2437 6.94044 11.2437 6.25008ZM6.16041 9.75013C6.52744 9.47486 7.04764 9.54814 7.32446 9.91333C7.37583 9.97887 7.43235 10.0405 7.48993 10.1006C7.61268 10.2287 7.79713 10.4025 8.03597 10.5762C8.51685 10.9259 9.18168 11.2501 9.99374 11.2501C10.8058 11.2501 11.4706 10.9259 11.9515 10.5762C12.1904 10.4025 12.3748 10.2287 12.4976 10.1006C12.5551 10.0406 12.6117 9.97886 12.663 9.91333C12.9398 9.54814 13.46 9.47486 13.8271 9.75013C14.1953 10.0263 14.2699 10.5486 13.9937 10.9168C13.9958 10.9141 13.9909 10.9203 13.9824 10.931C13.9621 10.9566 13.9214 11.0077 13.9076 11.0241C13.8587 11.0824 13.7896 11.1612 13.7009 11.2538C13.5241 11.4382 13.2659 11.6811 12.9318 11.9241C12.2669 12.4077 11.265 12.9168 9.99374 12.9168C8.72247 12.9168 7.72063 12.4077 7.05568 11.9241C6.7216 11.6811 6.46335 11.4382 6.28661 11.2538C6.19791 11.1612 6.12875 11.0824 6.07988 11.0241C6.05541 10.995 6.03595 10.9709 6.02158 10.9528C5.73372 10.5809 5.77242 10.0411 6.16041 9.75013ZM7.49373 5.00008C6.80338 5.00008 6.24373 5.55973 6.24373 6.25008C6.24373 6.94044 6.80338 7.50008 7.49373 7.50008C8.18409 7.50008 8.74373 6.94044 8.74373 6.25008C8.74373 5.55973 8.18409 5.00008 7.49373 5.00008Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
