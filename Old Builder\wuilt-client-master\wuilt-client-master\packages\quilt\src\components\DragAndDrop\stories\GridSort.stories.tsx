import React from "react";
import { ComponentStory, ComponentMeta } from "@storybook/react";
import { GridSort } from "../GridSort";
import { Heading } from "../../Heading";
import { Stack } from "../../Stack";
import { Box } from "../../Box";
import { Drag<PERSON>andle } from "../DragHandle";

// https://storybook.js.org/docs/react/essentials/controls
export default {
  title: "Components/Sorting/Grid",
  component: GridSort,
  argTypes: {
    value: { control: false },
    uniqueFieldName: { control: false },
    className: { control: false },
    boxProps: { control: false },
  },
} as ComponentMeta<typeof GridSort>;

const ITEMS = [
  { title: "item 1", id: "1" },
  { title: "item 2", id: "2" },
  { title: "item 3", id: "3" },
  { title: "item 4", id: "4" },
  { title: "item 5", id: "5" },
  { title: "item 6", id: "6" },
  { title: "item 7", id: "7" },
  { title: "item 8", id: "8" },
  { title: "item 9", id: "9" },
  { title: "item 10", id: "10" },
];

const Template: ComponentStory<typeof GridSort> = ({
  limitToContainerEdges,
  useHandleOnly,
  ...restProps
}) => {
  const [items, setItems] = React.useState(ITEMS);

  const handleChange = (sortedItems) => {
    setItems(sortedItems);
  };

  return (
    <GridSort
      {...restProps}
      useHandleOnly={useHandleOnly}
      value={items}
      onChange={handleChange}
    >
      {({ item }) => (
        <Box
          mb="10px"
          className="dnd-item"
          border="1px solid"
          borderColor="disabled"
          p="20px"
          bg="warning"
        >
          <Stack direction="row">
            <DragHandle id={item?.id} />
            <Heading>{item.title}</Heading>
          </Stack>
        </Box>
      )}
    </GridSort>
  );
};

export const ListSorting = Template.bind({});
ListSorting.args = {
  useHandleOnly: false,
};
