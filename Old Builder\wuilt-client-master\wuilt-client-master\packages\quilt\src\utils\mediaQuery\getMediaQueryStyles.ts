import kebabCase from "lodash/kebabCase";
import { css } from "styled-components";
import { DEVICES } from "./consts";
import { mediaQueries } from "./index";

type Arguments = {
  readonly smallMobile?: React.CSSProperties;
  readonly mediumMobile?: React.CSSProperties;
  readonly largeMobile?: React.CSSProperties;
  readonly tablet?: React.CSSProperties;
  readonly desktop?: React.CSSProperties;
  readonly largeDesktop?: React.CSSProperties;
};

function convertFromKamelCaseToKebabCase(obj?: React.CSSProperties) {
  if (!obj) return "";

  return Object.keys(obj).reduce(
    (acc, key) => `${acc}
		 ${[kebabCase(key)]}: ${obj[key]};`,
    ""
  );
}

export function getMediaQueryStyles(args: Arguments) {
  const CSSProperties = DEVICES.map((viewport) =>
    viewport in mediaQueries
      ? mediaQueries[viewport](css`
          ${convertFromKamelCaseToKebabCase(args[viewport])}
        `)
      : viewport === "largeDesktop" &&
        css`
          ${convertFromKamelCaseToKebabCase(args[viewport])}
        `
  );

  return CSSProperties;
}
