import React from "react";
import { ComponentMeta, ComponentStory } from "@storybook/react";
import { InputRange } from "./InputRange";
import { Field, Form, FormSubmit } from "../Form";

// https://storybook.js.org/docs/react/essentials/controls
export default {
  title: "Components/Form/InputRange",
  component: InputRange,

  argTypes: {},
} as ComponentMeta<typeof InputRange>;

const Template: ComponentStory<typeof InputRange> = ({ ...restProps }) => {
  const onSubmit = (values) => {
    console.log(values.range);
  };
  return (
    <Form onSubmit={onSubmit}>
      {({ formProps }) => (
        <form {...formProps}>
          <Field label="Range" name="range" defaultValue={50}>
            {({ fieldProps }) => <InputRange {...restProps} {...fieldProps} />}
          </Field>
          <FormSubmit>Submit</FormSubmit>
        </form>
      )}
    </Form>
  );
};

export const InputRangeStory = Template.bind({});
InputRangeStory.args = {};
