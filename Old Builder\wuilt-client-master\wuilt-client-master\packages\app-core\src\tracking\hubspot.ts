import { Attributes } from "./types";

export type IMethodParam =
  | "setPath"
  | "trackPageView"
  | "identify"
  | "trackEvent"
  | "revokeCookieConsent"
  | "addPrivacyConsentListener"
  | "doNotTrack"
  | "addIdentityListener"
  | "setContentType"
  | "refreshPageHandlers";

export type IPushParams = [IMethodParam, (string | object)?];

type PushFunction = (callParam: IPushParams) => void;

export type IPropsUseTrackingCode = {
  initialPath: string;
};

/**
 * The props to track event function
 */
export type IPropsUseSetTrackEvent = {
  eventId: string;
  value?: number | string;
};

export type IUseTrackingCode = {
  setContentType: (contentType: string) => void;
  setPathPageView: (path: string) => void;
  setIdentity: (email: string, customProperties?: any) => void;
  setTrackEvent: ({ eventId, value }: IPropsUseSetTrackEvent) => void;
};

declare global {
  interface Window {
    _hsq: { push: PushFunction };
    HubSpotConversations: any;
    hsConversationsOnReady: any;
    hsConversationsSettings: any;
  }
}

function initHubspotScript(portalId): IUseTrackingCode {
  if (!document.getElementById("hs-script-loader")) {
    const script = document.createElement("script");
    script.src = `https://js.hs-scripts.com/${portalId}.js`;
    script.async = true;
    script.type = "text/javascript";
    script.defer = true;
    script.id = "hs-script-loader";
    document.body.appendChild(script);
  }
  if (!document.getElementById("hs-meetings-embed-code")) {
    const script = document.createElement("script");
    script.src =
      "https://static.hsappstatic.net/MeetingsEmbed/ex/MeetingsEmbedCode.js";
    script.async = true;
    script.defer = true;
    script.type = "text/javascript";
    script.id = "hs-meetings-embed-code";
    document.body.appendChild(script);
  }
  const _hsq = (window._hsq = window._hsq || []);

  const setIdentity = (email: string, customProperties?: any) => {
    _hsq.push([
      "identify",
      {
        email,
        ...customProperties,
      },
    ]);
  };

  const setTrackEvent = ({ eventId, value }: IPropsUseSetTrackEvent) => {
    _hsq.push([
      "trackEvent",
      {
        id: eventId,
        value,
      },
    ]);
  };

  const setContentType = (contentType: string): void => {
    _hsq.push(["setContentType", contentType]);
  };

  const setPathPageView = (path: string): void => {
    _hsq.push(["setPath", path]);
    _hsq.push(["trackPageView"]);
  };

  return {
    setContentType,
    setPathPageView,
    setIdentity,
    setTrackEvent,
  };
}

export const createHubspotProvider = ({ config: { portalId }, user }) => {
  const hubspot = initHubspotScript(portalId);
  if (user) {
    hubspot.setIdentity(user.email, user);
  }
  return {
    setContext: (key: string, context?: Attributes) => {
      // Not implemented
    },
    pushError: (message: string, context?: Attributes) => {
      // We don't push errors to Amplitude
    },
    pushEvent: (name: string, attributes?: Attributes) => {
      // Don't need to push event from client for now
      // hubspot.setTrackEvent({ eventId: name, value: attributes });
    },
    view: (name: string, attributes?: Attributes) => {
      hubspot.setPathPageView(window.location.pathname);
    },
  };
};
