import * as React from "react";
import { Spinner } from "@wuilt/quilt";
import styled from "styled-components";

const alignItems = {
  top: "flex-start",
  center: "center",
  bottom: "flex-end",
};

const StyledContainer = styled.div<{ position; height }>`
  display: flex;
  width: 100%;
  height: ${({ height }) => height};
  justify-content: center;
  align-items: ${({ position }) => alignItems[position]};
`;

interface LoadingScreenProps {
  size?: "xsmall" | "small" | "medium" | "large";
  position?: "top" | "center" | "bottom";
  height?: string;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  size = "large",
  position = "center",
  height = "100vh",
}) => (
  <StyledContainer id="loading-screen" position={position} height={height}>
    <Spinner size={size} />
  </StyledContainer>
);
