import { DefaultTheme } from "styled-components";
import { ValidationState, StylesConfig } from "./types";
import { rtlSpacing } from "../../utils";

function gridSize() {
  return 6;
}

const ICON_PADDING = 2;

export default function baseStyles(
  validationState: ValidationState | undefined,
  isCompact: boolean,
  theme: DefaultTheme,
  bgColor: string | undefined
): StylesConfig {
  return {
    input: (css) => {
      return {
        ...css,
        padding: 0,
        color: "#252A31",
      };
    },
    menu: (css) => {
      return {
        ...css,
        cursor: "pointer",
        zIndex: 99,
        position: "absolute",
      };
    },
    container: (css, { isDisabled }) => ({
      ...css,
      pointerEvents: "all",
      cursor: isDisabled ? "not-allowed" : undefined,
      width: "100%",
    }),
    control: (css, { isFocused, isDisabled }) => {
      let backgroundColor = isFocused
        ? theme.palette.white.normal
        : theme.palette.white.normal;

      if (bgColor) {
        backgroundColor = bgColor;
      }

      if (isDisabled) {
        backgroundColor = theme.palette.cloud.dark;
      }

      return {
        ...css,
        pointerEvents: isDisabled ? "none" : undefined,
        backgroundColor,
        border: "none",
        outline: "none",
        boxShadow: "none",
        minHeight: isCompact ? gridSize() * 4 : 34,
        paddingTop: 0,
        fontSize: 14,
        paddingBottom: 0,
        msOverflowStyle: "-ms-autohiding-scrollbar",
        "::-webkit-scrollbar": {
          height: gridSize(),
          width: gridSize(),
        },
        "::-webkit-scrollbar-corner": {
          display: "none",
        },
        ":hover": {
          borderColor: theme.palette.ink.lighter,

          "::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(0,0,0,0.2)",
          },
          cursor: "pointer",
          backgroundColor: isFocused
            ? theme.palette.white.normal
            : theme.palette.white.normal,
        },
        "::-webkit-scrollbar-thumb:hover": {
          backgroundColor: "rgba(0,0,0,0.4)",
        },

        "@media (max-width: 576px)": {
          fontSize: 16,
        },
      };
    },
    valueContainer: (css) => ({
      ...css,
      height: 34,
      lineHeight: 1,
      paddingRight: 12,
      paddingLeft: 12,
      paddingBottom: isCompact ? 0 : 2,
      paddingTop: isCompact ? 0 : 2,
    }),
    clearIndicator: (css) => ({
      ...css,
      height: 34,
      color: theme.palette.ink.normal,
      paddingLeft: ICON_PADDING,
      paddingRight: ICON_PADDING,
      paddingBottom: isCompact ? 0 : 6,
      paddingTop: isCompact ? 0 : 6,
      ":hover": {
        color: theme.palette.ink.light,
      },
    }),
    loadingIndicator: (css) => ({
      ...css,
      paddingBottom: isCompact ? 0 : 6,
      paddingTop: isCompact ? 0 : 6,
    }),
    dropdownIndicator: (css, { isDisabled }) => {
      let color = theme.palette.ink.light;

      if (isDisabled) {
        color = theme.palette.cloud.light;
      }

      return {
        ...css,
        color,
        paddingLeft: ICON_PADDING,
        paddingRight: ICON_PADDING,
        paddingBottom: isCompact ? 0 : 6,
        paddingTop: isCompact ? 0 : 6,
        ":hover": {
          color: theme.palette.ink.normal,
        },
      };
    },
    indicatorsContainer: (css) => ({
      ...css,
      paddingRight: 12,
      paddingLeft: 12,
    }),
    indicatorSeparator: () => ({ display: "none" }),
    option: (css, { isFocused, isSelected, isDisabled }) => {
      let color;
      if (isDisabled) color = theme.palette.ink.normal;
      else if (isSelected) color = theme.palette.white.normal;

      let backgroundColor;
      if (isDisabled) backgroundColor = undefined;
      else if (isSelected) backgroundColor = theme.palette.product.normal;
      else if (isFocused) backgroundColor = theme.palette.product.light;

      const cursor = isDisabled ? "not-allowed" : undefined;

      return {
        ...css,
        // paddingTop: 6,
        // paddingBottom: 6,
        backgroundColor,
        color,
        cursor,
        ":active": {
          /* @ts-ignore */
          color: theme.palette.white.normal,
          backgroundColor: theme.palette.product.normalActive,
        },
      };
    },
    placeholder: (css) => ({
      ...css,
      color: theme.palette.ink.light,
      opacity: 0.5,
    }),
    singleValue: (css, { isDisabled }) => ({
      ...css,
      color: isDisabled ? theme.palette.ink.light : theme.palette.ink.normal,
      lineHeight: `${gridSize() * 2}px`, // 16px
      overflow: "none",
    }),
    menuList: (css) => ({
      ...css,
      paddingTop: gridSize(),
      paddingBottom: gridSize(),
    }),
    multiValue: (css) => ({
      ...css,
      borderRadius: "999px",
      overflow: "hidden",
      backgroundColor: theme.palette.cloud.light,
      color: theme.palette.ink.normal,
      maxWidth: "200px",
      lineHeight: "1",
    }),
    multiValueLabel: (css) => ({
      ...css,
      padding: rtlSpacing("8px 4px 8px 8px")({ theme }),
      fontSize: "14px",
    }),
    multiValueRemove: (css, { isFocused }) => ({
      ...css,
      backgroundColor: isFocused ? theme.palette.red.light : undefined,
      color: isFocused ? theme.palette.red.normal : undefined,
      paddingLeft: "5px",
      paddingRight: "5px",
      ":hover": {
        color: theme.palette.red.normal,
        backgroundColor: theme.palette.red.light,
      },
    }),
    menuPortal: (css) => ({ ...css, zIndex: 9999 }),
  };
}
