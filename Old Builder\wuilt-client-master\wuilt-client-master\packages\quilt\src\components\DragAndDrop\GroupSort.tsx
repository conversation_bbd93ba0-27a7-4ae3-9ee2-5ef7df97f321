import React, { useState } from "react";
import { arrayMove } from "@dnd-kit/sortable";
import { DragEndEvent, UniqueIdentifier } from "@dnd-kit/core";
import { VerticalSort, VerticalSortProps } from "./VerticalSort";

export type ItemMove = {
  id: string;
  newPosition: number;
};

interface GroupSortingProps<Type> extends VerticalSortProps<Type> {
  onChangeMoves: (moves: ItemMove[]) => Promise<void>;
  selectedIds: string[];
}

function GroupSort<Type>({
  value = [],
  onChange,
  children,
  onChangeMoves,
  selectedIds = [],
  uniqueFieldName = "id",
  ...restDndContextProps
}: GroupSortingProps<Type>) {
  const [isSorting, setIsSorting] = useState(false);
  const [draggedId, setDraggedId] = useState("");

  const filterItems = (item) => {
    if (draggedId === item?.id) return true;
    if (isSorting && selectedIds.includes(item?.id)) return false;
    return true;
  };

  const handleOnDragStart = ({ active }) => {
    const draggedId = active.id;
    setDraggedId(draggedId);
    setIsSorting(true);
    document.body.style.cursor = "grabbing";
  };

  // eslint-disable-next-line array-callback-return
  const disabledItems = value.map((item) => {
    if (
      selectedIds.length &&
      !selectedIds?.includes?.(item?.[uniqueFieldName])
    ) {
      return item?.[uniqueFieldName];
    }
  });

  const disabledItemsIds = disabledItems.filter(
    (itemId) => itemId !== undefined
  );

  const handleOnDragEnd = ({ active, over }: DragEndEvent) => {
    setDraggedId("");
    setIsSorting(false);
    document.body.style.cursor = "";
    let newItems: Type[];
    let moves: ItemMove[];

    const selectedItems = value.filter((item) =>
      selectedIds?.includes?.(item?.[uniqueFieldName])
    );

    if (active.id !== over?.id) {
      const oldIndex = active?.data?.current?.sortable?.index;
      const newIndex = over?.data?.current?.sortable?.index;

      if (selectedIds?.length) {
        const nonSelectedItems = value.filter(
          (item) => !selectedIds?.includes?.(item?.[uniqueFieldName])
        );
        newItems = [
          ...nonSelectedItems.slice(0, newIndex),
          ...selectedItems,
          ...nonSelectedItems.slice(newIndex, nonSelectedItems.length),
        ];

        moves = selectedItems.map((item, index) => ({
          id: item?.[uniqueFieldName],
          newPosition: newIndex + index,
        }));
      } else {
        newItems = arrayMove(value, oldIndex, newIndex);
        moves = [{ id: active.id as string, newPosition: newIndex }];
      }
      onChange?.(newItems, oldIndex, newIndex);
      onChangeMoves?.(moves);
    }
  };

  const handleDisableItem = (id: UniqueIdentifier) =>
    disabledItemsIds.includes(id);

  return (
    <VerticalSort
      value={value.filter(filterItems)}
      onDragEnd={handleOnDragEnd}
      onDragStart={handleOnDragStart}
      disableItem={disabledItemsIds.length ? handleDisableItem : () => false}
      limitToContainerEdges
      onChange={() => {}}
      {...restDndContextProps}
    >
      {children}
    </VerticalSort>
  );
}

export { GroupSort };
