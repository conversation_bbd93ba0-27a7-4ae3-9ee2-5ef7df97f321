import React from "react";
import styled from "styled-components";
import { Button } from "../Button";
import { ChevronDownIcon } from "../icons";

import { Popover } from "./Popover";
import { Stack } from "../Stack";

export default {
  title: "Components/Popover",
  component: Popover,
};

const selects = (
  <>
    <Stack align="center">
      <Stack spacing="none">tst</Stack>
    </Stack>
    <Stack align="center">
      <Stack spacing="none">tst</Stack>
    </Stack>
  </>
);

const StyledContentConatianer = styled.div`
  background: #f4f4f4;
  border: 1px solid #000;
  border-radius: 5px;
  padding: 10px;
  width: 300px;
`;

const content = (
  <StyledContentConatianer>
    <Stack>{selects}</Stack>
  </StyledContentConatianer>
);
const DropButton = ({ children }) => {
  const target = React.useRef<any>(null);
  const [opened, setOpened] = React.useState(false);
  return (
    <>
      <Button
        ref={target}
        color="primary"
        onClick={() => setOpened(!opened)}
        suffixIcon={<ChevronDownIcon />}
      >
        Open popover
      </Button>
      <Popover
        target={target}
        opened={opened}
        preferredAlign="center"
        preferredPosition="bottom"
      >
        {children}
      </Popover>
    </>
  );
};

export const Default = () => {
  const target = React.useRef<any>(null);
  const [opened, setOpened] = React.useState(false);
  return (
    <>
      <Button
        ref={target}
        color="primary"
        onClick={() => setOpened(!opened)}
        suffixIcon={<ChevronDownIcon />}
      >
        Open popover
      </Button>
      <Popover
        target={target}
        opened={opened}
        preferredAlign="center"
        preferredPosition="bottom"
      >
        {content}
      </Popover>
    </>
  );
};

const StyledScrollContainer = styled.div`
  height: 300px;
  padding: 20px;
  background: ${(props) => props.theme.palette.cloud.normal};
  overflow: auto;
`;
export const InScrollConteainer = () => {
  return (
    <StyledScrollContainer>
      <h1>Teest </h1>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
      <DropButton>{content}</DropButton>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
      <p>
        Lorem ipsum, dolor sit amet consectetur adipisicing elit. Ratione totam
        suscipit delectus ab eligendi officia quos, reprehenderit, unde quas
        deserunt tenetur, est placeat eum obcaecati aliquid in cum nihil harum.
      </p>
    </StyledScrollContainer>
  );
};
