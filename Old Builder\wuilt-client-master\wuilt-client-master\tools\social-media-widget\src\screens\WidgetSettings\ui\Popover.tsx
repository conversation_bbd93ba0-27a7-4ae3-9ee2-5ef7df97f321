import React, { FC, ReactNode, useState, useRef, useEffect } from "react";
import styled from "styled-components";
import { CloseIcon } from "@wuilt/quilt";
import { useIntl } from "react-intl";
interface PopoverProps {
  button: ReactNode;
  content: ReactNode;
  closeButton?: boolean;
  translateX?: number;
  withArrow?: boolean;
}

const StyledPopoverContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const StyledButtonWrapper = styled.div`
  cursor: pointer;
  width: 100%;
`;

const StyledPopoverContent = styled.div<{
  visible: boolean;
  withArrow: boolean;
  translateX?: number;
  locale: string;
}>`
  position: absolute;
  top: calc(100% + 10px);
  left: 50%;
  transform: translateX(-${({ translateX }) => translateX}%);
  display: ${({ visible }) => (visible ? "block" : "none")};
  background-color: #fff;
  box-shadow: rgba(14, 18, 22, 0.35) 0px 10px 38px -10px,
    rgba(14, 18, 22, 0.2) 0px 10px 20px -15px;
  border-radius: 8px;
  z-index: 1;
  ::before {
    content: "";
    display: ${({ withArrow }) => (withArrow ? "block" : "none")};
    position: absolute;
    top: -8px;
    left: ${({ translateX }) => translateX}%;
    transform: translateX(-50%);
    border-width: 0 8px 8px;
    border-style: solid;
    border-color: transparent transparent #fff;
  }
  .closeButton {
    cursor: pointer;
    position: absolute;
    top: 10px;
    right: ${({ locale }) => (locale === "ar" ? "auto" : "10px")};
    left: ${({ locale }) => (locale === "ar" ? "10px" : "auto")};
    font-size: 12px;
    color: #333;
  }
`;

export const Popover: FC<PopoverProps> = ({
  button,
  content,
  closeButton,
  translateX = 80,
  withArrow = true,
}) => {
  const [isVisible, setVisibility] = useState(false);
  const popoverContainerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLDivElement>(null);
  const { locale } = useIntl();

  const handleButtonClick = () => {
    setVisibility(!isVisible);
  };

  const handleCloseButtonClick = () => {
    setVisibility(false);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      popoverContainerRef.current &&
      buttonRef.current &&
      !popoverContainerRef.current.contains(event.target as Node) &&
      !buttonRef.current.contains(event.target as Node)
    ) {
      setVisibility(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <StyledPopoverContainer ref={popoverContainerRef}>
      <StyledButtonWrapper ref={buttonRef} onClick={handleButtonClick}>
        {button}
      </StyledButtonWrapper>
      <StyledPopoverContent
        withArrow={withArrow}
        translateX={translateX}
        visible={isVisible}
        locale={locale.substring(0, 2)}
      >
        {closeButton && (
          <div className="closeButton" onClick={handleCloseButtonClick}>
            <CloseIcon customColor="#667085" />
          </div>
        )}
        {content}
      </StyledPopoverContent>
    </StyledPopoverContainer>
  );
};
