import React from "react";
import styled, { css } from "styled-components";
import cuid from "cuid";
import { CheckCheckboxIcon, HyphenIcon } from "../icons";
import { rtlSpacing } from "../../utils/rtl";
import { Global } from "../../common/types";

// import {useFeatures} from '../../utilities/features';
// import {useToggle} from '../../utilities/use-toggle';
// import {useUniqueId} from '../../utilities/unique-id';
// import {Choice, helpTextID} from '../Choice';
// import {errorTextID} from '../InlineError';
// import {Icon} from '../Icon';
// import {Error, Key, CheckboxHandles} from '../../types';

type FontWeightType = "bold" | "normal";

export interface CheckboxProps extends Global {
  // Indicates the ID of the element that describes the checkbox
  ariaDescribedBy?: string;
  /** Label for the checkbox */
  label?: React.ReactNode;
  /** Visually hide the label */
  labelHidden?: boolean;
  /** Checkbox is selected. `indeterminate` shows a horizontal line in the checkbox */
  value?: boolean | "indeterminate";
  /** Additional text to aide in use */
  helpText?: React.ReactNode;
  /** Disable input */
  isDisabled?: boolean;
  /** ID for form input */
  id?: string;
  /** Name for form input */
  name?: string;
  /** Display an error message */
  error?: Error | boolean;
  /** Choose the label font weight */
  fontWeight?: FontWeightType;
  /** Callback when checkbox is toggled */
  onChange?(newChecked: boolean, id: string): void;
  /** Callback when checkbox is focussed */
  onFocus?(): void;
  /** Callback when focus is removed */
  onBlur?(): void;
  style?: React.CSSProperties;
}

const StyledControl = styled.span`
  display: flex;
  box-sizing: border-box;
  flex: 0 0 auto;
  align-items: stretch;
  width: 16px;
  height: 16px;
  margin-top: 0;

  margin: ${rtlSpacing("0 6px 0 0")};

  > * {
    width: 100%;
  }
`;
const StyledLabel = styled.label<{
  fontWeight: FontWeightType;
  isDisabled: boolean;
}>`
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  user-select: none;
  line-height: 16px;
  cursor: ${({ isDisabled }) => (isDisabled ? "not-allowed" : "pointer")};

  ${({ fontWeight }) =>
    fontWeight === "bold"
      ? css`
          font-weight: ${({ theme }) => theme.base.fontWeight.semiBold};
        `
      : css`
          font-weight: ${({ theme }) => theme.base.fontWeight.normal};
        `};
`;

const StyledWrapper = styled.div`
  position: relative;
  border: 2px solid #c4cdd5;
  border-radius: 4px;

  &:active,
  :focus {
    outline: none;
    box-shadow: 0 0 2px #00a991;
  }
`;

const StyledInput = styled.input`
  position: absolute !important;
  top: 0;
  clip: rect(1px, 1px, 1px, 1px) !important;
  overflow: hidden !important;
  height: 1px !important;
  width: 1px !important;
  padding: 0 !important;
  border: 0 !important;
`;

const StyledBackdrop = styled.span`
  box-sizing: border-box;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  pointer-events: none;
  position: relative;
  display: block;
  width: 100%;
  height: 100%;

  background-color: ${(props) => props.theme.palette.product.normal};
  box-shadow: 0 0 0 1px transparent, shadow(faint);
  border-radius: 4px;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: block;
    background: linear-gradient(
      to bottom,
      ${(props) => props.theme.palette.white.normal},
      ${(props) => props.theme.palette.cloud.dark}
    );
    border-radius: 2px;
  }
`;

const StyledIconContainer = styled.span`
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  color: ${(props) => props.theme.palette.white.normal};
  background-color: ${(props) => props.theme.palette.product.normal};
  border: 2px solid ${(props) => props.theme.palette.product.lightActive};
  border-radius: 4px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: 50% 50%;
  pointer-events: none;
  transform: translate(-50%, -50%) scale(0.25);
  opacity: 0;
  width: 16px;
  height: 16px;

  ${StyledInput}:checked ~& {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
`;

const Checkbox = React.forwardRef(function Checkbox(
  {
    label,
    dataTest,
    isDisabled = undefined,
    id: idProp,
    name,
    value = false,
    error,
    fontWeight = "normal",
    onChange,
    onFocus,
    onBlur,
    style,
  }: CheckboxProps,
  ref
) {
  const inputNode = React.useRef<HTMLInputElement>(null);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const id = React.useMemo(() => idProp || cuid(), []);
  const [keyFocused, setKeyFocused] = React.useState(false);
  // const {
  //   value: mouseOver,
  //   setTrue: handleMouseOver,
  //   setFalse: handleMouseOut,
  // } = useToggle(false);

  React.useImperativeHandle(ref, () => ({
    focus: () => {
      if (inputNode.current) {
        inputNode.current.focus();
      }
    },
  }));

  const handleBlur = () => {
    onBlur && onBlur();
    setKeyFocused(false);
  };

  const handleInput = (event?) => {
    event && event?.stopPropagation();
    if (onChange == null || inputNode.current == null || isDisabled) {
      return;
    }
    onChange(!inputNode.current.checked, id);
    inputNode.current.focus();
  };

  const handleKeyUp = () => {
    !keyFocused && setKeyFocused(true);
  };

  const isIndeterminate = value === "indeterminate";
  const isChecked = isIndeterminate || Boolean(value);

  const indeterminateAttributes = isIndeterminate
    ? { indeterminate: "true", "aria-checked": "mixed" as const }
    : { "aria-checked": isChecked };

  const Icon = isIndeterminate ? (
    <HyphenIcon size="md" />
  ) : (
    <CheckCheckboxIcon size="xxs" />
  );

  return (
    <StyledLabel
      data-test={dataTest}
      fontWeight={fontWeight}
      isDisabled={isDisabled || false}
      onClick={handleInput}
      onKeyDown={(event) => {
        if (event?.key === " ") {
          handleInput();
        }
      }}
      style={style}
    >
      <StyledControl>
        <StyledWrapper tabIndex={1}>
          <StyledInput
            onKeyUp={handleKeyUp}
            ref={inputNode}
            id={id}
            name={name}
            value={`${value}`}
            type="checkbox"
            checked={isChecked}
            onFocus={onFocus}
            onBlur={handleBlur}
            onClick={stopPropagation}
            onChange={noop}
            aria-invalid={error != null}
            role="checkbox"
            disabled={isDisabled || undefined}
            {...indeterminateAttributes}
            tabIndex={-1}
          />
          <StyledBackdrop />
          <StyledIconContainer>{Icon}</StyledIconContainer>
        </StyledWrapper>
      </StyledControl>
      {label}
    </StyledLabel>
  );
});

function noop() {}

function stopPropagation<E>(event: React.MouseEvent<E>) {
  event.stopPropagation();
}

Checkbox.displayName = "Checkbox";
export { Checkbox };
