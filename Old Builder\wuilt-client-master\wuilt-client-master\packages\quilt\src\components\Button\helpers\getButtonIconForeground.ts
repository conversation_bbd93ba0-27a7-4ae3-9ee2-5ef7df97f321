import { ButtonStyles } from "./getButtonStyles";

type GetButtonIconForeground = (buttonStyles: ButtonStyles) => {
  foreground: string;
  foregroundHover: string;
  foregroundActive: string;
  foregroundFocus: string;
};

const getButtonIconForeground: GetButtonIconForeground = (buttonStyles) => {
  return {
    foreground: buttonStyles.foreground,
    foregroundHover: buttonStyles.foregroundHover,
    foregroundActive: buttonStyles.foregroundActive,
    foregroundFocus: buttonStyles.foregroundActive,
  };
};

export default getButtonIconForeground;
