import React, { useEffect, useState } from "react";
import styled, { css } from "styled-components";
import { CancelIcon, CheckIcon } from "../icons";
import { Stack, StackFlexStyles } from "../Stack";
import { Global } from "../../common/types";
import { Box } from "../Box";

export interface ToggleButtonProps extends Global, StackFlexStyles {
  value?: boolean;
  isDisabled?: boolean;
  hideIcons?: boolean;
  label?: React.ReactNode;
  disableColor?: string;
  activeColor?: string;
  sliderBoxShadow?: string;
  onChange?: (value: boolean) => void;
}

const StyledToggle = styled.span<{
  checked;
  isDisabled;
  disableColor;
  activeColor;
}>`
  cursor: pointer;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 40px;
  height: 21px;
  border-radius: 100px;
  padding: 2px 4px;

  &:active,
  :focus {
    outline: none;
    box-shadow: 0 0 3px ${({ checked }) => (checked ? "#6b778c" : "#00a991")};
  }

  ${({ checked, disableColor, activeColor }) =>
    checked ? `background: ${activeColor}` : `background: ${disableColor}`};

  ${({ isDisabled }) =>
    isDisabled &&
    css`
      filter: contrast(0.8);
      cursor: not-allowed;
    `}
`;

const StyledSlider = styled.span<{
  checked?: boolean;
  sliderBoxShadow?: string;
}>`
  position: absolute;
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 100px;
  background-color: #fff;
  transform-origin: 50% 50%;
  box-shadow: ${({ sliderBoxShadow }) => sliderBoxShadow};

  transition: all 100ms ease-in-out;

  ${({ theme, checked }) =>
    theme.rtl
      ? css`
          ${checked
            ? "transform: translateX(-100%); left: 18px;"
            : "transform: translateX(0); right:2px;"}
        `
      : css`
          ${checked
            ? "transform: translateX(100%); right: 18px;"
            : "transform: translateX(0); left:2px;"}
        `}
`;

const StyledCancel = styled.span`
  margin: 1px;
  svg {
    width: 10px;
    path {
      fill: #fff;
    }
  }
`;

const StyledCheck = styled.span`
  svg {
    width: 13px;
    color: #fff;
  }
`;

const StyledLabel = styled(Box)<{ isDisabled }>`
  ${({ isDisabled }) =>
    isDisabled &&
    css`
      filter: contrast(0.8);
    `}
`;

const ToggleButton: React.FC<ToggleButtonProps> = ({
  value,
  isDisabled,
  hideIcons = false,
  label,
  dataTest,
  disableColor = "#6b778c",
  activeColor = "#00a991",
  sliderBoxShadow,
  onChange,
  direction = "row",
  align = "center",
  ...stackStyles
}) => {
  const [checked, setChecked] = useState(value);

  useEffect(() => {
    setChecked(value);
  }, [value]);

  const handleClick = () => {
    if (isDisabled) {
      return;
    }
    if (onChange) onChange(!checked);
    setChecked((prev) => !prev);
  };

  return (
    <Stack
      direction={direction}
      align={align}
      spacing="condensed"
      {...stackStyles}
    >
      <StyledToggle
        disableColor={disableColor}
        activeColor={activeColor}
        tabIndex={1}
        data-test={dataTest}
        checked={checked}
        isDisabled={isDisabled}
        onClick={handleClick}
        onKeyDown={(event) => {
          if (event?.key === " ") {
            handleClick();
          }
        }}
      >
        {!hideIcons && (
          <StyledCheck>
            <CheckIcon />
          </StyledCheck>
        )}
        <StyledSlider sliderBoxShadow={sliderBoxShadow} checked={checked} />
        {!hideIcons && (
          <StyledCancel>
            <CancelIcon />
          </StyledCancel>
        )}
      </StyledToggle>
      {label && <StyledLabel isDisabled={isDisabled}>{label}</StyledLabel>}
    </Stack>
  );
};

ToggleButton.displayName = "ToggleButton";

export { ToggleButton };
