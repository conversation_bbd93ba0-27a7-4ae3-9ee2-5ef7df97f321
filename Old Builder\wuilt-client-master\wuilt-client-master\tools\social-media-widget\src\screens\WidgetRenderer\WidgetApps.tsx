import React from "react";
import {
  AnimationEnum,
  AppsEnum,
  TAppSettings,
  TWidgetSettings,
  WidgetOrientationEnum,
  WidgetPositionEnum,
} from "../../shared/types";
import styled, { css } from "styled-components";
import { BackgroundGradient, WidgetAppStyles, getAppRedirect } from "./utils";
import { AppsIcon } from "../../shared/IconsList";
import { Tooltip } from "./Tooltip";
import { AnimationBasisStyle, AnimationMapper } from "../../shared/animation";

interface WidgetAppsProps {
  maxHeight?: string;
  isOpen: boolean;
  settings: TWidgetSettings;
  onClose: () => void;
  openWhatsappWidget: () => void;
}

const WidgetApps: React.FC<WidgetAppsProps> = ({
  maxHeight,
  isOpen,
  settings,
  onClose,
  openWhatsappWidget,
}) => {
  const isSingleApp = settings.apps.length === 1;
  const isLeftSide =
    settings.appearance.display.position === WidgetPositionEnum.Left;
  const isVertical =
    settings.appearance.display.orientation === WidgetOrientationEnum.Vertical;
  const withText = settings.appearance.content.withText;
  const size = settings?.appearance?.style?.size;
  const animationName = settings?.appearance?.style?.animation;
  const color = settings?.apps?.[0]?.background?.color;

  const getTooltipPlacement = () => {
    if (
      settings.appearance.display.orientation === WidgetOrientationEnum.Vertical
    ) {
      return settings.appearance.display.position === WidgetPositionEnum.Right
        ? "left"
        : "right";
    }
    return "top";
  };

  const isWhatsappHaveAgents = (app: TAppSettings) => {
    return (
      app.name === AppsEnum.Whatsapp &&
      (app.whatsAppSettings?.agents?.length || 0) > 1
    );
  };

  const handleWhatsappAppClick = () => {
    onClose();
    openWhatsappWidget();
  };

  return (
    <StyledAppsWrapper
      maxHeight={maxHeight}
      settings={settings}
      isLeftSide={isLeftSide}
      isSingleApp={isSingleApp}
      isVertical={isVertical}
      isOpen={isOpen || isSingleApp}
      animationName={animationName}
      color={color}
    >
      {settings.apps.map((app) => {
        const Icon = AppsIcon[app.name];
        return (
          <Tooltip
            key={app.name}
            placement={getTooltipPlacement()}
            disabled={!app.onHoverText || withText}
            content={<StyledText fontSize={size}>{app.onHoverText}</StyledText>}
          >
            <StyledApp
              isSingleApp={isSingleApp}
              settings={settings}
              app={app}
              target="_blank"
              rel="noopener"
              href={
                !isWhatsappHaveAgents(app) ? getAppRedirect(app) : undefined
              }
              onClick={
                isWhatsappHaveAgents(app)
                  ? () => handleWhatsappAppClick()
                  : undefined
              }
            >
              <StyledStack>
                <Icon
                  size={withText ? `calc(${size} / 1.2)` : "100%"}
                  messengerThunderColor={app?.background?.color}
                />
                {withText && (
                  <StyledText fontSize={size}>{app.onHoverText}</StyledText>
                )}
              </StyledStack>
            </StyledApp>
          </Tooltip>
        );
      })}
    </StyledAppsWrapper>
  );
};

export default WidgetApps;

/**
 * Styles
 */

const transitionStyles = ({ isOpen, isVertical, isLeftSide }) => {
  return css`
    transition: opacity 200ms ease-in-out, transform 200ms ease-in-out;
    visibility: hidden;
    opacity: 0;
    width: 0;
    height: 0;

    transform: ${isVertical
      ? "translateY(20px)"
      : `translateX(${isLeftSide ? "-20px" : "20px"})`};

    ${isOpen &&
    css`
      visibility: visible;
      opacity: 1;
      width: auto;
      height: auto;
      transform: translate(0);
    `}
  `;
};

const StyledAppsWrapper = styled.div<{
  settings: TWidgetSettings;
  isSingleApp: boolean;
  isLeftSide: boolean;
  isVertical: boolean;
  isOpen: boolean;
  maxHeight?: string;
  animationName: AnimationEnum | "none";
  color: string;
}>`
  display: flex;
  flex-direction: ${({ isVertical }) =>
    isVertical ? "column-reverse" : "row"};
  flex-wrap: ${({ isVertical }) => (isVertical ? "wrap" : "wrap-reverse")};
  max-height: ${({ maxHeight }) => maxHeight || "90vh"};
  max-width: 90vw;
  ${transitionStyles}
  ${AnimationBasisStyle}
  ${({ isSingleApp, animationName }) =>
    isSingleApp && AnimationMapper[animationName]}
`;

const backgroundStyle = ({ app }: { app: TAppSettings }) => {
  const isGradient = app.background.gradient;
  const backgroundColor = app.background.color;
  return css`
    background: ${isGradient && `${BackgroundGradient},`} ${backgroundColor};
  `;
};

const StyledApp = styled.a<{
  settings: TWidgetSettings;
  app: TAppSettings;
  isSingleApp: boolean;
}>`
  ${WidgetAppStyles}
  ${backgroundStyle};
  margin: ${({ isSingleApp }) => isSingleApp && "0px"};
  margin-left: ${({ isSingleApp }) => isSingleApp && "0px"};
  margin-right: ${({ isSingleApp }) => isSingleApp && "0px"};
`;

const StyledStack = styled.div`
  color: white;
  display: flex;
  align-items: center;
  direction: ltr;
`;

const StyledText = styled.div<{ fontSize: string }>`
  font-size: ${({ fontSize }) => `calc(${fontSize} / 3)`};
  font-weight: 500;
  color: white;
  padding: 6px 8px;
`;
