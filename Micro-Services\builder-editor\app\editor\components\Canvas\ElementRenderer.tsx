'use client'

import { useState } from 'react'
import { 
  Box, 
  Text, 
  Image, 
  Button, 
  useColorModeValue 
} from '@chakra-ui/react'
import { motion } from 'framer-motion'
import { Element, useEditorStore } from '../../../lib/stores/editorStore'

interface ElementRendererProps {
  element: Element
  sectionId: string
}

export function ElementRenderer({ element, sectionId }: ElementRendererProps) {
  const [isHovered, setIsHovered] = useState(false)
  
  const {
    selectedElement,
    selectElement,
    currentBreakpoint
  } = useEditorStore()

  const isSelected = selectedElement?.id === element.id
  const borderColor = useColorModeValue('blue.400', 'blue.300')
  const hoverBorderColor = useColorModeValue('gray.300', 'gray.600')

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    selectElement(element)
  }

  const renderElementContent = () => {
    switch (element.type) {
      case 'text':
        return (
          <Text
            {...element.props}
            style={element.style}
          >
            {element.props.content || 'Click to edit text'}
          </Text>
        )
      
      case 'image':
        return (
          <Image
            src={element.props.src || '/placeholder-image.jpg'}
            alt={element.props.alt || 'Image'}
            {...element.props}
            style={element.style}
          />
        )
      
      case 'button':
        return (
          <Button
            {...element.props}
            style={element.style}
          >
            {element.props.text || 'Button'}
          </Button>
        )
      
      case 'video':
        return (
          <Box
            as="video"
            controls
            {...element.props}
            style={element.style}
          >
            <source src={element.props.src} type="video/mp4" />
            Your browser does not support the video tag.
          </Box>
        )
      
      case 'form':
        return (
          <Box
            p={4}
            border="1px solid"
            borderColor="gray.200"
            borderRadius="md"
            {...element.props}
            style={element.style}
          >
            <Text fontSize="sm" color="gray.500">
              Form Component - {element.props.formType || 'Contact Form'}
            </Text>
          </Box>
        )
      
      case 'map':
        return (
          <Box
            h="300px"
            bg="gray.100"
            border="1px solid"
            borderColor="gray.200"
            borderRadius="md"
            display="flex"
            alignItems="center"
            justifyContent="center"
            {...element.props}
            style={element.style}
          >
            <Text color="gray.500">
              Map Component - {element.props.location || 'Location'}
            </Text>
          </Box>
        )
      
      case 'social':
        return (
          <Box
            display="flex"
            gap={2}
            {...element.props}
            style={element.style}
          >
            {(element.props.platforms || ['facebook', 'twitter', 'instagram']).map((platform: string) => (
              <Button
                key={platform}
                size="sm"
                variant="outline"
                colorScheme="blue"
              >
                {platform}
              </Button>
            ))}
          </Box>
        )
      
      case 'custom':
        return (
          <Box
            p={4}
            border="2px dashed"
            borderColor="gray.300"
            borderRadius="md"
            textAlign="center"
            {...element.props}
            style={element.style}
          >
            <Text color="gray.500">
              Custom Element - {element.props.name || 'Unnamed'}
            </Text>
          </Box>
        )
      
      default:
        return (
          <Box
            p={4}
            bg="gray.50"
            border="1px solid"
            borderColor="gray.200"
            borderRadius="md"
            {...element.props}
            style={element.style}
          >
            <Text fontSize="sm" color="gray.500">
              Unknown element type: {element.type}
            </Text>
          </Box>
        )
    }
  }

  return (
    <motion.div
      layout
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <Box
        position="relative"
        border={
          isSelected 
            ? `2px solid ${borderColor}` 
            : isHovered 
            ? `1px solid ${hoverBorderColor}` 
            : 'none'
        }
        borderRadius={isSelected || isHovered ? 'md' : 'none'}
        cursor="pointer"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleClick}
        _hover={{
          boxShadow: isSelected ? 'none' : 'sm'
        }}
      >
        {/* Element selection indicator */}
        {isSelected && (
          <Box
            position="absolute"
            top="-8px"
            left="-2px"
            bg={borderColor}
            color="white"
            fontSize="xs"
            px={2}
            py={1}
            borderRadius="sm"
            zIndex={5}
          >
            {element.type}
          </Box>
        )}
        
        {/* Element content */}
        {renderElementContent()}
        
        {/* Hover overlay */}
        {isHovered && !isSelected && (
          <Box
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            bg="blue.50"
            opacity="0.1"
            pointerEvents="none"
            borderRadius="md"
          />
        )}
      </Box>
    </motion.div>
  )
}
