import React from "react";
import {
  Box,
  CustomizeYourButtonIcon,
  Heading,
  Stack,
  Text,
} from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import AppStyle from "./AppStyle";
import AppColors from "./AppColors";
import { useWidget } from "../../../context/widget-provider";
import PositionAndShift from "./PositionAndShift";
import DirectionAndDisplay from "./DirectionAndDisplay";
import WhereToShow from "./WhereToShow";

function CustomizeYourButton() {
  const { settings, update } = useWidget();
  const { appearance } = settings;

  return (
    <Box my="30px">
      <Stack mb="20px" direction="row" align="center">
        <CustomizeYourButtonIcon className="title-icon" />
        <Heading variant="h1" fontSize="xl" fontWeight="semiBold" font="inter">
          <FormattedMessage
            defaultMessage="Customize your button"
            id="qVsh0k"
          />
        </Heading>
      </Stack>
      <Text fontSize="md" fontWeight="semiBold">
        <FormattedMessage defaultMessage="Appearance" id="2GURQY" />
      </Text>
      <Stack
        spacing="comfy"
        tablet={{ direction: "column" }}
        my="16px"
        direction="row"
      >
        <AppStyle appearance={appearance} update={update} />
        <AppColors appearance={appearance} update={update} />
      </Stack>

      <Text fontSize="md" fontWeight="semiBold">
        <FormattedMessage defaultMessage="General" id="1iEPTM" />
      </Text>
      <Stack
        spacing="comfy"
        tablet={{ direction: "column" }}
        mt="16px"
        direction="row"
      >
        <Stack flex="1.3 1" width="100%">
          <PositionAndShift appearance={appearance} update={update} />
          <DirectionAndDisplay appearance={appearance} update={update} />
        </Stack>
        <Stack flex="1 1" width="100%">
          <WhereToShow appearance={appearance} update={update} />
        </Stack>
      </Stack>
    </Box>
  );
}

export default CustomizeYourButton;
