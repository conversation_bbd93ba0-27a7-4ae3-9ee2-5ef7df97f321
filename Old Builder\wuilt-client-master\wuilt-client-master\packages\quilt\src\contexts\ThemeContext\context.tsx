import React from "react";
import PropTypes from "prop-types";
import { ThemeContext } from "styled-components";
import { deepMerge } from "../../utils";

const ExtendableThemeContext: React.Context<any> & {
  Extend;
} = ThemeContext as any;

ExtendableThemeContext.Extend = ({ children, value }) => (
  <ThemeContext.Consumer>
    {(theme) => (
      <ThemeContext.Provider value={deepMerge(theme, value)}>
        {children}
      </ThemeContext.Provider>
    )}
  </ThemeContext.Consumer>
);

ExtendableThemeContext.Extend.propTypes = {
  children: PropTypes.node.isRequired,
  value: PropTypes.shape({}).isRequired,
};

export { ExtendableThemeContext as ThemeContext };
