import { css } from "styled-components";
import { system } from "styled-system";
import { left, right } from "../../utils";

const AlignmentValues = {
  left: left,
  right: right,
  center: "center",
};

export type TextAlignmentProps = { align?: keyof typeof AlignmentValues };

export const TextAlignmentFn = system({
  align: {
    property: "textAlign",
    transform: (value) => AlignmentValues[value],
  },
});

export const TextAlignmentCss = css<{ align?: string }>`
  ${({ align }) =>
    align &&
    css`
      text-align: ${AlignmentValues[align]};
    `}
`;
