import styled, { css, keyframes } from "styled-components";

type Props = {
  isDragging?: boolean;
  isSorting?: boolean;
};

const boxShadow = () => css`
  box-shadow: 0 0 0 calc(1px) rgba(63, 63, 68, 0.05),
    0 1px calc(3px) 0 rgba(34, 33, 81, 0.15);
`;

const boxShadowPickedUp = () => css`
  box-shadow: 0 0 0 calc(1px) rgba(63, 63, 68, 0.05),
    -1px 0 15px 0 rgba(34, 33, 81, 0.01), 0px 15px 15px 0 rgba(34, 33, 81, 0.25);
`;

const pop = () => keyframes`
  0% {
		${boxShadow()}
  }
  100% {
		${boxShadowPickedUp()}
  }
	`;

export const StyledSortableItem = styled.div<Props>`
  transform-origin: 0 0;
  touch-action: none;

  ${({ isSorting, isDragging }) =>
    isSorting &&
    !isDragging &&
    css`
      opacity: 0.85;
      z-index: 0;
    `}

  ${({ isDragging }) =>
    isDragging &&
    css`
      opacity: 1;
      z-index: 9999;
    `}

  .dnd-item {
    ${({ isSorting, isDragging }) =>
      isSorting &&
      !isDragging &&
      css`
        ${boxShadow}
      `}

    ${({ isDragging }) =>
      isDragging &&
      css`
        ${boxShadowPickedUp}
        animation: ${pop} 200ms cubic-bezier(0.18, 0.67, 0.6, 1.22);
      `}
  }
`;
