{"name": "@chakra-ui/theme", "version": "3.4.9", "description": "The default theme for chakra components", "keywords": ["theme", "theming", "ui mode", "ui"], "sideEffects": false, "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/chakra-ui/chakra-ui#readme", "license": "MIT", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.mjs", "types": "dist/types/index.d.ts", "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/chakra-ui/chakra-ui.git", "directory": "packages/components/theme"}, "bugs": {"url": "https://github.com/chakra-ui/chakra-ui/issues"}, "dependencies": {"@chakra-ui/theme-tools": "2.2.9", "@chakra-ui/utils": "2.2.5", "@chakra-ui/anatomy": "2.3.6"}, "peerDependencies": {"@chakra-ui/styled-system": ">=2.8.0"}, "devDependencies": {"@chakra-ui/styled-system": "2.12.3", "@chakra-ui/cli": "2.5.8"}, "exports": {".": {"import": {"types": "./dist/types/index.d.ts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/types/index.d.ts", "default": "./dist/cjs/index.cjs"}}, "./components": {"import": {"types": "./dist/types/components/index.d.ts", "default": "./dist/esm/components/index.mjs"}, "require": {"types": "./dist/types/components/index.d.ts", "default": "./dist/cjs/components/index.cjs"}}, "./foundations": {"import": {"types": "./dist/types/foundations/index.d.ts", "default": "./dist/esm/foundations/index.mjs"}, "require": {"types": "./dist/types/foundations/index.d.ts", "default": "./dist/cjs/foundations/index.cjs"}}, "./semantic-tokens": {"import": {"types": "./dist/types/semantic-tokens.d.ts", "default": "./dist/esm/semantic-tokens.mjs"}, "require": {"types": "./dist/types/semantic-tokens.d.ts", "default": "./dist/cjs/semantic-tokens.cjs"}}, "./styles": {"import": {"types": "./dist/types/styles.d.ts", "default": "./dist/esm/styles.mjs"}, "require": {"types": "./dist/types/styles.d.ts", "default": "./dist/cjs/styles.cjs"}}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit", "build:fast": "tsup src"}}