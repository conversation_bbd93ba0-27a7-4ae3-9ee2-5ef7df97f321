import React from "react";
// import { Align } from '../Form/types';
import { Stack, StackFlexStyles } from "../Stack";
import { HideBelowMobile, ShowBelowMobile } from "../Responsiveness";
import { DropMenu } from "../DropMenu";
import { Heading } from "../Heading";
import { Text } from "../Text";
import { MoreHorizIcon } from "../icons";
import { Thumbnail } from "../Thumbnail";

type Align = any;
const HeadingSizes = {
  large: "xxxl",
  medium: "xxl",
  small: "xl",
} as const;

type SizesType = keyof typeof HeadingSizes;
export interface PageHeaderProps {
  breadcrumbs?: React.ReactNode;
  title: React.ReactNode;
  hint?: React.ReactNode;
  description?: React.ReactNode;
  image?: string;
  primaryAction?: React.ReactNode;
  secondaryActions?: React.ReactNode[];
  moreIcon?: React.ReactNode;
  moreText?: React.ReactNode;
  size?: SizesType;
  className?: string;
  smallMobile?: StackFlexStyles;
  mediumMobile?: StackFlexStyles;
  largeMobile?: StackFlexStyles;
  tablet?: StackFlexStyles;
  desktop?: StackFlexStyles;
  largeDesktop?: StackFlexStyles;
  align?: Align;
  spreadSecondaryAction?: boolean;
  breadcrumbsSpacing?: StackFlexStyles["spacing"];
  contentSpacing?: StackFlexStyles["spacing"];
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  breadcrumbs,
  title,
  hint,
  description,
  image,
  primaryAction,
  secondaryActions,
  size = "medium",
  className,
  smallMobile,
  mediumMobile,
  largeMobile,
  tablet,
  desktop,
  largeDesktop,
  moreIcon = <MoreHorizIcon />,
  moreText = null,
  align = "end",
  spreadSecondaryAction = false,
  breadcrumbsSpacing = "extraTight",
  contentSpacing = "extraTight",
}) => {
  const DropDownButton = secondaryActions && (
    <DropMenu
      applyHoverEffect
      buttonProps={{
        suffixIcon: moreIcon,
        children: moreText,
        size: "small",
        compact: true,
      }}
      dataTest="button-header-drop-menu"
    >
      {secondaryActions?.map((action, index) => (
        <span key={index}>{action}</span>
      ))}
    </DropMenu>
  );

  return (
    <Stack
      className={className}
      direction="column"
      spaceAfter="large"
      smallMobile={smallMobile}
      mediumMobile={mediumMobile}
      largeMobile={largeMobile}
      tablet={tablet}
      desktop={desktop}
      {...largeDesktop}
    >
      <Stack direction="row" justify="between" align={align} spacing="none">
        <Stack spacing={breadcrumbsSpacing}>
          {breadcrumbs && breadcrumbs}
          <Stack direction="row" align="center">
            {image && <Thumbnail src={image} size="medium" />}
            <Stack spacing={contentSpacing}>
              <Stack direction="row" spacing="compact" align="center">
                <Heading fontWeight="semiBold" fontSize={HeadingSizes[size]}>
                  {title}
                </Heading>
                {hint && hint}
              </Stack>
              {description && <Text fontSize="medium">{description}</Text>}
            </Stack>
          </Stack>
        </Stack>
        <Stack direction="row" spacing="compact">
          <HideBelowMobile>
            {spreadSecondaryAction && (secondaryActions?.length || 0) > 1
              ? secondaryActions
              : secondaryActions?.length === 1
              ? secondaryActions[0]
              : DropDownButton}
            {primaryAction && primaryAction}
          </HideBelowMobile>
          <ShowBelowMobile>
            {spreadSecondaryAction && secondaryActions
              ? secondaryActions
              : DropDownButton}
            {!description &&
              !secondaryActions &&
              primaryAction &&
              primaryAction}
          </ShowBelowMobile>
        </Stack>
      </Stack>

      {(description || secondaryActions) && primaryAction && (
        <ShowBelowMobile>
          {React.cloneElement(primaryAction as React.ReactElement, {
            fullWidth: true,
            contentAlign: "center",
            contentWidth: "auto",
          })}
        </ShowBelowMobile>
      )}
    </Stack>
  );
};
