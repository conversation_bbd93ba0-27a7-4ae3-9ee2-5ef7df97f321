import React from "react";
import styled, { css } from "styled-components";
import { mediaQueries } from "../../utils";
import { useMediaQuery } from "../../hooks/useMediaQuery";

const mapDevicesName = {
  isMobile: "largeMobile",
  isTablet: "tablet",
  isDesktop: "desktop",
};

const StyledHide = styled.div<{ device: string }>`
  display: block;
  ${({ device }) => css`
    ${mediaQueries[mapDevicesName[device]](css`
      display: none;
    `)}
  `}
`;

const StyledShow = styled.div<{ device: string }>`
  display: none;
  ${({ device }) => css`
    ${mediaQueries[mapDevicesName[device]](css`
      display: block;
    `)}
  `}
`;

const HideComponent =
  (device: string) =>
  ({ children }) => {
    const isDevice = useMediaQuery[device]();
    if (isDevice === null) {
      return <StyledHide device={device}>{children}</StyledHide>;
    }
    return isDevice ? null : <React.Fragment>{children}</React.Fragment>;
  };

const ShowComponent =
  (device: string) =>
  ({ children }) => {
    const isDevice = useMediaQuery[device]();
    if (isDevice === null) {
      return <StyledShow device={device}>{children}</StyledShow>;
    }
    return isDevice ? <React.Fragment>{children}</React.Fragment> : null;
  };

export const HideBelowMobile = HideComponent("isMobile");
export const ShowBelowMobile = ShowComponent("isMobile");
export const HideBelowTablet = HideComponent("isTablet");
export const ShowBelowTablet = ShowComponent("isTablet");
export const HideBelowDesktop = HideComponent("isDesktop");
export const ShowBelowDesktop = ShowComponent("isDesktop");
