import React from "react";
import whiteListProps from "./whiteListProps";
import { Icon, IconProps } from "./Icon";

const createIcon = (
  def: React.ReactNode,
  viewBox: string,
  displayName: string
) => {
  const icon: React.FC<IconProps> = (props: IconProps) => (
    <Icon viewBox={viewBox} {...whiteListProps(props)}>
      {def}
    </Icon>
  );
  icon.displayName = displayName;
  return icon;
};

export default createIcon;
