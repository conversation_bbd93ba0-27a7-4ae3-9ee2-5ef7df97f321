import React from "react";
import { groupedCountries } from "./data/countries";
import Select from "./Select";

interface OptionType {
  abbr: string;
  code: number;
  icon: string;
  name: string;
}

const Opt = ({ children, icon }: any) => (
  <div>
    <span>{icon}</span>
    {children}
  </div>
);

// return the country name; used for searching
const getOptionLabel = (opt: OptionType) => opt.name;

// set the country's abbreviation for the option value, (also searchable)
const getOptionValue = (opt: OptionType) => opt.abbr;

// the text node of the control
const controlLabel = (opt: OptionType) => (
  <Opt icon={opt.icon}>{opt.abbr.toUpperCase()}</Opt>
);
// the text node for an option
const optionLabel = ({ abbr, code, icon, name }: OptionType) => (
  <Opt icon={icon}>
    {name} ({abbr.toUpperCase()}) +{code}
  </Opt>
);

// switch formatters based on render context (menu | value)
const formatOptionLabel = (opt: OptionType, { context }: any) =>
  context === "value" ? controlLabel(opt) : optionLabel(opt);

// put it all together
const CountrySelect = (props: any) => (
  <Select
    isClearable={false}
    formatOptionLabel={formatOptionLabel}
    getOptionLabel={getOptionLabel}
    getOptionValue={getOptionValue}
    isMulti={false}
    options={groupedCountries}
    styles={{
      container: (css) => ({ ...css, width: "100%" }),
      dropdownIndicator: (css) => ({ ...css, paddingLeft: 0 }),
      menu: (css) => ({ ...css, width: "100%" }),
    }}
    {...props}
  />
);

CountrySelect.displayName = "CountrySelect";
export default CountrySelect;
