import { TIconType } from "../types";

export function MessageChatSquareSolid({
  size = 20,
  width,
  height,
  color,
}: TIconType) {
  return (
    <svg
      width={width || size}
      height={height || size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.968 8.33325H15.699C16.1382 8.33324 16.5172 8.33322 16.8294 8.35873C17.1588 8.38564 17.4865 8.44507 17.8018 8.60574C18.2722 8.84542 18.6547 9.22787 18.8943 9.69828C19.055 10.0136 19.1144 10.3413 19.1414 10.6707C19.1669 10.9829 19.1668 11.3619 19.1668 11.8012V14.1218C19.1668 14.4874 19.1669 14.8028 19.149 15.0638C19.1303 15.3387 19.089 15.6136 18.9765 15.8852C18.7228 16.4978 18.2361 16.9845 17.6235 17.2382C17.3519 17.3507 17.077 17.3919 16.8021 17.4107C16.7586 17.4137 16.7134 17.4162 16.6668 17.4182V18.3333C16.6668 18.6435 16.4945 18.9281 16.2195 19.0718C15.9446 19.2155 15.6125 19.1946 15.3578 19.0175L13.5441 17.7565C13.2648 17.5623 13.2083 17.5269 13.1549 17.5027C13.0933 17.4748 13.0286 17.4545 12.9621 17.4423C12.9045 17.4317 12.8378 17.4285 12.4976 17.4285H10.9681C10.5288 17.4285 10.1498 17.4285 9.83764 17.403C9.50819 17.3761 9.1805 17.3167 8.86519 17.156C8.39478 16.9163 8.01233 16.5339 7.77265 16.0635C7.61199 15.7482 7.55256 15.4205 7.52564 15.091C7.50013 14.7789 7.50015 14.3999 7.50016 13.9606V11.8011C7.50015 11.3618 7.50013 10.9829 7.52564 10.6707C7.55256 10.3413 7.61199 10.0136 7.77265 9.69828C8.01233 9.22787 8.39478 8.84542 8.86519 8.60574C9.1805 8.44507 9.50819 8.38564 9.83764 8.35873C10.1498 8.33322 10.5288 8.33324 10.968 8.33325Z"
        fill={color || "currentColor"}
      />
      <path
        d="M11.8679 0.833252H5.63244C4.96164 0.833242 4.40799 0.833233 3.957 0.870081C3.48859 0.908352 3.058 0.990489 2.65355 1.19656C2.02635 1.51614 1.51641 2.02608 1.19683 2.65328C0.990757 3.05773 0.90862 3.48832 0.870349 3.95673C0.833501 4.40773 0.83351 4.96136 0.833521 5.63217L0.833508 9.60063C0.833397 9.96865 0.833315 10.2413 0.868716 10.483C1.08216 11.9402 2.22657 13.0846 3.68375 13.2981C3.74332 13.3068 3.78047 13.3291 3.79648 13.3425L3.79648 14.6573C3.79645 14.8845 3.79642 15.1073 3.81227 15.2865C3.82634 15.4457 3.86322 15.7754 4.09749 16.0487C4.35863 16.3533 4.75012 16.514 5.14998 16.4808C5.50867 16.451 5.7666 16.2423 5.88846 16.139C5.92318 16.1095 5.95914 16.0774 5.99607 16.0432C5.91892 15.7426 5.8839 15.4638 5.86453 15.2268C5.83331 14.8446 5.83341 14.4031 5.83351 13.9959V11.7659C5.83341 11.3587 5.83331 10.9172 5.86453 10.535C5.89987 10.1025 5.98733 9.53108 6.28766 8.94165C6.68713 8.15764 7.32455 7.52022 8.10856 7.12075C8.69799 6.82042 9.26942 6.73296 9.70194 6.69762C10.0841 6.66639 10.5256 6.6665 10.9328 6.6666H15.7342C16.039 6.66652 16.363 6.66645 16.6669 6.6795V5.63215C16.6669 4.96135 16.6669 4.40772 16.63 3.95673C16.5918 3.48832 16.5096 3.05773 16.3035 2.65328C15.984 2.02608 15.474 1.51614 14.8468 1.19656C14.4424 0.990489 14.0118 0.908352 13.5434 0.870081C13.0924 0.833233 12.5387 0.833242 11.8679 0.833252Z"
        fill={color || "currentColor"}
      />
    </svg>
  );
}
