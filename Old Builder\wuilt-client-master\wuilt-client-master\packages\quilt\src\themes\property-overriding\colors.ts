import { compose, opacity, OpacityProps, system } from "styled-system";
import { theme } from "../theme";

// type KeyedTuple<T> = { [K in keyof T]: [K, T[K]] }[keyof T];
type TypeOfPalette = typeof theme.palette;
type KeyOfTypeOfPalette = keyof TypeOfPalette;
type ColorsPalette = {
  [Property in KeyOfTypeOfPalette]?: keyof TypeOfPalette[Property];
};

export type ColorsValues = keyof typeof theme.base.colors;

export type ColorsType = ColorsPalette | ColorsValues;

export type TextColorProps = {
  color?: ColorsType;
  textColor?: ColorsType;
};

export type BackgroundColorProps = {
  bg?: ColorsType;
  bgColor?: ColorsType;
  backgroundColor?: ColorsType;
};

const background = system({
  backgroundColor: {
    property: "backgroundColor",
    transform: getColorValue,
  },
  bgColor: {
    property: "backgroundColor",
    transform: getColorValue,
  },
  bg: {
    property: "backgroundColor",
    transform: getColorValue,
  },
});

const textColor = system({
  color: {
    property: "color",
    transform: getColorValue,
  },
  textColor: {
    property: "color",
    transform: getColorValue,
  },
});

export function getColorValue(value: ColorsType) {
  if (!value) return;
  if (typeof value === "string") return theme.base.colors[value];
  const mainColor = Object.keys(value)[0];
  const variantColor = value[mainColor];
  return theme.palette[mainColor][variantColor];
}

export function modifyColorProp(
  propName: string,
  value: { [key: string]: string } | string
) {
  if (!value) return {};
  return typeof value === "string"
    ? { [propName]: value }
    : { [propName]: { color: value } };
}

export type ColorProps = TextColorProps & BackgroundColorProps & OpacityProps;
export const Color = (props) => {
  const modifiedColorProps = {
    ...props,
    ...modifyColorProp("color", props.color),
    ...modifyColorProp("textColor", props.textColor),
    ...modifyColorProp("bg", props.bg),
    ...modifyColorProp("bgColor", props.bgColor),
    ...modifyColorProp("backgroundColor", props.backgroundColor),
  };
  return compose(background, opacity, textColor)(modifiedColorProps);
};
