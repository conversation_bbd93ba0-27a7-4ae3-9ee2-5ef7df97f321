import React, {
  cloneElement,
  createContext,
  ReactElement,
  ReactNode,
  useContext,
  useMemo,
  useState,
} from "react";
import {
  DndContext,
  MouseSensor,
  useDraggable,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  useHover,
  useFocus,
  useClick,
  useDismiss,
  useInteractions,
  autoUpdate,
  flip,
  offset,
  shift,
  useFloating,
  Placement,
  FloatingPortal,
  useMergeRefs,
} from "@floating-ui/react";
import { DraggablePopup, PopupChildrenArgs } from "./DraggablePopup";
import { Box, BoxProps } from "../Box";
import { ButtonIcon } from "../ButtonIcon";
import { CloseIcon } from "../icons";
// import { StyleSheetManager } from "styled-components";
// import { CacheProvider } from "@emotion/react";

// TODO: revert iframe portals
// import createCache from "@emotion/cache";
// import { prefixer } from "stylis";
// import { useClickOutside } from "../../hooks";

// const cache = createCache({
//   key: "wf",
//   container: window?.parent?.document.head,
//   stylisPlugins: [prefixer],
// });

const PopupContext = createContext({
  open: false,
  toggleOpen: () => {},
});

export interface PopupHeaderProps extends BoxProps {
  children: ReactNode;
  hideCloseButton?: boolean;
  closeButtonControls?: BoxProps;
  headerContentControls?: BoxProps;
}
export interface PopupBodyProps extends BoxProps {
  children: ReactNode;
}

export type Coordinates = { x?: number; y?: number };

export interface PopupProps {
  children: ReactNode | ((args: PopupChildrenArgs) => ReactNode);
  activator?: ReactElement;
  placement?: Placement;
  onEvent?: "onClick" | "onHover";
  preserveLocation?: boolean;
  referToElement?: boolean;
  open?: boolean;
  dontCLoseWhenClickingOutside?: boolean;
  dontCloseWhenPressingEscape?: boolean;
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  onPopupClose?: () => void;
  onPopupOpen?: () => void;
}

// TODO: revert iframe portals
// export function CanvasOuterWrapper({ children }) {
//   return (
//     <StyleSheetManager target={window?.parent?.document?.body || undefined}>
//       <CacheProvider value={cache}>{children}</CacheProvider>
//     </StyleSheetManager>
//   );
// }

const Popup: React.FC<PopupProps> & {
  Header: React.FC<PopupHeaderProps>;
  Body: React.FC<PopupBodyProps>;
} = ({
  children,
  activator,
  placement,
  onEvent,
  preserveLocation,
  referToElement,
  open: openProp,
  dontCLoseWhenClickingOutside,
  dontCloseWhenPressingEscape,
  setOpen: setOpenProp,
  onPopupOpen: onPopupOpenProp,
  onPopupClose: onPopupCloseProp,
}) => {
  const parentRoot = undefined as any; //window?.parent?.document.body; // TODO: revert iframe portals
  const [openLocal, setOpenLocal] = useState(false);
  const [coordinates, setCoordinates] = useState<Coordinates>({});
  const open = openProp ?? openLocal;
  const setOpen = setOpenProp ?? setOpenLocal;

  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: { distance: 5 },
  });
  const sensors = useSensors(mouseSensor);

  const toggleOpenState = () => {
    if (open) onPopupClose();
    else onPopupOpen();
  };

  const { x, y, strategy, context, refs } = useFloating({
    placement,
    open,
    onOpenChange: toggleOpenState,
    middleware: [offset(5), flip(), shift({ padding: 8 })],
    whileElementsMounted: autoUpdate,
  });

  // eslint-disable-next-line react-hooks/rules-of-hooks
  const hoverInteractions = () => [useHover(context), useFocus(context)];
  const clickInteractions = () => [
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useClick(context),
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useDismiss(context, {
      outsidePress: !dontCLoseWhenClickingOutside,
      escapeKey: !dontCloseWhenPressingEscape,
    }),
  ];
  const interactions =
    onEvent === "onHover" ? hoverInteractions() : clickInteractions();
  const { getFloatingProps, getReferenceProps } = useInteractions(interactions);

  const ref = useMergeRefs([refs.setReference, (activator as any)?.ref]);

  const getDefaultCoordinates = () => {
    const relevantWindow = parentRoot?.ownerDocument?.defaultView || window;
    const windowCenter = {
      x: relevantWindow.pageXOffset + relevantWindow.innerWidth / 2,
      y: relevantWindow.pageYOffset + relevantWindow.innerHeight / 2,
    };
    const elementCenter = { x: x || 0, y: y || 0 };
    const defaultLocation = referToElement ? elementCenter : windowCenter;
    return defaultLocation;
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const defaultCoordinates = useMemo(getDefaultCoordinates, [open]);

  const onPopupOpen = () => {
    if (coordinates?.x == null && coordinates?.y == null) {
      setCoordinates(getDefaultCoordinates());
    }
    onPopupOpenProp?.();
    setOpen(true);
  };

  const onPopupClose = () => {
    if (!preserveLocation) setCoordinates({});
    onPopupCloseProp?.();
    setOpen(false);
  };

  // useClickOutside(refs.floating, onPopupClose); // TODO:

  const mergedOnClickFunctions = (event: any) => {
    const activatorOnClick = activator?.props?.onClick;
    const referenceOnClick = getReferenceProps()?.onClick as any;
    activatorOnClick?.(event);
    referenceOnClick?.(event);
  };

  return (
    <PopupContext.Provider value={{ open: open, toggleOpen: toggleOpenState }}>
      {!!activator &&
        cloneElement(activator, {
          ...activator?.props,
          ...getReferenceProps(),
          onClick: mergedOnClickFunctions,
          ref,
        })}
      <FloatingPortal root={parentRoot} id={parentRoot ? undefined : "popups"}>
        {open && (
          <div
            {...getFloatingProps()}
            ref={refs.setFloating}
            style={{
              position: strategy,
              left: coordinates?.x ?? defaultCoordinates.x,
              top: coordinates?.y ?? defaultCoordinates.y,
              zIndex: 990,
              transform: referToElement ? "unset" : "translate(-50%,-50%)",
            }}
          >
            <DndContext
              sensors={sensors}
              onDragEnd={({ delta }) => {
                setCoordinates((prev) => ({
                  x: (prev.x ?? defaultCoordinates.x) + delta.x,
                  y: (prev.y ?? defaultCoordinates.y) + delta.y,
                }));
              }}
            >
              {/*  TODO: revert iframe portals */}
              {/* {parentRoot ? (
                <CanvasOuterWrapper>
                  <DraggablePopup closePopup={toggleOpenState}>
                    {children}
                  </DraggablePopup>
                </CanvasOuterWrapper>
              ) : ( */}
              <DraggablePopup closePopup={toggleOpenState}>
                {children}
              </DraggablePopup>
            </DndContext>
          </div>
        )}
      </FloatingPortal>
    </PopupContext.Provider>
  );
};

Popup.Header = ({
  children,
  hideCloseButton,
  closeButtonControls,
  headerContentControls,
  ...boxProps
}: PopupHeaderProps) => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { toggleOpen } = useContext(PopupContext);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { attributes, listeners } = useDraggable({ id: "draggable-popup" });
  const dragProps = { ...listeners, ...attributes };
  if (!children) return null;
  return (
    <div {...dragProps}>
      <Box
        p="18px"
        cursor="move"
        bg="secondary"
        color="white"
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        {...boxProps}
      >
        <Box
          flex="1"
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          {...headerContentControls}
        >
          {children}
        </Box>
        {!hideCloseButton && (
          <Box
            display="flex"
            alignContent="center"
            justifyContent="space-between"
            {...closeButtonControls}
            style={{ paddingInlineStart: "20px" }}
          >
            <ButtonIcon
              onClick={toggleOpen}
              dataTest="button-close"
              onlyIcon
              transparent
            >
              <CloseIcon color="white" size="lg" />
            </ButtonIcon>
          </Box>
        )}
      </Box>
    </div>
  );
};
Popup.Header.displayName = "Popup.Header";
Popup.Body = ({ children, ...boxProps }: PopupBodyProps) => {
  if (!children) return null;
  return (
    <Box bg="white" p="16px" maxHeight="70vh" overflowY="auto" {...boxProps}>
      {children}
    </Box>
  );
};
Popup.Body.displayName = "Popup.Body";

export { Popup };
