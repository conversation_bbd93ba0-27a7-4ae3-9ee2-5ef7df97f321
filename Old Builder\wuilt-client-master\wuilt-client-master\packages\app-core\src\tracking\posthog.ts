import posthog from "posthog-js";
import { PostHogProvider as ReactPostHogProvider } from "posthog-js/react";
import { Attributes } from "./types";

export const PostHogProvider = ReactPostHogProvider;
export const posthogClient = posthog;

export const createPostHogProvider = ({ config: { token, apiHost }, user }) => {
  if (user) {
    posthog.init(token, {
      api_host: apiHost,
    });

    posthog.identify(user.id, {
      id: user.id,
      email: user.email,
      name: user.name,
    });
  }

  return {
    setContext: (key: string, context?: Attributes) => {
      if (!context) {
        // remove context
        // return;
      }
      // add context
    },
    pushEvent: (name: string, attributes?: Attributes) => {
      posthog.capture(name, attributes);
    },
    view: (name: string, attributes?: Attributes) => {},
    pushError: (error: unknown, context?: Attributes) => {},
  };
};
