import React from "react";
import styled, { css } from "styled-components";
import { ColorProps, Color } from "../../themes/property-overriding";
import { Illustration_SIZES } from "./consts";

export const getSize = (size) => () => {
  return Illustration_SIZES[size] || Illustration_SIZES.medium;
};

const reverse = ({ reverseOnRtl, theme }) =>
  reverseOnRtl &&
  theme.rtl &&
  css`
    transform: scale(-1, 1);
  `;

const StyledIllustration = styled(
  ({
    className,
    viewBox,
    dataTest,
    children,
    ariaHidden,
    ariaLabel,
    title,
  }) => (
    <svg
      className={className}
      viewBox={viewBox}
      data-test={dataTest}
      preserveAspectRatio="xMidYMid meet"
      aria-hidden={ariaHidden ? "true" : undefined}
      aria-label={ariaLabel}
    >
      {title && <title>{title}</title>}
      {children}
    </svg>
  )
)`
  width: ${({ size, width }) => width || getSize(size)};
  height: ${({ size, height }) => height || getSize(size)};
  flex-shrink: 0; /* prevent shrinking when used in flex-box */
  vertical-align: middle;
  fill: currentColor;
  ${Color}
  ${({ customColor }) => customColor && `color:${customColor}`};
  ${reverse};
`;

export interface IllustrationProps {
  size?: keyof typeof Illustration_SIZES;
  width?: string;
  height?: string;
  color?: ColorProps["color"];
  customColor?: string;
  className?: string;
  children?: React.ReactNode;
  viewBox?: string;
  dataTest?: string;
  ariaHidden?: string;
  reverseOnRtl?: boolean;
  ariaLabel?: string;
  title?: React.ReactNode;
}

const Illustration: React.FC<IllustrationProps> = (props) => {
  const {
    size,
    width,
    height,
    className,
    children,
    viewBox,
    dataTest,
    ariaHidden,
    reverseOnRtl,
    ariaLabel,
    color,
    customColor,
    title,
  } = props;
  return (
    <StyledIllustration
      viewBox={viewBox}
      size={size}
      width={width}
      height={height}
      className={className}
      dataTest={dataTest}
      ariaHidden={ariaHidden}
      reverseOnRtl={reverseOnRtl}
      ariaLabel={ariaLabel}
      customColor={customColor}
      color={color}
      title={title}
    >
      {children}
    </StyledIllustration>
  );
};

Illustration.defaultProps = {
  size: "medium",
};

export { Illustration };
