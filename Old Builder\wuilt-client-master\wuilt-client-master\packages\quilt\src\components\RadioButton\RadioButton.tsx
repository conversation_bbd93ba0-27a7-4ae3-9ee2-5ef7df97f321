import React, { useRef, useContext } from "react";
import styled from "styled-components";
import { Stack, StackFlexStyles } from "../Stack";
import { Label } from "../Label";
import { color as colorFn } from "../../utils";
import { RadioContext } from "./RadioGroup";
import { Global } from "../../common/types";
import { theme } from "../../themes";

type ColorProps = keyof typeof theme.base.colors;

export interface RadioButtonProps
  extends Global,
    Pick<StackFlexStyles, "align"> {
  value: string | number;
  label: React.ReactNode;
  isDisabled?: boolean;
  color?: ColorProps;
  childrenOrientation?: "horizontal" | "vertical";
  children?: (args: { isSelected: boolean }) => React.ReactNode;
}

const RadioButton: React.FC<RadioButtonProps> = ({
  label,
  value,
  isDisabled,
  color = "primary",
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  childrenOrientation = "vertical",
  align = "center",
  children,
  dataTest,
}) => {
  const inputNodeRef = useRef<HTMLInputElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);

  const { name, value: selectedValue, onChange } = useContext(RadioContext);

  React.useEffect(() => {
    if (value === selectedValue) {
      if (inputNodeRef.current) inputNodeRef.current.click();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedValue]);

  const handleClick = () => {
    if (isDisabled) {
      return;
    }
    if (inputNodeRef.current) inputNodeRef.current.click();
    if (wrapperRef.current) wrapperRef.current.focus();
    if (onChange) onChange(value);
  };

  return (
    <div>
      <StyledContainer
        data-test={dataTest}
        onClick={handleClick}
        isDisabled={isDisabled || false}
      >
        <Stack direction="row" align={align} inline spacing="condensed">
          <StyledInputWrapper
            ref={wrapperRef}
            tabIndex={1}
            color={color}
            isDisabled={isDisabled}
            isChecked={value === selectedValue}
            onClick={stopPropagation}
            onChange={() => {}}
            onKeyDown={(event) => {
              if (event?.key === " ") {
                handleClick();
              }
            }}
          >
            <StyledInput
              type="radio"
              ref={inputNodeRef}
              tabIndex={-1}
              id={value.toString()}
              name={name}
              value={value}
            />
            <StyledRadio color={color} />
          </StyledInputWrapper>
          <StyledLabel htmlFor={value.toString()} isDisabled={isDisabled}>
            {label}
          </StyledLabel>
        </Stack>
      </StyledContainer>
      {children &&
        children({
          isSelected: value === selectedValue,
        })}
    </div>
    // </Stack>
  );
};

function stopPropagation<E>(event: React.MouseEvent<E>) {
  event.stopPropagation();
}

RadioButton.displayName = "RadioButton";
export { RadioButton };

/**
 *
 *
 * Styles
 *
 *
 */

const StyledContainer = styled.div<{
  isDisabled: boolean;
}>`
  cursor: ${({ isDisabled }) => (isDisabled ? "not-allowed" : "pointer")};
`;

const StyledInput = styled.input`
  position: absolute !important;
  top: 0;
  clip: rect(1px, 1px, 1px, 1px) !important;
  overflow: hidden !important;
  height: 1px !important;
  width: 1px !important;
  padding: 0 !important;
  border: 0 !important;
`;

const StyledInputWrapper = styled.div<{
  isDisabled?: boolean;
  isChecked: boolean;
  color: ColorProps;
}>`
  pointer-events: none;
  position: relative;
  border: 1px solid;
  border-radius: 50%;
  width: 16px;
  height: 16px;

  border-color: ${({ isDisabled }) =>
    isDisabled ? colorFn("ink", "lighter") : colorFn("ink", "light")};

  border-color: ${({ isChecked, color }) =>
    isChecked && theme.base.colors[color]};

  &:active,
  :focus {
    outline: none;
    box-shadow: 0 0 10px ${({ color }) => theme.base.colors[color]};
  }
`;

const StyledRadio = styled.span<{ color: ColorProps }>`
  pointer-events: none;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;

  background-color: ${({ color }) => color && theme.base.colors[color]};

  border: 1px solid ${({ color }) => color && theme.base.colors[color]};

  border-radius: 50%;

  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: 50% 50%;
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  transition: all 80ms ease-in-out;

  width: 8px;
  height: 8px;

  ${StyledInput}:checked ~& {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
`;

const StyledLabel = styled(Label)<{ isDisabled?: boolean }>`
  pointer-events: none;
  color: ${({ isDisabled }) =>
    isDisabled ? colorFn("ink", "lighter") : colorFn("ink")};
`;
