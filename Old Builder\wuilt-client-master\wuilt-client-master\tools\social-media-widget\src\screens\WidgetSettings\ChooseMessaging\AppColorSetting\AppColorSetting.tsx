import { Box, Select, Stack, InputGroup, ChevronDownIcon } from "@wuilt/quilt";
import React, { useState } from "react";
import { AppsEnum, TWidgetBackground } from "../../../../shared/types";
import ColorInput from "../ColorInput";
import { AppsDefaultColor } from "../../../../shared/IconsList";
import { components } from "react-select";

interface AppColorSettingProps {
  color: TWidgetBackground["color"];
  handleChangeColor: (color: any) => void;
  appName?: AppsEnum;
  defaultColor?: boolean;
}

function AppColorSetting({
  color,
  appName,
  defaultColor = true,
  handleChangeColor,
}: AppColorSettingProps) {
  const COLORS = [
    { label: "Default", value: AppsDefaultColor[`${appName}`] },
    { label: "Blue", value: "#2E90FA" },
    { label: "Green", value: "#16B364" },
    { label: "Red", value: "#F04438" },
    { label: "Orange", value: "#EF6820" },
    { label: "Purple", value: "#7A5AF8" },
    { label: "Pink", value: "#EE46BC" },
    { label: "Gray", value: "#1D2939" },
    { label: "Black", value: "#010101" },
  ];

  const [selectedColor, setSelectedColor] = useState(
    defaultColor ? COLORS[0] : COLORS[1]
  );
  const COLORS_OPTIONS = defaultColor ? COLORS : COLORS.slice(1);
  const [isCustomColor, setIsCustomColor] = useState(false);
  const customColor = { label: "Custom", value: color };

  const DropdownIndicator = (props) => {
    return (
      components.DropdownIndicator && (
        <components.DropdownIndicator {...props}>
          <ChevronDownIcon customColor="#667085" />
        </components.DropdownIndicator>
      )
    );
  };
  return (
    <Stack boxShadow="0px 1px 2px 0px #1018280D">
      <InputGroup borderRadius="8px">
        <Select
          components={{ DropdownIndicator }}
          styles={selectCustomStyles}
          isClearable={false}
          isSearchable={false}
          onChange={(color: any) => {
            setSelectedColor(color);
            setIsCustomColor(false);
            handleChangeColor(color?.value);
          }}
          formatOptionLabel={(option) => (
            <Stack direction="row" spacing="condensed" align="center">
              <Box
                border="1px solid #EAECF0"
                height="14px"
                width="14px"
                borderRadius="50%"
                style={{ background: option?.value }}
              ></Box>
              <Stack>{option?.label}</Stack>
            </Stack>
          )}
          options={COLORS_OPTIONS}
          value={isCustomColor ? customColor : selectedColor}
        />
        <ColorInput
          customStyle={{
            borderStartStartRadius: "0",
            borderEndStartRadius: "0",
          }}
          value={color || selectedColor.value}
          onChange={(color: string) => {
            setIsCustomColor(true);
            handleChangeColor(color);
          }}
        />
      </InputGroup>
    </Stack>
  );
}

export default AppColorSetting;

const selectCustomStyles = {
  control: (css) => {
    return {
      ...css,
      borderRadius: "8px",
    };
  },
  option: (css, { isFocused, isSelected }) => {
    let color = "#667085";
    if (isSelected) color = "#1D2939";
    else if (isFocused) color = "#1D2939";

    let backgroundColor;
    if (isSelected) backgroundColor = "#F9FAFB";
    else if (isFocused) backgroundColor = "#f9fafbb6";

    return {
      ...css,
      padding: "10px 12px",
      borderRadius: "5px",
      marginBottom: 6,
      backgroundColor,
      color,
      ":active": {
        color: "#1D2939",
        backgroundColor: "#F9FAFB",
      },
    };
  },
  menuList: (css) => ({
    ...css,
    padding: 6,
  }),
};
