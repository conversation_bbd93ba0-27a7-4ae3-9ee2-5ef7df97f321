# !/bin/bash
source ./scripts/select.sh

echo "Select the application you need to run:"
echo

APPS=("stores-admin" "app-shell" "account" "billing" "social-media-widget" "all")
select_option "${APPS[@]}"
app=$?

case "$app" in
	4)
    echo "Social Media Widget"
    yarn nx sync-accent social-media-widget
    ;;
	5)
    echo "Generating All"
    yarn nx run-many --target=sync-accent
    ;;
  *)
		yarn nx run ${APPS[$app]}:sync-accent
    ;;

esac
