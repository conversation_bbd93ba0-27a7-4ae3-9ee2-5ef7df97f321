import { palette } from "../../themes/original";

export default {
  primary: {
    background: palette.product.normal,
    colorText: palette.white.normal,
  },
  secondary: {
    background: palette.white.normalHover,
    colorText: palette.black.normal,
  },
  grey: {
    background: palette.cloud.dark,
    colorText: palette.ink.light,
  },
  success: {
    background: palette.product.light,
    colorText: palette.product.darker,
  },
  critical: {
    background: palette.product.critical,
    colorText: palette.white.normal,
  },
  warning: {
    background: palette.orange.light,
    colorText: palette.orange.darker,
  },
  failed: {
    background: palette.red.light,
    colorText: palette.red.darker,
  },
  white: {
    background: palette.white.normal,
    colorText: palette.product.dark,
  },
  ink: {
    background: palette.ink.normal,
    colorText: palette.white.normal,
  },
  orange: {
    background: palette.orange.light,
    colorText: palette.orange.normal,
  },
  pink: {
    background: palette.pink.light,
    colorText: palette.pink.darkHover,
  },
};
