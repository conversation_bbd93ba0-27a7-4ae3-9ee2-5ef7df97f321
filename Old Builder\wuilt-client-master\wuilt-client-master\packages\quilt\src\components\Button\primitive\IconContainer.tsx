import React from "react";
import styled, { css } from "styled-components";

export const StyledIconContainer = styled(({ className, children }) => (
  <div className={className}>{children}</div>
))`
  ${({ margin, width, height }) => css`
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin: ${margin};
    color: currentColor;
    > svg {
      width: ${width};
      height: ${height};
    }
  `}
`;

const IconContainer = ({ margin, width, height, children }) => (
  <StyledIconContainer margin={margin} width={width} height={height}>
    {children}
  </StyledIconContainer>
);

export default IconContainer;
