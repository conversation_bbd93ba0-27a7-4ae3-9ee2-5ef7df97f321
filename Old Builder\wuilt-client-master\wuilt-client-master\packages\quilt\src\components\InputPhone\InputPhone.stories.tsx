import React, { useState } from "react";
import { Stack } from "../Stack";
import { InputPhone, TPhoneInputValue } from "./InputPhone";
import { Field, Form, FormSubmit } from "../Form";

import "react-phone-number-input/style.css";

export default {
  title: "Components/Form/InputPhone ",
  component: InputPhone,
};

export const Playground = () => {
  const [value, setValue] = useState<TPhoneInputValue>({
    value: "010 1234 11 22",
    isValid: true,
  });

  return (
    <Stack direction="column">
      <InputPhone value={value} onChange={setValue} />

      {
        // eslint-disable-next-line no-console
        <Form<{ number: number }> onSubmit={(data) => console.log(data)}>
          {({ formProps }) => (
            <form {...formProps}>
              <Field<TPhoneInputValue> name="phone">
                {({ fieldProps }) => <InputPhone {...fieldProps} />}
              </Field>
              <FormSubmit>Submit</FormSubmit>
            </form>
          )}
        </Form>
      }
    </Stack>
  );
};
