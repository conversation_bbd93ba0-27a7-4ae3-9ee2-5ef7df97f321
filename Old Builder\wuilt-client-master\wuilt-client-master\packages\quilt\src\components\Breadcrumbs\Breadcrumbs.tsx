import React from "react";
import styled from "styled-components";
import cuid from "cuid";
import { ChevronLeftIcon, ChevronRightIcon } from "../icons";
import { Stack } from "../Stack";
import getSpacingToken from "../../common/getSpacingToken";
import { SpaceAfterProps } from "../../common/spaceAfter";
import { Global } from "../../common/types";
import { UnstyledLink } from "../UnstyledLink";

export interface CallbackAction extends Global {
  /** A unique identifier for the action */
  id?: string;
  /** Content the action displays */
  content?: string;
  /** Visually hidden text for screen readers */
  accessibilityLabel?: string;
  /** Callback when an action takes place */
  onAction?(): void;
}

export interface LinkComponentAction extends Global {
  id?: string;
  content?: React.ReactNode;
  url?: string;
}

export interface BreadcrumbsProps extends SpaceAfterProps {
  breadcrumbs: (CallbackAction | LinkComponentAction)[];
  linkComponent?: any;
  urlProp?: "to" | "href";
  type?: "back-button" | "breadcrumb";
  className?: string;
  onClick?: () => void;
}

export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
  breadcrumbs,
  linkComponent: LinkComponent = UnstyledLink,
  urlProp = "to",
  type = "breadcrumb",
  className,
  spaceAfter = "none",
  onClick,
}) => {
  const markup = breadcrumbs
    .filter((breadcrumb) => breadcrumb != undefined)
    .map((breadcrumb, index) => {
      const contentMarkup = (
        <Stack direction="row" align="center" spacing="condensed" inline>
          {type === "back-button" && <ChevronLeftIcon reverseOnRtl size="lg" />}
          <span>{breadcrumb.content}</span>
          {type === "breadcrumb" && index !== breadcrumbs.length - 1 && (
            <ChevronRightIcon reverseOnRtl size="lg" />
          )}
        </Stack>
      );

      return "url" in breadcrumb ? (
        <StyledBreadcrumbItem
          data-test={breadcrumb?.dataTest}
          key={index}
          asComponent={LinkComponent}
          {...{
            [urlProp]: breadcrumb.url,
          }}
        >
          {contentMarkup}
        </StyledBreadcrumbItem>
      ) : (
        <StyledBreadcrumbItem
          data-test={breadcrumb?.dataTest}
          onClick={onClick}
          key={index}
        >
          {contentMarkup}
        </StyledBreadcrumbItem>
      );
    });

  return (
    <StyledNav className={className} role="navigation" spaceAfter={spaceAfter}>
      {markup}
    </StyledNav>
  );
};

Breadcrumbs.displayName = "Breadcrumbs";

/**
 *
 * Styles
 *
 */

const StyledNav = styled.nav<{ spaceAfter }>`
  margin-bottom: ${({ spaceAfter, theme }) =>
    getSpacingToken({ spaceAfter, theme })};
`;

const StyledBreadcrumbItem = styled(
  ({ asComponent: Component = "button", ...rest }) => <Component {...rest} />
)`
  font-weight: ${({ theme }) => theme.base.fontWeight.semiBold};
  font-size: ${({ theme }) => theme.base.fontSize.sm};
  color: ${({ theme }) => theme.palette.ink.light};
  text-decoration: none;

  cursor: pointer;

  &:hover,
  &:focus {
    outline: none;
    text-decoration: none;
  }
  &:hover {
    color: ${({ theme }) => theme.palette.ink.lightHover};
  }
  :focus {
    color: ${({ theme }) => theme.palette.ink.inkActive};
  }
`;
