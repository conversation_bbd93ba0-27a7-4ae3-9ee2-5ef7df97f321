import React from "react";
import { base } from "../themes/original";
import { DataTable } from "../components/DataTable";
import { Box } from "../components/Box";
import { Text } from "../components/Text";

function Display({ fontWeight }) {
  return <Text fontWeight={fontWeight}>Quilt is design system for Wuilt</Text>;
}

export const FontWeights = () => {
  return (
    <Box margin="0 auto" maxWidth="1000px">
      <DataTable
        header={{
          cells: [
            { content: "Font Weight" },
            { content: "Value" },
            { content: "Display" },
          ],
        }}
        rows={Object.keys(base.fontWeight).map((f) => ({
          cells: [
            { content: f },
            { content: base.fontWeight[f] },
            { content: <Display fontWeight={f} /> },
          ],
        }))}
      />
    </Box>
  );
};

FontWeights.title = "Intro/Guide";

FontWeights.story = {
  name: "Font Weights",
};

export default FontWeights;
